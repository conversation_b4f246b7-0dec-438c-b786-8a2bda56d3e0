// Security and Access Control System for The Wolf Challenge
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import {
  doc,
  getDoc,
  updateDoc,
  serverTimestamp
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class SecurityManager {
  constructor() {
    // Core security state
    this.sessionStartTime = Date.now();
    this.lastActivity = Date.now();
    this.isInitialized = false;

    // Security tracking maps
    this.failedAttempts = new Map();
    this.rateLimitMap = new Map();
    this.securityEvents = [];

    // Configuration
    this.config = {
      session: {
        maxDuration: 8 * 60 * 60 * 1000, // 8 hours
        maxInactivity: 2 * 60 * 60 * 1000, // 2 hours
        checkInterval: 60 * 1000 // 1 minute
      },
      rateLimit: {
        maxAttempts: 5,
        windowMs: 60 * 1000,
        blockDurationMs: 5 * 60 * 1000
      },
      security: {
        maxSecurityEvents: 100,
        suspiciousSubmissionThreshold: 10,
        suspiciousTimeWindow: 10 * 1000
      }
    };

    this.initializeSecurity();
  }

  initializeSecurity() {
    if (this.isInitialized) return;

    try {
      // Initialize security components in order
      this.setupCSRFProtection();
      this.setupSessionManagement();
      this.setupRateLimiting();
      this.setupActivityTracking();
      this.setupSecurityHeaders();
      this.loadExistingSession();

      this.isInitialized = true;
      console.log('🐺 Security system initialized successfully');
    } catch (error) {
      console.error('🐺 Security initialization failed:', error);
      throw new Error('Security system initialization failed');
    }
  }

  // CSRF Protection System
  setupCSRFProtection() {
    try {
      this.csrfToken = this.generateCSRFToken();

      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.initializeCSRFHandlers());
      } else {
        this.initializeCSRFHandlers();
      }

      console.log('🐺 CSRF protection enabled');
    } catch (error) {
      console.error('🐺 CSRF protection setup failed:', error);
      throw error;
    }
  }

  initializeCSRFHandlers() {
    // Add CSRF tokens to existing forms
    this.addCSRFTokenToForms();

    // Set up form submission validation
    this.setupFormValidation();

    // Monitor for dynamically added forms
    this.observeFormChanges();
  }

  generateCSRFToken() {
    if (!window.crypto || !window.crypto.getRandomValues) {
      // Fallback for older browsers
      return 'csrf_' + Math.random().toString(36).substring(2, 34) + Date.now().toString(36);
    }

    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  addCSRFTokenToForms() {
    const forms = document.querySelectorAll('form:not([data-csrf-protected])');
    let tokensAdded = 0;

    forms.forEach(form => {
      try {
        if (!form.querySelector('input[name="csrf_token"]')) {
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = 'csrf_token';
          csrfInput.value = this.csrfToken;
          form.appendChild(csrfInput);
          form.setAttribute('data-csrf-protected', 'true');
          tokensAdded++;
        }
      } catch (error) {
        console.warn('🐺 Failed to add CSRF token to form:', error);
      }
    });

    if (tokensAdded > 0) {
      console.log(`🐺 Added CSRF tokens to ${tokensAdded} forms`);
    }
  }

  setupFormValidation() {
    // Skip form validation in development mode
    if (this.isDevelopmentMode()) {
      console.log('🐺 Development mode: Form validation disabled');
      return;
    }

    document.addEventListener('submit', (e) => {
      // Only validate forms that explicitly need CSRF protection
      if (e.target.hasAttribute('data-csrf-required')) {
        if (!this.validateFormSecurity(e.target)) {
          e.preventDefault();
          e.stopPropagation();
          this.handleSecurityViolation('Form security validation failed');
          return false;
        }
      }
    }, true); // Use capture phase for better control
  }

  isDevelopmentMode() {
    return window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1' ||
           window.location.protocol === 'file:';
  }

  validateFormSecurity(form) {
    try {
      // Skip validation for forms that don't need CSRF protection
      if (form.hasAttribute('data-no-csrf')) {
        return true;
      }

      // For login form and other critical forms, validate CSRF token
      if (form.id === 'login-form' || form.hasAttribute('data-csrf-required')) {
        if (!this.validateCSRFToken(form)) {
          this.logSecurityEvent({
            type: 'CSRF_VALIDATION_FAILED',
            form: form.id || form.className,
            timestamp: Date.now()
          });
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('🐺 Form validation error:', error);
      // Don't block form submission on validation errors
      return true;
    }
  }

  validateCSRFToken(form) {
    const csrfInput = form.querySelector('input[name="csrf_token"]');
    return csrfInput && csrfInput.value === this.csrfToken;
  }

  observeFormChanges() {
    if (!window.MutationObserver) return;

    const observer = new MutationObserver((mutations) => {
      let formsAdded = false;

      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.tagName === 'FORM' || node.querySelector('form')) {
              formsAdded = true;
            }
          }
        });
      });

      if (formsAdded) {
        // Debounce form token addition
        clearTimeout(this.formObserverTimeout);
        this.formObserverTimeout = setTimeout(() => {
          this.addCSRFTokenToForms();
        }, 100);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.formObserver = observer;
  }

  // Session Management System
  setupSessionManagement() {
    try {
      // Initialize session monitoring
      this.startSessionMonitoring();

      // Set up activity tracking
      this.setupActivityListeners();

      // Handle page lifecycle events
      this.setupPageLifecycleHandlers();

      console.log('🐺 Session management enabled');
    } catch (error) {
      console.error('🐺 Session management setup failed:', error);
      throw error;
    }
  }

  startSessionMonitoring() {
    // Clear any existing interval
    if (this.sessionInterval) {
      clearInterval(this.sessionInterval);
    }

    // Start session validity checks
    this.sessionInterval = setInterval(() => {
      this.checkSessionValidity();
    }, this.config.session.checkInterval);
  }

  setupActivityListeners() {
    // Throttled activity update to prevent excessive calls
    const throttledUpdate = this.throttle(() => {
      this.updateLastActivity();
    }, 30000); // Update at most once per 30 seconds

    // Activity events
    const activityEvents = ['click', 'keypress', 'scroll', 'mousemove', 'touchstart'];

    activityEvents.forEach(eventType => {
      document.addEventListener(eventType, throttledUpdate, {
        passive: true,
        capture: false
      });
    });

    // Page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.updateLastActivity();
        this.checkSessionValidity(); // Check immediately when page becomes visible
      }
    });
  }

  setupPageLifecycleHandlers() {
    // Save session data before page unload
    window.addEventListener('beforeunload', () => {
      this.saveSessionData();
    });

    // Handle page focus/blur
    window.addEventListener('focus', () => {
      this.updateLastActivity();
    });

    // Handle online/offline status
    window.addEventListener('online', () => {
      console.log('🐺 Connection restored');
      this.updateLastActivity();
    });

    window.addEventListener('offline', () => {
      console.log('🐺 Connection lost');
      this.logSecurityEvent({
        type: 'CONNECTION_LOST',
        timestamp: Date.now()
      });
    });
  }

  checkSessionValidity() {
    if (!this.isInitialized) return;

    const now = Date.now();
    const sessionDuration = now - this.sessionStartTime;
    const inactivityDuration = now - this.lastActivity;

    // Check session limits
    if (sessionDuration > this.config.session.maxDuration) {
      this.handleSessionExpiry('Maximum session duration exceeded');
      return false;
    }

    if (inactivityDuration > this.config.session.maxInactivity) {
      this.handleSessionExpiry('Session expired due to inactivity');
      return false;
    }

    // Warn user when session is about to expire
    const warningThreshold = this.config.session.maxInactivity - (10 * 60 * 1000); // 10 minutes before expiry
    if (inactivityDuration > warningThreshold && !this.sessionWarningShown) {
      this.showSessionWarning();
    }

    return true;
  }

  showSessionWarning() {
    this.sessionWarningShown = true;

    const warningDiv = document.createElement('div');
    warningDiv.className = 'fixed top-4 right-4 bg-yellow-500 text-black p-4 rounded shadow-lg z-50';
    warningDiv.innerHTML = `
      <div class="flex items-center space-x-2">
        <i class="fas fa-exclamation-triangle"></i>
        <span class="font-bold">Session Warning</span>
      </div>
      <p class="text-sm mt-1">Your session will expire soon due to inactivity.</p>
      <button onclick="this.parentElement.remove(); securityManager.updateLastActivity();"
              class="mt-2 bg-black text-yellow-500 px-3 py-1 text-sm font-bold">
        STAY ACTIVE
      </button>
    `;

    document.body.appendChild(warningDiv);

    // Auto-remove warning after 30 seconds
    setTimeout(() => {
      if (warningDiv.parentElement) {
        warningDiv.parentElement.removeChild(warningDiv);
      }
    }, 30000);
  }

  handleSessionExpiry(reason) {
    console.log('🐺 Session expired:', reason);

    this.logSecurityEvent({
      type: 'SESSION_EXPIRED',
      reason: reason,
      sessionDuration: Date.now() - this.sessionStartTime,
      timestamp: Date.now()
    });

    // Clear session interval
    if (this.sessionInterval) {
      clearInterval(this.sessionInterval);
    }

    // Show user-friendly message
    this.showSessionExpiredModal(reason);
  }

  showSessionExpiredModal(reason) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-8 max-w-md mx-4 text-center">
        <div class="text-4xl mb-4">⏰</div>
        <h3 class="text-2xl font-bold mb-4">Session Expired</h3>
        <p class="text-gray-600 mb-6">${this.getSessionExpiryMessage(reason)}</p>
        <button onclick="authManager.handleLogout()"
                class="neo-brutalist bg-red-500 text-white px-6 py-3 font-bold">
          LOGIN AGAIN
        </button>
      </div>
    `;

    document.body.appendChild(modal);
  }

  getSessionExpiryMessage(reason) {
    switch (reason) {
      case 'Maximum session duration exceeded':
        return 'Your session has reached the maximum duration limit for security purposes.';
      case 'Session expired due to inactivity':
        return 'Your session has expired due to inactivity. Please log in again to continue.';
      default:
        return 'Your session has expired. Please log in again to continue.';
    }
  }

  updateLastActivity() {
    const now = Date.now();
    this.lastActivity = now;
    this.sessionWarningShown = false; // Reset warning flag

    // Debounced database update
    this.debounceUserActivityUpdate();
  }

  debounceUserActivityUpdate() {
    // Clear existing timeout
    if (this.activityUpdateTimeout) {
      clearTimeout(this.activityUpdateTimeout);
    }

    // Set new timeout for database update
    this.activityUpdateTimeout = setTimeout(() => {
      const user = authManager.getCurrentUser();
      if (user) {
        this.updateUserActivityInDB(user.uid);
      }
    }, 60000); // Update database at most once per minute
  }

  async updateUserActivityInDB(userId) {
    try {
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);
      await updateDoc(userRef, {
        lastActivity: serverTimestamp()
      });
    } catch (error) {
      console.error('🐺 Error updating user activity:', error);
      // Don't throw error as this is not critical for user experience
    }
  }

  // Utility function for throttling
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Rate Limiting System
  setupRateLimiting() {
    try {
      // Initialize rate limiting cleanup
      this.startRateLimitCleanup();

      console.log('🐺 Rate limiting enabled');
    } catch (error) {
      console.error('🐺 Rate limiting setup failed:', error);
      throw error;
    }
  }

  startRateLimitCleanup() {
    // Clean up expired rate limit entries every 5 minutes
    this.rateLimitCleanupInterval = setInterval(() => {
      this.cleanupExpiredRateLimits();
    }, 5 * 60 * 1000);
  }

  cleanupExpiredRateLimits() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, data] of this.rateLimitMap.entries()) {
      // Remove entries that are no longer blocked and outside the window
      if (data.blockedUntil < now &&
          (now - data.windowStart) > this.config.rateLimit.windowMs * 2) {
        this.rateLimitMap.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🐺 Cleaned up ${cleanedCount} expired rate limit entries`);
    }
  }

  checkRateLimit(challengeId, userId = null) {
    if (!challengeId) {
      throw new Error('Challenge ID is required for rate limiting');
    }

    const currentUserId = userId || authManager.getCurrentUser()?.uid;
    if (!currentUserId) {
      throw new Error('User must be authenticated for rate limiting');
    }

    const now = Date.now();
    const key = `${currentUserId}_${challengeId}`;

    // Initialize rate limit data if not exists
    if (!this.rateLimitMap.has(key)) {
      this.rateLimitMap.set(key, {
        attempts: 0,
        windowStart: now,
        blockedUntil: 0,
        firstAttempt: now
      });
    }

    const rateLimitData = this.rateLimitMap.get(key);

    // Check if currently blocked
    if (rateLimitData.blockedUntil > now) {
      const remainingTime = Math.ceil((rateLimitData.blockedUntil - now) / 1000);

      this.logSecurityEvent({
        type: 'RATE_LIMIT_BLOCKED',
        challengeId: challengeId,
        userId: currentUserId,
        remainingTime: remainingTime,
        timestamp: now
      });

      throw new Error(`Rate limited. Try again in ${remainingTime} seconds.`);
    }

    // Reset window if expired
    if (now - rateLimitData.windowStart > this.config.rateLimit.windowMs) {
      rateLimitData.attempts = 0;
      rateLimitData.windowStart = now;
    }

    // Check if exceeded rate limit
    if (rateLimitData.attempts >= this.config.rateLimit.maxAttempts) {
      rateLimitData.blockedUntil = now + this.config.rateLimit.blockDurationMs;

      this.handleSecurityViolation(`Rate limit exceeded for challenge ${challengeId}`);

      const blockDurationMinutes = Math.ceil(this.config.rateLimit.blockDurationMs / 60000);
      throw new Error(`Too many attempts. Please wait ${blockDurationMinutes} minutes before trying again.`);
    }

    // Increment attempts
    rateLimitData.attempts++;

    // Log rate limit status
    this.logSecurityEvent({
      type: 'RATE_LIMIT_CHECK',
      challengeId: challengeId,
      userId: currentUserId,
      attempts: rateLimitData.attempts,
      maxAttempts: this.config.rateLimit.maxAttempts,
      timestamp: now
    });

    return {
      allowed: true,
      attemptsRemaining: this.config.rateLimit.maxAttempts - rateLimitData.attempts,
      windowResetTime: rateLimitData.windowStart + this.config.rateLimit.windowMs
    };
  }

  getRateLimitStatus(challengeId, userId = null) {
    const currentUserId = userId || authManager.getCurrentUser()?.uid;
    if (!currentUserId) return null;

    const key = `${currentUserId}_${challengeId}`;
    const rateLimitData = this.rateLimitMap.get(key);

    if (!rateLimitData) {
      return {
        attempts: 0,
        maxAttempts: this.config.rateLimit.maxAttempts,
        blocked: false,
        resetTime: null
      };
    }

    const now = Date.now();
    const isBlocked = rateLimitData.blockedUntil > now;

    return {
      attempts: rateLimitData.attempts,
      maxAttempts: this.config.rateLimit.maxAttempts,
      blocked: isBlocked,
      resetTime: isBlocked ? rateLimitData.blockedUntil : rateLimitData.windowStart + this.config.rateLimit.windowMs,
      remainingTime: isBlocked ? Math.ceil((rateLimitData.blockedUntil - now) / 1000) : 0
    };
  }

  setupActivityTracking() {
    // Track user interactions
    const events = ['click', 'keypress', 'scroll', 'mousemove'];
    
    events.forEach(eventType => {
      document.addEventListener(eventType, () => {
        this.updateLastActivity();
      }, { passive: true });
    });
  }

  setupSecurityHeaders() {
    // Add security-related meta tags if not present
    this.addSecurityMetaTags();
    
    // Monitor for suspicious activities
    this.setupSuspiciousActivityDetection();
  }

  addSecurityMetaTags() {
    const securityTags = [
      { name: 'X-Content-Type-Options', content: 'nosniff' },
      { name: 'X-Frame-Options', content: 'DENY' },
      { name: 'X-XSS-Protection', content: '1; mode=block' },
      { name: 'Referrer-Policy', content: 'strict-origin-when-cross-origin' }
    ];

    securityTags.forEach(tag => {
      if (!document.querySelector(`meta[name="${tag.name}"]`)) {
        const meta = document.createElement('meta');
        meta.name = tag.name;
        meta.content = tag.content;
        document.head.appendChild(meta);
      }
    });
  }

  setupSuspiciousActivityDetection() {
    // Detect rapid-fire submissions
    let submissionTimes = [];
    
    document.addEventListener('submit', () => {
      const now = Date.now();
      submissionTimes.push(now);
      
      // Keep only submissions from last 10 seconds
      submissionTimes = submissionTimes.filter(time => now - time < 10000);
      
      // If more than 10 submissions in 10 seconds, flag as suspicious
      if (submissionTimes.length > 10) {
        this.handleSecurityViolation('Suspicious rapid submissions detected');
      }
    });

    // Detect developer tools usage (basic detection)
    this.detectDevTools();
  }

  detectDevTools() {
    let devtools = { open: false, orientation: null };
    
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || 
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools.open) {
          devtools.open = true;
          console.log('🐺 Developer tools detected - this is expected for CTF challenges!');
        }
      } else {
        devtools.open = false;
      }
    }, 500);
  }

  // Challenge Security System
  async validateChallengeAccess(challengeId) {
    if (!challengeId) {
      throw new Error('Challenge ID is required');
    }

    const user = authManager.getCurrentUser();
    if (!user) {
      throw new Error('User must be authenticated to access challenges');
    }

    try {
      // Check if challenge was already solved (one-time access rule)
      await this.checkChallengeNotSolved(challengeId);

      // Log access attempt
      this.logSecurityEvent({
        type: 'CHALLENGE_ACCESS',
        challengeId: challengeId,
        userId: user.uid,
        timestamp: Date.now()
      });

      return true;
    } catch (error) {
      this.logSecurityEvent({
        type: 'CHALLENGE_ACCESS_DENIED',
        challengeId: challengeId,
        userId: user.uid,
        reason: error.message,
        timestamp: Date.now()
      });
      throw error;
    }
  }

  async checkChallengeNotSolved(challengeId) {
    try {
      const user = authManager.getCurrentUser();
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const userData = userSnap.data();
        const solvedChallenges = userData.solvedChallenges || [];

        if (solvedChallenges.includes(challengeId)) {
          throw new Error('Challenge already solved. One-time access rule enforced.');
        }
      }

      return true;
    } catch (error) {
      console.error('🐺 Error checking challenge access:', error);
      throw error;
    }
  }

  // Flag Submission Security
  validateFlagSubmission(challengeId, flag) {
    if (!challengeId || !flag) {
      throw new Error('Challenge ID and flag are required');
    }

    try {
      // Pre-submission security checks
      this.performPreSubmissionChecks(challengeId, flag);

      // Check rate limiting
      const rateLimitResult = this.checkRateLimit(challengeId);

      // Validate flag format and content
      this.validateFlagSecurity(flag);

      // Log successful validation
      this.logSecurityEvent({
        type: 'FLAG_VALIDATION_SUCCESS',
        challengeId: challengeId,
        userId: authManager.getCurrentUser()?.uid,
        attemptsRemaining: rateLimitResult.attemptsRemaining,
        timestamp: Date.now()
      });

      return {
        valid: true,
        rateLimitStatus: rateLimitResult
      };
    } catch (error) {
      // Record failed attempt
      this.recordFailedAttempt(challengeId, flag, error.message);
      throw error;
    }
  }

  performPreSubmissionChecks(challengeId, flag) {
    // Check for suspicious rapid submissions
    this.checkSubmissionPattern(challengeId);

    // Validate user session
    if (!this.checkSessionValidity()) {
      throw new Error('Session invalid. Please refresh and try again.');
    }

    // Check for automated submissions
    if (this.detectAutomatedSubmission()) {
      throw new Error('Automated submissions detected. Please solve challenges manually.');
    }

    // Basic flag pre-validation
    if (!flag || typeof flag !== 'string') {
      throw new Error('Invalid flag format provided');
    }
  }

  checkSubmissionPattern(challengeId) {
    const userId = authManager.getCurrentUser()?.uid;
    const key = `${userId}_${challengeId}`;
    const now = Date.now();

    if (!this.submissionTimes) {
      this.submissionTimes = new Map();
    }

    if (!this.submissionTimes.has(key)) {
      this.submissionTimes.set(key, []);
    }

    const submissions = this.submissionTimes.get(key);

    // Add current submission time
    submissions.push(now);

    // Keep only submissions from last 10 seconds
    const recentSubmissions = submissions.filter(time =>
      now - time < this.config.security.suspiciousTimeWindow
    );

    this.submissionTimes.set(key, recentSubmissions);

    // Check for suspicious pattern
    if (recentSubmissions.length > this.config.security.suspiciousSubmissionThreshold) {
      this.handleSecurityViolation(`Suspicious rapid submissions detected for challenge ${challengeId}`);
      throw new Error('Too many rapid submissions detected. Please slow down.');
    }
  }

  detectAutomatedSubmission() {
    // Simple heuristics to detect automated submissions
    const now = Date.now();

    // Check if user has been active recently (mouse/keyboard activity)
    if (now - this.lastActivity > 30000) { // 30 seconds of inactivity
      return true;
    }

    // Check for consistent timing patterns (would need more sophisticated detection)
    return false;
  }

  validateFlagSecurity(flag) {
    // Validate flag format
    if (!this.validateFlagFormat(flag)) {
      throw new Error('Invalid flag format. Flags should be in format: wolf{...}');
    }

    // Check flag length (reasonable bounds)
    if (flag.length < 6 || flag.length > 100) {
      throw new Error('Flag length is invalid');
    }

    // Check for bypass attempts
    if (this.detectBypassAttempt(flag)) {
      this.handleSecurityViolation(`Bypass attempt detected: ${this.sanitizeForLogging(flag)}`);
      throw new Error('Invalid flag content detected');
    }

    // Check for common attack patterns
    if (this.detectAttackPatterns(flag)) {
      this.handleSecurityViolation(`Attack pattern detected: ${this.sanitizeForLogging(flag)}`);
      throw new Error('Malicious content detected in flag');
    }
  }

  validateFlagFormat(flag) {
    // Enhanced flag format validation
    const flagPattern = /^wolf\{[a-zA-Z0-9_\-\{\}\.\!\?\@\#\$\%\^\&\*\(\)\+\=\[\]\;\:\'\"\,\<\>\/\\\|]+\}$/;
    return flagPattern.test(flag) && flag.includes('{') && flag.includes('}');
  }

  detectBypassAttempt(flag) {
    const bypassPatterns = [
      /<script/i,
      /<\/script>/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /\.\.\//,
      /\.\.\\\\/,
      /eval\s*\(/i,
      /function\s*\(/i,
      /alert\s*\(/i,
      /document\./i,
      /window\./i
    ];

    return bypassPatterns.some(pattern => pattern.test(flag));
  }

  detectAttackPatterns(flag) {
    const attackPatterns = [
      // SQL Injection
      /union\s+select/i,
      /drop\s+table/i,
      /delete\s+from/i,
      /insert\s+into/i,
      /update\s+set/i,

      // Command Injection
      /;\s*rm\s+/i,
      /;\s*cat\s+/i,
      /;\s*ls\s+/i,
      /\|\s*nc\s+/i,

      // Path Traversal
      /\.\.\/\.\.\/\.\.\//,
      /\.\.\\\\\.\.\\\\\.\.\\\\/,

      // Template Injection
      /\{\{.*\}\}/,
      /\$\{.*\}/,
      /%\{.*\}/
    ];

    return attackPatterns.some(pattern => pattern.test(flag));
  }

  recordFailedAttempt(challengeId, flag, reason) {
    const userId = authManager.getCurrentUser()?.uid;
    const key = `${userId}_${challengeId}`;
    const now = Date.now();

    if (!this.failedAttempts.has(key)) {
      this.failedAttempts.set(key, []);
    }

    const attempts = this.failedAttempts.get(key);
    attempts.push({
      flag: this.sanitizeForLogging(flag),
      reason: reason,
      timestamp: now,
      userAgent: navigator.userAgent.substring(0, 200), // Limit length
      sessionId: this.generateSessionId()
    });

    // Keep only last 50 attempts per challenge
    if (attempts.length > 50) {
      attempts.splice(0, attempts.length - 50);
    }

    // Log security event
    this.logSecurityEvent({
      type: 'FLAG_SUBMISSION_FAILED',
      challengeId: challengeId,
      userId: userId,
      reason: reason,
      timestamp: now
    });

    console.warn('🐺 Failed flag attempt:', {
      challengeId,
      reason,
      timestamp: new Date(now).toISOString()
    });
  }

  sanitizeForLogging(input) {
    // Sanitize sensitive data for logging
    return input.substring(0, 50).replace(/[<>'"&]/g, '?');
  }

  generateSessionId() {
    return this.sessionStartTime.toString(36) + Math.random().toString(36).substring(2, 8);
  }

  // Security Event Handling
  handleSecurityViolation(violation) {
    console.error('🐺 Security violation detected:', violation);

    const event = {
      type: 'SECURITY_VIOLATION',
      description: violation,
      userId: authManager.getCurrentUser()?.uid,
      timestamp: Date.now(),
      userAgent: navigator.userAgent.substring(0, 200),
      url: window.location.href,
      severity: this.assessViolationSeverity(violation)
    };

    this.logSecurityEvent(event);

    // Take action based on severity
    this.handleViolationResponse(event);
  }

  assessViolationSeverity(violation) {
    const highSeverityPatterns = [
      /bypass attempt/i,
      /attack pattern/i,
      /malicious content/i,
      /automated/i
    ];

    const mediumSeverityPatterns = [
      /rate limit/i,
      /suspicious/i,
      /rapid submissions/i
    ];

    const lowSeverityPatterns = [
      /form security validation/i,
      /csrf/i,
      /validation failed/i
    ];

    if (highSeverityPatterns.some(pattern => pattern.test(violation))) {
      return 'HIGH';
    } else if (mediumSeverityPatterns.some(pattern => pattern.test(violation))) {
      return 'MEDIUM';
    } else if (lowSeverityPatterns.some(pattern => pattern.test(violation))) {
      return 'LOW';
    }

    return 'LOW';
  }

  handleViolationResponse(event) {
    switch (event.severity) {
      case 'HIGH':
        this.handleHighSeverityViolation(event);
        break;
      case 'MEDIUM':
        this.handleMediumSeverityViolation(event);
        break;
      default:
        // Low severity - just log
        break;
    }
  }

  handleHighSeverityViolation(event) {
    // For high severity violations, implement stricter measures
    console.warn('🐺 High severity security violation detected');

    // Could implement temporary restrictions
    this.showSecurityWarning('Suspicious activity detected. Your actions are being monitored.');

    // Increase rate limiting for this user
    this.applyStrictRateLimit(event.userId);
  }

  handleMediumSeverityViolation(event) {
    // For medium severity, apply moderate restrictions
    console.warn('🐺 Medium severity security violation detected');

    // Could implement warnings or temporary slowdowns
    if (event.userId) {
      // Log for tracking patterns
      console.log(`🐺 Medium severity violation for user: ${event.userId}`);
    }
  }

  showSecurityWarning(message) {
    const warningDiv = document.createElement('div');
    warningDiv.className = 'fixed top-4 left-4 bg-red-500 text-white p-4 rounded shadow-lg z-50 max-w-md';
    warningDiv.innerHTML = `
      <div class="flex items-center space-x-2">
        <i class="fas fa-shield-alt"></i>
        <span class="font-bold">Security Alert</span>
      </div>
      <p class="text-sm mt-1">${message}</p>
      <button onclick="this.parentElement.remove()"
              class="mt-2 bg-white text-red-500 px-3 py-1 text-sm font-bold rounded">
        UNDERSTOOD
      </button>
    `;

    document.body.appendChild(warningDiv);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (warningDiv.parentElement) {
        warningDiv.parentElement.removeChild(warningDiv);
      }
    }, 10000);
  }

  applyStrictRateLimit(userId) {
    // Apply stricter rate limiting for flagged users
    const strictConfig = {
      maxAttempts: 2,
      windowMs: 2 * 60 * 1000, // 2 minutes
      blockDurationMs: 10 * 60 * 1000 // 10 minutes
    };

    // Store strict rate limit config for this user
    this.strictRateLimits = this.strictRateLimits || new Map();
    this.strictRateLimits.set(userId, {
      config: strictConfig,
      appliedAt: Date.now(),
      expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
    });
  }

  // Enhanced logging system
  logSecurityEvent(event) {
    try {
      // Add to in-memory events
      this.securityEvents.push(event);

      // Keep only recent events in memory
      if (this.securityEvents.length > this.config.security.maxSecurityEvents) {
        this.securityEvents.splice(0, this.securityEvents.length - this.config.security.maxSecurityEvents);
      }

      // Store in localStorage with rotation
      this.persistSecurityEvent(event);

      // In production, this would send to a security monitoring service
      console.log('🐺 Security Event:', {
        type: event.type,
        severity: event.severity || 'LOW',
        timestamp: new Date(event.timestamp).toISOString()
      });

    } catch (error) {
      console.error('🐺 Failed to log security event:', error);
    }
  }

  persistSecurityEvent(event) {
    try {
      const securityLog = JSON.parse(localStorage.getItem('ctf_security_log') || '[]');
      securityLog.push(event);

      // Keep only last 200 events in localStorage
      if (securityLog.length > 200) {
        securityLog.splice(0, securityLog.length - 200);
      }

      localStorage.setItem('ctf_security_log', JSON.stringify(securityLog));
    } catch (error) {
      console.error('🐺 Failed to persist security event:', error);
    }
  }

  getSecurityEvents(limit = 50) {
    return this.securityEvents.slice(-limit);
  }

  // Session Management
  saveSessionData() {
    try {
      const sessionData = {
        sessionStartTime: this.sessionStartTime,
        lastActivity: this.lastActivity,
        csrfToken: this.csrfToken,
        version: '1.0'
      };

      sessionStorage.setItem('ctf_session', JSON.stringify(sessionData));
    } catch (error) {
      console.error('🐺 Failed to save session data:', error);
    }
  }

  loadExistingSession() {
    try {
      const sessionData = sessionStorage.getItem('ctf_session');
      if (sessionData) {
        const data = JSON.parse(sessionData);

        // Validate session data
        if (data.version === '1.0' && data.sessionStartTime && data.csrfToken) {
          this.sessionStartTime = data.sessionStartTime;
          this.lastActivity = data.lastActivity || Date.now();
          this.csrfToken = data.csrfToken;

          console.log('🐺 Restored existing session');
        } else {
          console.log('🐺 Invalid session data, creating new session');
          this.csrfToken = this.generateCSRFToken();
        }
      } else {
        this.csrfToken = this.generateCSRFToken();
      }
    } catch (error) {
      console.error('🐺 Failed to load session data:', error);
      this.csrfToken = this.generateCSRFToken();
    }
  }

  // Utility Methods
  sanitizeInput(input) {
    if (typeof input !== 'string') return '';

    const div = document.createElement('div');
    div.textContent = input;
    return div.innerHTML;
  }

  generateSecureId() {
    if (window.crypto && window.crypto.getRandomValues) {
      return crypto.getRandomValues(new Uint32Array(1))[0].toString(16);
    }
    return Math.random().toString(16).substring(2);
  }

  // System cleanup and maintenance
  performMaintenance() {
    try {
      this.cleanupExpiredRateLimits();
      this.cleanupOldFailedAttempts();
      this.cleanupStrictRateLimits();

      console.log('🐺 Security system maintenance completed');
    } catch (error) {
      console.error('🐺 Security maintenance failed:', error);
    }
  }

  cleanupOldFailedAttempts() {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    let cleanedCount = 0;

    for (const [key, attempts] of this.failedAttempts.entries()) {
      const recentAttempts = attempts.filter(attempt => attempt.timestamp > cutoffTime);

      if (recentAttempts.length !== attempts.length) {
        if (recentAttempts.length === 0) {
          this.failedAttempts.delete(key);
        } else {
          this.failedAttempts.set(key, recentAttempts);
        }
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🐺 Cleaned up ${cleanedCount} old failed attempt records`);
    }
  }

  cleanupStrictRateLimits() {
    if (!this.strictRateLimits) return;

    const now = Date.now();
    let cleanedCount = 0;

    for (const [userId, data] of this.strictRateLimits.entries()) {
      if (data.expiresAt < now) {
        this.strictRateLimits.delete(userId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🐺 Cleaned up ${cleanedCount} expired strict rate limits`);
    }
  }

  // Cleanup and destruction
  destroy() {
    try {
      // Clear intervals
      if (this.sessionInterval) {
        clearInterval(this.sessionInterval);
      }
      if (this.rateLimitCleanupInterval) {
        clearInterval(this.rateLimitCleanupInterval);
      }
      if (this.activityUpdateTimeout) {
        clearTimeout(this.activityUpdateTimeout);
      }
      if (this.formObserverTimeout) {
        clearTimeout(this.formObserverTimeout);
      }

      // Disconnect observers
      if (this.formObserver) {
        this.formObserver.disconnect();
      }

      // Clear sensitive data
      this.csrfToken = null;
      this.failedAttempts.clear();
      this.rateLimitMap.clear();
      this.securityEvents.length = 0;

      if (this.strictRateLimits) {
        this.strictRateLimits.clear();
      }

      // Clear session storage
      sessionStorage.removeItem('ctf_session');

      this.isInitialized = false;
      console.log('🐺 Security system destroyed');
    } catch (error) {
      console.error('🐺 Error during security system destruction:', error);
    }
  }
}

// Initialize security manager
const securityManager = new SecurityManager();

// Make securityManager globally available for debugging and emergency access
window.securityManager = securityManager;

export default securityManager;
