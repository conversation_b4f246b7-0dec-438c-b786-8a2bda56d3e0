// Enhanced Real-time Leaderboard System for The Wolf Challenge
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import scoreService from './score-service.js';
import {
  collection,
  query,
  orderBy,
  limit,
  onSnapshot,
  where,
  getDocs,
  getDoc,
  doc,
  setDoc,
  updateDoc,
  writeBatch,
  serverTimestamp
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class LeaderboardManager {
  constructor() {
    this.leaderboardData = [];
    this.unsubscribe = null;
    this.isRealTimeEnabled = false;
    this.showEmails = false; // Privacy setting for email display
    this.refreshInterval = null;
    this.rankingUpdateInterval = null;
    this.filters = {
      category: 'all',
      timeframe: 'all',
      showOnlyActive: false
    };
    this.sortBy = 'score'; // score, challengesSolved, lastActivity
    this.sortOrder = 'desc';
    this.pageSize = 50;
    this.currentPage = 1;
    this.totalPages = 1;
    this.liveUpdateIndicator = null;
    this.rankingCache = new Map();
    this.initializeLeaderboard();
  }

  initializeLeaderboard() {
    // Set up real-time listener when leaderboard tab is active
    this.setupTabListener();

    // Initialize live update indicator
    this.setupLiveUpdateIndicator();

    // Start periodic ranking updates
    this.startRankingUpdates();

    // Set up visibility change listener for performance optimization
    this.setupVisibilityListener();
  }

  setupTabListener() {
    const leaderboardTab = document.getElementById('leaderboard-tab');
    if (leaderboardTab) {
      leaderboardTab.addEventListener('click', () => {
        this.enableRealTimeUpdates();
        this.showLiveUpdateIndicator();
      });
    }

    // Listen for tab changes to disable real-time updates when not needed
    const tabs = ['challenges-tab', 'profile-tab', 'admin-tab'];
    tabs.forEach(tabId => {
      const tab = document.getElementById(tabId);
      if (tab) {
        tab.addEventListener('click', () => {
          this.disableRealTimeUpdates();
          this.hideLiveUpdateIndicator();
        });
      }
    });
  }

  setupLiveUpdateIndicator() {
    // Create live update indicator element
    this.liveUpdateIndicator = document.createElement('div');
    this.liveUpdateIndicator.id = 'live-update-indicator';
    this.liveUpdateIndicator.className = 'fixed top-4 right-4 z-50 hidden';
    this.liveUpdateIndicator.innerHTML = `
      <div class="neo-brutalist bg-green-500 text-white px-4 py-2 text-sm font-bold flex items-center space-x-2">
        <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        <span>LIVE</span>
      </div>
    `;
    document.body.appendChild(this.liveUpdateIndicator);
  }

  setupVisibilityListener() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Page is hidden, reduce update frequency
        this.reduceUpdateFrequency();
      } else {
        // Page is visible, restore normal frequency
        this.restoreUpdateFrequency();
      }
    });
  }

  startRankingUpdates() {
    // Update rankings every 30 seconds
    this.rankingUpdateInterval = setInterval(() => {
      if (this.isRealTimeEnabled) {
        this.updateRankings();
      }
    }, 30000);
  }

  showLiveUpdateIndicator() {
    if (this.liveUpdateIndicator) {
      this.liveUpdateIndicator.classList.remove('hidden');
    }
  }

  hideLiveUpdateIndicator() {
    if (this.liveUpdateIndicator) {
      this.liveUpdateIndicator.classList.add('hidden');
    }
  }

  reduceUpdateFrequency() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      // Reduce to every 30 seconds when page is hidden
      this.refreshInterval = setInterval(() => {
        if (this.isRealTimeEnabled) {
          this.refreshLeaderboard();
        }
      }, 30000);
    }
  }

  restoreUpdateFrequency() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      // Restore to every 5 seconds when page is visible
      this.refreshInterval = setInterval(() => {
        if (this.isRealTimeEnabled) {
          this.refreshLeaderboard();
        }
      }, 5000);
    }
  }

  enableRealTimeUpdates() {
    if (this.isRealTimeEnabled) return;

    console.log('🐺 Enabling enhanced real-time leaderboard updates');
    this.isRealTimeEnabled = true;

    try {
      // SECURITY: Only Firebase database operations allowed
      if (!db || typeof collection === 'undefined') {
        console.warn('🐺 Firebase not available, using demo mode with periodic updates');
        this.startDemoMode();
        return;
      }

      // Create enhanced real-time query with current filters
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      let leaderboardQuery = query(
        usersRef,
        where('role', '==', CTF_CONFIG.USER_ROLES.PARTICIPANT)
      );

      // Apply sorting
      if (this.sortBy === 'score') {
        leaderboardQuery = query(leaderboardQuery,
          orderBy('score', this.sortOrder),
          orderBy('lastActivity', 'desc') // Tie-breaker
        );
      } else if (this.sortBy === 'challengesSolved') {
        leaderboardQuery = query(leaderboardQuery,
          orderBy('challengesSolved', this.sortOrder),
          orderBy('score', 'desc') // Tie-breaker
        );
      } else if (this.sortBy === 'lastActivity') {
        leaderboardQuery = query(leaderboardQuery,
          orderBy('lastActivity', this.sortOrder)
        );
      }

      // Apply limit with pagination
      leaderboardQuery = query(leaderboardQuery, limit(this.pageSize * 2)); // Load extra for smooth pagination

      // Set up real-time listener
      this.unsubscribe = onSnapshot(leaderboardQuery, (snapshot) => {
        console.log('🐺 Enhanced real-time leaderboard update received');
        this.handleEnhancedLeaderboardUpdate(snapshot);
        this.showUpdateAnimation();
      }, (error) => {
        console.error('🐺 Leaderboard real-time error:', error);
        this.handleLeaderboardError();
      });

      // Also set up periodic refresh as backup
      this.startPeriodicRefresh();
    } catch (error) {
      console.error('🐺 Critical error setting up real-time updates:', error);
      this.startDemoMode();
    }
  }

  handleEnhancedLeaderboardUpdate(snapshot) {
    console.log('🐺 Processing enhanced leaderboard update...');
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (!leaderboardContent) {
      console.error('🐺 Leaderboard content element not found');
      return;
    }

    // Process the snapshot data with enhanced features
    let allUsers = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Apply filters
    allUsers = this.applyFilters(allUsers);

    // Calculate rankings
    allUsers = this.calculateRankings(allUsers);

    // Apply pagination
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.leaderboardData = allUsers.slice(startIndex, endIndex);
    this.totalPages = Math.ceil(allUsers.length / this.pageSize);

    console.log('🐺 Enhanced leaderboard data processed:', {
      totalUsers: allUsers.length,
      currentPage: this.currentPage,
      totalPages: this.totalPages,
      displayedUsers: this.leaderboardData.length
    });

    // Update the leaderboard display
    this.renderEnhancedLeaderboard();

    // Show update indicator
    this.showUpdateIndicator();
  }

  applyFilters(users) {
    let filteredUsers = [...users];

    // Filter by activity
    if (this.filters.showOnlyActive) {
      const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      filteredUsers = filteredUsers.filter(user =>
        user.lastActivity && user.lastActivity.toDate() > dayAgo
      );
    }

    // Filter by timeframe
    if (this.filters.timeframe !== 'all') {
      const timeframes = {
        'today': 24 * 60 * 60 * 1000,
        'week': 7 * 24 * 60 * 60 * 1000,
        'month': 30 * 24 * 60 * 60 * 1000
      };

      if (timeframes[this.filters.timeframe]) {
        const cutoff = new Date(Date.now() - timeframes[this.filters.timeframe]);
        filteredUsers = filteredUsers.filter(user =>
          user.lastActivity && user.lastActivity.toDate() > cutoff
        );
      }
    }

    // Filter by category (based on progress)
    if (this.filters.category !== 'all') {
      filteredUsers = filteredUsers.filter(user => {
        const progress = user.progress || {};
        const categoryProgress = progress[this.filters.category];
        return categoryProgress && categoryProgress.solved > 0;
      });
    }

    return filteredUsers;
  }

  calculateRankings(users) {
    // Sort users and assign rankings
    users.sort((a, b) => {
      if (this.sortBy === 'score') {
        if (b.score !== a.score) return b.score - a.score;
        return new Date(b.lastActivity?.toDate() || 0) - new Date(a.lastActivity?.toDate() || 0);
      } else if (this.sortBy === 'challengesSolved') {
        if (b.challengesSolved !== a.challengesSolved) return b.challengesSolved - a.challengesSolved;
        return b.score - a.score;
      } else if (this.sortBy === 'lastActivity') {
        return new Date(b.lastActivity?.toDate() || 0) - new Date(a.lastActivity?.toDate() || 0);
      }
      return 0;
    });

    // Assign ranks with tie handling
    let currentRank = 1;
    for (let i = 0; i < users.length; i++) {
      if (i > 0 && this.shouldHaveSameRank(users[i], users[i-1])) {
        users[i].rank = users[i-1].rank;
      } else {
        users[i].rank = currentRank;
      }
      currentRank = i + 2; // Next available rank
    }

    return users;
  }

  shouldHaveSameRank(user1, user2) {
    if (this.sortBy === 'score') {
      return user1.score === user2.score;
    } else if (this.sortBy === 'challengesSolved') {
      return user1.challengesSolved === user2.challengesSolved && user1.score === user2.score;
    }
    return false;
  }

  startDemoMode() {
    console.log('🐺 Starting demo leaderboard mode');
    this.loadDemoLeaderboard();

    // Update demo data every 10 seconds
    this.refreshInterval = setInterval(() => {
      console.log('🐺 Refreshing demo leaderboard');
      this.loadDemoLeaderboard();
    }, 10000);
  }

  startPeriodicRefresh() {
    // Refresh every 5 seconds as backup to real-time updates
    this.refreshInterval = setInterval(() => {
      if (this.isRealTimeEnabled) {
        console.log('🐺 Periodic refresh check');
        this.refreshLeaderboard();
      }
    }, 5000);
  }

  disableRealTimeUpdates() {
    if (!this.isRealTimeEnabled) return;

    console.log('🐺 Disabling enhanced real-time leaderboard updates');
    this.isRealTimeEnabled = false;

    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }

    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }

    if (this.rankingUpdateInterval) {
      clearInterval(this.rankingUpdateInterval);
      this.rankingUpdateInterval = null;
    }
  }

  renderEnhancedLeaderboard() {
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (!leaderboardContent) return;

    const currentUser = authManager.getCurrentUser();
    const currentUserId = currentUser ? currentUser.uid : null;

    leaderboardContent.innerHTML = `
      <!-- Enhanced Leaderboard Controls -->
      <div class="mb-6 space-y-4">
        <!-- Filter Controls -->
        <div class="neo-brutalist bg-gray-50 p-4">
          <h4 class="text-lg font-bold mb-3">🔍 Filters & Controls</h4>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Category Filter -->
            <div>
              <label class="block text-sm font-bold mb-1">Category</label>
              <select id="category-filter" class="neo-brutalist w-full p-2 text-sm bg-white border-2 border-black">
                <option value="all">All Categories</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>

            <!-- Timeframe Filter -->
            <div>
              <label class="block text-sm font-bold mb-1">Timeframe</label>
              <select id="timeframe-filter" class="neo-brutalist w-full p-2 text-sm bg-white border-2 border-black">
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
              </select>
            </div>

            <!-- Sort Options -->
            <div>
              <label class="block text-sm font-bold mb-1">Sort By</label>
              <select id="sort-filter" class="neo-brutalist w-full p-2 text-sm bg-white border-2 border-black">
                <option value="score">Score</option>
                <option value="challengesSolved">Challenges Solved</option>
                <option value="lastActivity">Last Activity</option>
              </select>
            </div>

            <!-- Additional Options -->
            <div class="flex flex-col space-y-2">
              <label class="flex items-center space-x-2">
                <input type="checkbox" id="active-only-filter" class="w-4 h-4">
                <span class="text-sm font-bold">Active Only (24h)</span>
              </label>
              <button id="refresh-leaderboard" class="neo-brutalist bg-blue-500 text-white px-3 py-1 text-sm font-bold">
                <i class="fas fa-refresh mr-1"></i>REFRESH
              </button>
            </div>
          </div>
        </div>

        <!-- Live Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="neo-brutalist bg-blue-50 p-3 text-center">
            <div class="text-2xl font-bold text-blue-600">${this.leaderboardData.length}</div>
            <div class="text-xs">Showing</div>
          </div>
          <div class="neo-brutalist bg-green-50 p-3 text-center">
            <div class="text-2xl font-bold text-green-600">${this.totalPages}</div>
            <div class="text-xs">Total Pages</div>
          </div>
          <div class="neo-brutalist bg-purple-50 p-3 text-center">
            <div class="text-2xl font-bold text-purple-600" id="live-update-count">0</div>
            <div class="text-xs">Live Updates</div>
          </div>
          <div class="neo-brutalist bg-yellow-50 p-3 text-center">
            <div class="text-2xl font-bold text-yellow-600">${new Date().toLocaleTimeString()}</div>
            <div class="text-xs">Last Update</div>
          </div>
        </div>
      </div>

      <!-- Enhanced Leaderboard Table -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b-4 border-black bg-gray-100">
              <th class="text-left p-3 font-bold">Rank</th>
              <th class="text-left p-3 font-bold">Hunter</th>
              <th class="text-center p-3 font-bold cursor-pointer hover:bg-gray-200" onclick="leaderboardManager.setSortBy('score')">
                Score ${this.sortBy === 'score' ? (this.sortOrder === 'desc' ? '↓' : '↑') : ''}
              </th>
              <th class="text-center p-3 font-bold cursor-pointer hover:bg-gray-200" onclick="leaderboardManager.setSortBy('challengesSolved')">
                Solved ${this.sortBy === 'challengesSolved' ? (this.sortOrder === 'desc' ? '↓' : '↑') : ''}
              </th>
              <th class="text-center p-3 font-bold">Progress</th>
              <th class="text-center p-3 font-bold cursor-pointer hover:bg-gray-200" onclick="leaderboardManager.setSortBy('lastActivity')">
                Last Active ${this.sortBy === 'lastActivity' ? (this.sortOrder === 'desc' ? '↓' : '↑') : ''}
              </th>
              <th class="text-center p-3 font-bold">Trend</th>
            </tr>
          </thead>
          <tbody>
            ${this.leaderboardData.map(user => this.renderEnhancedLeaderboardRow(user, currentUserId)).join('')}
          </tbody>
        </table>
      </div>

      <!-- Pagination Controls -->
      ${this.renderPaginationControls()}

      <!-- Enhanced Stats -->
      ${this.renderEnhancedLeaderboardStats()}
    `;

    // Setup enhanced event listeners
    this.setupEnhancedLeaderboardControls();
  }

  renderEnhancedLeaderboardRow(user, currentUserId) {
    const isCurrentUser = currentUserId === user.id;
    const lastActive = user.lastActivity ?
      this.formatTimeAgo(user.lastActivity.toDate()) : 'Never';

    // Calculate trend (this would be enhanced with historical data)
    const trend = this.calculateUserTrend(user);
    const trendIcon = trend > 0 ? '📈' : trend < 0 ? '📉' : '➡️';
    const trendColor = trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-600';

    return `
      <tr class="${isCurrentUser ? 'bg-yellow-100 border-4 border-yellow-400' : 'border-b border-gray-200'} hover:bg-gray-50 transition-colors">
        <td class="p-3">
          <div class="flex items-center space-x-2">
            <div class="text-2xl font-bold ${this.getRankColor(user.rank)}">#${user.rank}</div>
            ${this.getRankBadge(user.rank)}
          </div>
        </td>
        <td class="p-3">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold">
              ${user.email ? user.email.charAt(0).toUpperCase() : '?'}
            </div>
            <div>
              <div class="font-bold ${isCurrentUser ? 'text-yellow-800' : 'text-gray-900'}">
                ${this.showEmails ? user.email : this.maskEmail(user.email)}
                ${isCurrentUser ? ' (You)' : ''}
              </div>
              <div class="text-sm text-gray-600">
                ${this.getHunterTitle(user.score)}
              </div>
            </div>
          </div>
        </td>
        <td class="p-3 text-center">
          <div class="text-2xl font-bold text-green-600">${user.score || 0}</div>
          <div class="text-xs text-gray-500">/ ${CTF_CONFIG.TOTAL_POSSIBLE_SCORE}</div>
        </td>
        <td class="p-3 text-center">
          <div class="text-lg font-bold">${user.challengesSolved || 0}</div>
          <div class="text-xs text-gray-500">/ 70</div>
        </td>
        <td class="p-3 text-center">
          <div class="space-y-1">
            ${this.renderProgressBars(user.progress)}
          </div>
        </td>
        <td class="p-3 text-center">
          <div class="text-sm">${lastActive}</div>
          <div class="text-xs ${this.getActivityColor(user.lastActivity)}">
            ${this.getActivityStatus(user.lastActivity)}
          </div>
        </td>
        <td class="p-3 text-center">
          <div class="text-lg ${trendColor}">${trendIcon}</div>
          <div class="text-xs ${trendColor}">${trend > 0 ? '+' : ''}${trend}</div>
        </td>
      </tr>
    `;
  }

  setupEnhancedLeaderboardControls() {
    // Category filter
    const categoryFilter = document.getElementById('category-filter');
    if (categoryFilter) {
      categoryFilter.value = this.filters.category;
      categoryFilter.addEventListener('change', (e) => {
        this.filters.category = e.target.value;
        this.applyFiltersAndRefresh();
      });
    }

    // Timeframe filter
    const timeframeFilter = document.getElementById('timeframe-filter');
    if (timeframeFilter) {
      timeframeFilter.value = this.filters.timeframe;
      timeframeFilter.addEventListener('change', (e) => {
        this.filters.timeframe = e.target.value;
        this.applyFiltersAndRefresh();
      });
    }

    // Sort filter
    const sortFilter = document.getElementById('sort-filter');
    if (sortFilter) {
      sortFilter.value = this.sortBy;
      sortFilter.addEventListener('change', (e) => {
        this.setSortBy(e.target.value);
      });
    }

    // Active only filter
    const activeOnlyFilter = document.getElementById('active-only-filter');
    if (activeOnlyFilter) {
      activeOnlyFilter.checked = this.filters.showOnlyActive;
      activeOnlyFilter.addEventListener('change', (e) => {
        this.filters.showOnlyActive = e.target.checked;
        this.applyFiltersAndRefresh();
      });
    }

    // Refresh button
    const refreshButton = document.getElementById('refresh-leaderboard');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        this.refreshLeaderboard();
        this.showRefreshAnimation();
      });
    }
  }

  setSortBy(sortBy) {
    if (this.sortBy === sortBy) {
      // Toggle sort order
      this.sortOrder = this.sortOrder === 'desc' ? 'asc' : 'desc';
    } else {
      this.sortBy = sortBy;
      this.sortOrder = 'desc'; // Default to descending for new sort
    }
    this.applyFiltersAndRefresh();
  }

  applyFiltersAndRefresh() {
    this.currentPage = 1; // Reset to first page
    if (this.isRealTimeEnabled) {
      // Restart real-time updates with new filters
      this.disableRealTimeUpdates();
      setTimeout(() => {
        this.enableRealTimeUpdates();
      }, 100);
    } else {
      this.refreshLeaderboard();
    }
  }

  renderPaginationControls() {
    if (this.totalPages <= 1) return '';

    const prevDisabled = this.currentPage <= 1;
    const nextDisabled = this.currentPage >= this.totalPages;

    return `
      <div class="flex justify-center items-center space-x-4 mt-6">
        <button ${prevDisabled ? 'disabled' : ''}
                onclick="leaderboardManager.goToPage(${this.currentPage - 1})"
                class="neo-brutalist ${prevDisabled ? 'bg-gray-300' : 'bg-blue-500 hover:bg-blue-600'} text-white px-4 py-2 font-bold ${prevDisabled ? 'cursor-not-allowed' : ''}">
          <i class="fas fa-chevron-left mr-1"></i>PREV
        </button>

        <div class="flex space-x-2">
          ${this.renderPageNumbers()}
        </div>

        <button ${nextDisabled ? 'disabled' : ''}
                onclick="leaderboardManager.goToPage(${this.currentPage + 1})"
                class="neo-brutalist ${nextDisabled ? 'bg-gray-300' : 'bg-blue-500 hover:bg-blue-600'} text-white px-4 py-2 font-bold ${nextDisabled ? 'cursor-not-allowed' : ''}">
          NEXT<i class="fas fa-chevron-right ml-1"></i>
        </button>
      </div>
    `;
  }

  renderPageNumbers() {
    const pages = [];
    const maxVisible = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
    let endPage = Math.min(this.totalPages, startPage + maxVisible - 1);

    if (endPage - startPage + 1 < maxVisible) {
      startPage = Math.max(1, endPage - maxVisible + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      const isActive = i === this.currentPage;
      pages.push(`
        <button onclick="leaderboardManager.goToPage(${i})"
                class="neo-brutalist ${isActive ? 'bg-yellow-400 text-black' : 'bg-gray-300 text-black hover:bg-gray-400'} px-3 py-2 font-bold">
          ${i}
        </button>
      `);
    }

    return pages.join('');
  }

  goToPage(page) {
    if (page < 1 || page > this.totalPages || page === this.currentPage) return;

    this.currentPage = page;
    this.applyFiltersAndRefresh();
  }

  renderLeaderboard() {
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (!leaderboardContent) return;

    if (this.leaderboardData.length === 0) {
      leaderboardContent.innerHTML = this.renderEmptyState();
      return;
    }

    const currentUserId = authManager.getCurrentUser()?.uid;

    leaderboardContent.innerHTML = `
      <div class="mb-4 space-y-4">
        <!-- Live Status and Controls -->
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm font-bold">LIVE LEADERBOARD</span>
            <span class="text-xs text-gray-500">(Updates every 5 seconds)</span>
          </div>
          <div class="text-sm text-gray-600">
            Last updated: ${new Date().toLocaleTimeString()}
          </div>
        </div>

        <!-- Privacy Controls -->
        <div class="flex justify-between items-center p-3 bg-gray-50 border-2 border-gray-300">
          <div class="flex items-center space-x-4">
            <label class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" id="show-emails-toggle" ${this.showEmails ? 'checked' : ''}
                     class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
              <span class="text-sm font-bold">Show Email Addresses</span>
            </label>
            <div class="text-xs text-gray-500">
              ${this.showEmails ? '🔓 Emails visible' : '🔒 Emails hidden for privacy'}
            </div>
          </div>
          <button id="refresh-leaderboard" class="neo-brutalist bg-blue-500 text-white px-3 py-1 text-xs font-bold">
            <i class="fas fa-sync-alt mr-1"></i>REFRESH
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b-4 border-black bg-gray-100">
              <th class="text-left p-3 font-bold">Rank</th>
              <th class="text-left p-3 font-bold">Hunter</th>
              <th class="text-center p-3 font-bold">Score</th>
              <th class="text-center p-3 font-bold">Solved</th>
              <th class="text-center p-3 font-bold">Progress</th>
              <th class="text-center p-3 font-bold">Last Active</th>
            </tr>
          </thead>
          <tbody>
            ${this.leaderboardData.map(user => this.renderLeaderboardRow(user, currentUserId)).join('')}
          </tbody>
        </table>
      </div>

      ${this.renderLeaderboardStats()}
    `;

    // Setup event listeners for controls
    this.setupLeaderboardControls();
  }

  renderLeaderboardRow(user, currentUserId) {
    const isCurrentUser = user.id === currentUserId;
    const lastActive = this.formatLastActive(user.lastActivity);
    
    return `
      <tr class="border-b-2 border-gray-200 hover:bg-gray-50 transition-colors
                 ${isCurrentUser ? 'bg-yellow-100 border-yellow-300' : ''}">
        <td class="p-3">
          <div class="flex items-center">
            ${this.getRankDisplay(user.rank)}
          </div>
        </td>
        <td class="p-3">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center mr-3 text-white font-bold">
              ${user.email.charAt(0).toUpperCase()}
            </div>
            <div>
              <div class="font-bold ${isCurrentUser ? 'text-yellow-800' : ''}">
                ${this.displayEmail(user.email, isCurrentUser)}
                ${isCurrentUser ? '<span class="text-xs bg-yellow-500 text-white px-2 py-1 rounded ml-2">YOU</span>' : ''}
              </div>
              <div class="text-sm text-gray-600">
                ${this.getHunterTitle(user.score)}
              </div>
            </div>
          </div>
        </td>
        <td class="p-3 text-center">
          <div class="text-2xl font-bold text-green-600">${user.score || 0}</div>
          <div class="text-xs text-gray-500">/ ${CTF_CONFIG.TOTAL_POSSIBLE_SCORE}</div>
        </td>
        <td class="p-3 text-center">
          <div class="text-lg font-bold">${user.challengesSolved || 0}</div>
          <div class="text-xs text-gray-500">/ 70</div>
        </td>
        <td class="p-3 text-center">
          <div class="space-y-1">
            ${this.renderProgressBars(user.progress)}
          </div>
        </td>
        <td class="p-3 text-center">
          <div class="text-sm">${lastActive}</div>
        </td>
      </tr>
    `;
  }

  getRankDisplay(rank) {
    let icon, bgColor, textColor;
    
    switch (rank) {
      case 1:
        icon = '🥇';
        bgColor = 'bg-yellow-400';
        textColor = 'text-yellow-800';
        break;
      case 2:
        icon = '🥈';
        bgColor = 'bg-gray-300';
        textColor = 'text-gray-800';
        break;
      case 3:
        icon = '🥉';
        bgColor = 'bg-orange-300';
        textColor = 'text-orange-800';
        break;
      default:
        icon = '🏅';
        bgColor = 'bg-blue-100';
        textColor = 'text-blue-800';
    }
    
    return `
      <div class="flex items-center">
        <span class="text-2xl mr-2">${icon}</span>
        <span class="px-2 py-1 ${bgColor} ${textColor} font-bold text-sm rounded">#${rank}</span>
      </div>
    `;
  }

  displayEmail(email, isCurrentUser) {
    // Always show current user's full email
    if (isCurrentUser) {
      return email;
    }

    // Show full email if privacy setting allows, otherwise mask it
    if (this.showEmails) {
      return email;
    } else {
      return this.maskEmail(email);
    }
  }

  maskEmail(email) {
    const [username, domain] = email.split('@');
    if (username.length <= 3) return email;

    const maskedUsername = username.substring(0, 2) + '*'.repeat(username.length - 2);
    return `${maskedUsername}@${domain}`;
  }

  setupLeaderboardControls() {
    // Email visibility toggle
    const emailToggle = document.getElementById('show-emails-toggle');
    if (emailToggle) {
      emailToggle.addEventListener('change', (e) => {
        this.showEmails = e.target.checked;
        console.log('🐺 Email visibility changed:', this.showEmails ? 'visible' : 'hidden');
        // Save preference to localStorage
        localStorage.setItem('ctf_show_emails', this.showEmails.toString());
        // Re-render leaderboard with new setting
        this.renderLeaderboard();
      });
    }

    // Manual refresh button
    const refreshBtn = document.getElementById('refresh-leaderboard');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        console.log('🐺 Manual leaderboard refresh triggered');
        this.forceRefresh();
      });
    }

    // Load saved preference
    const savedPreference = localStorage.getItem('ctf_show_emails');
    if (savedPreference !== null) {
      this.showEmails = savedPreference === 'true';
      if (emailToggle) {
        emailToggle.checked = this.showEmails;
      }
    }
  }

  getHunterTitle(score) {
    if (score >= 600) return '🐺 Alpha Wolf';
    if (score >= 400) return '🦊 Pack Leader';
    if (score >= 200) return '🐕 Hunter';
    if (score >= 100) return '🐶 Scout';
    return '🐾 Rookie';
  }

  renderProgressBars(progress) {
    if (!progress) return '<div class="text-xs text-gray-400">No progress</div>';
    
    const categories = [
      { key: 'beginner', color: 'green', icon: '🟢' },
      { key: 'intermediate', color: 'yellow', icon: '🟡' },
      { key: 'advanced', color: 'red', icon: '🔴' }
    ];
    
    return categories.map(category => {
      const categoryProgress = progress[category.key];
      if (!categoryProgress) return '';
      
      const percentage = (categoryProgress.solved / categoryProgress.total) * 100;
      return `
        <div class="flex items-center text-xs">
          <span class="mr-1">${category.icon}</span>
          <div class="w-16 h-2 bg-gray-200 mr-1 border border-gray-400">
            <div class="h-full bg-${category.color}-500 transition-all duration-300" 
                 style="width: ${percentage}%"></div>
          </div>
          <span class="text-xs">${categoryProgress.solved}/${categoryProgress.total}</span>
        </div>
      `;
    }).join('');
  }

  formatLastActive(timestamp) {
    if (!timestamp) return 'Never';
    
    const now = new Date();
    const lastActive = timestamp.toDate();
    const diffMs = now - lastActive;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return lastActive.toLocaleDateString();
  }

  renderLeaderboardStats() {
    const totalParticipants = this.leaderboardData.length;
    const activeParticipants = this.leaderboardData.filter(user => 
      user.lastActivity && (new Date() - user.lastActivity.toDate()) < 86400000 // 24 hours
    ).length;
    
    const averageScore = totalParticipants > 0 
      ? Math.round(this.leaderboardData.reduce((sum, user) => sum + (user.score || 0), 0) / totalParticipants)
      : 0;
    
    const topScore = this.leaderboardData.length > 0 ? this.leaderboardData[0].score || 0 : 0;
    
    return `
      <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="neo-brutalist bg-blue-50 p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">${totalParticipants}</div>
          <div class="text-sm">Total Hunters</div>
        </div>
        <div class="neo-brutalist bg-green-50 p-4 text-center">
          <div class="text-2xl font-bold text-green-600">${activeParticipants}</div>
          <div class="text-sm">Active Today</div>
        </div>
        <div class="neo-brutalist bg-purple-50 p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">${averageScore}</div>
          <div class="text-sm">Average Score</div>
        </div>
        <div class="neo-brutalist bg-yellow-50 p-4 text-center">
          <div class="text-2xl font-bold text-yellow-600">${topScore}</div>
          <div class="text-sm">Top Score</div>
        </div>
      </div>
    `;
  }

  renderEmptyState() {
    return `
      <div class="text-center py-12">
        <div class="text-6xl mb-4">🐺</div>
        <h3 class="text-2xl font-bold mb-2">No Hunters Yet</h3>
        <p class="text-gray-600 mb-4">Be the first to solve challenges and claim your place on the leaderboard!</p>
        <button onclick="document.getElementById('challenges-tab').click()" 
                class="neo-brutalist bg-yellow-400 text-black px-6 py-3 text-lg font-bold">
          START HUNTING
        </button>
      </div>
    `;
  }

  showUpdateIndicator() {
    // Create a subtle update indicator
    const indicator = document.createElement('div');
    indicator.className = 'fixed top-20 right-4 bg-green-500 text-white px-3 py-1 text-sm font-bold rounded z-50';
    indicator.textContent = 'Updated';
    
    document.body.appendChild(indicator);
    
    // Remove after 2 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
      }
    }, 2000);
  }

  loadDemoLeaderboard() {
    console.log('🐺 Loading demo leaderboard data');

    // Create demo leaderboard data
    const demoData = this.generateDemoLeaderboardData();

    // Simulate snapshot structure
    const mockSnapshot = {
      docs: demoData.map((user) => ({
        id: user.id,
        data: () => user
      }))
    };

    this.handleEnhancedLeaderboardUpdate(mockSnapshot);

    // Show demo mode indicator
    this.showDemoModeIndicator();
  }

  generateDemoLeaderboardData() {
    const currentUser = authManager.getCurrentUser();

    // Generate more dynamic demo data
    const demoUsers = [
      {
        id: 'demo-user-1',
        email: '<EMAIL>',
        role: 'participant',
        score: 680 + Math.floor(Math.random() * 20), // Add some randomness
        challengesSolved: 68,
        lastActivity: { toDate: () => new Date(Date.now() - Math.floor(Math.random() * 5) * 60 * 1000) },
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 20, total: 20 },
          advanced: { solved: 38, total: 40 }
        }
      },
      {
        id: 'demo-user-2',
        email: '<EMAIL>',
        role: 'participant',
        score: 580,
        challengesSolved: 58,
        lastActivity: { toDate: () => new Date(Date.now() - 8 * 60 * 1000) },
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 20, total: 20 },
          advanced: { solved: 28, total: 40 }
        }
      },
      {
        id: 'demo-user-3',
        email: '<EMAIL>',
        role: 'participant',
        score: 520,
        challengesSolved: 52,
        lastActivity: { toDate: () => new Date(Date.now() - 15 * 60 * 1000) },
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 20, total: 20 },
          advanced: { solved: 22, total: 40 }
        }
      },
      {
        id: 'demo-user-4',
        email: '<EMAIL>',
        role: 'participant',
        score: 480,
        challengesSolved: 48,
        lastActivity: { toDate: () => new Date(Date.now() - 25 * 60 * 1000) },
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 18, total: 20 },
          advanced: { solved: 20, total: 40 }
        }
      },
      {
        id: 'demo-user-5',
        email: '<EMAIL>',
        role: 'participant',
        score: 420,
        challengesSolved: 42,
        lastActivity: { toDate: () => new Date(Date.now() - 35 * 60 * 1000) },
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 16, total: 20 },
          advanced: { solved: 16, total: 40 }
        }
      },
      {
        id: 'demo-user-6',
        email: '<EMAIL>',
        role: 'participant',
        score: 380,
        challengesSolved: 38,
        lastActivity: { toDate: () => new Date(Date.now() - 45 * 60 * 1000) },
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 14, total: 20 },
          advanced: { solved: 14, total: 40 }
        }
      },
      {
        id: 'demo-user-7',
        email: '<EMAIL>',
        role: 'participant',
        score: 340,
        challengesSolved: 34,
        lastActivity: { toDate: () => new Date(Date.now() - 60 * 60 * 1000) },
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 12, total: 20 },
          advanced: { solved: 12, total: 40 }
        }
      },
      {
        id: 'demo-user-8',
        email: '<EMAIL>',
        role: 'participant',
        score: 290,
        challengesSolved: 29,
        lastActivity: { toDate: () => new Date(Date.now() - 90 * 60 * 1000) },
        progress: {
          beginner: { solved: 9, total: 10 },
          intermediate: { solved: 10, total: 20 },
          advanced: { solved: 10, total: 40 }
        }
      },
      {
        id: 'demo-user-9',
        email: '<EMAIL>',
        role: 'participant',
        score: 250,
        challengesSolved: 25,
        lastActivity: { toDate: () => new Date(Date.now() - 120 * 60 * 1000) },
        progress: {
          beginner: { solved: 8, total: 10 },
          intermediate: { solved: 9, total: 20 },
          advanced: { solved: 8, total: 40 }
        }
      },
      {
        id: 'demo-user-10',
        email: '<EMAIL>',
        role: 'participant',
        score: 180,
        challengesSolved: 18,
        lastActivity: { toDate: () => new Date(Date.now() - 180 * 60 * 1000) },
        progress: {
          beginner: { solved: 6, total: 10 },
          intermediate: { solved: 6, total: 20 },
          advanced: { solved: 6, total: 40 }
        }
      }
    ];

    // Add current user to leaderboard if logged in
    if (currentUser) {
      // Get or create user score from localStorage for persistence
      const userScoreKey = `ctf_user_score_${currentUser.uid}`;
      let userScore = parseInt(localStorage.getItem(userScoreKey)) || Math.floor(Math.random() * 300) + 150;

      // Save score for persistence
      localStorage.setItem(userScoreKey, userScore.toString());

      const challengesSolved = Math.floor(userScore / 10);
      const currentUserData = {
        id: currentUser.uid,
        email: currentUser.email,
        role: currentUser.email.includes('admin') ? 'admin' : 'participant',
        score: userScore,
        challengesSolved: challengesSolved,
        lastActivity: { toDate: () => new Date() },
        progress: {
          beginner: { solved: Math.min(10, Math.floor(challengesSolved * 0.3)), total: 10 },
          intermediate: { solved: Math.min(20, Math.floor(challengesSolved * 0.4)), total: 20 },
          advanced: { solved: Math.min(40, Math.floor(challengesSolved * 0.3)), total: 40 }
        }
      };

      // Remove any existing entry for current user
      const existingIndex = demoUsers.findIndex(user => user.id === currentUser.uid);
      if (existingIndex !== -1) {
        demoUsers.splice(existingIndex, 1);
      }

      demoUsers.push(currentUserData);
    }

    // Sort by score (descending) and then by last activity (most recent first)
    return demoUsers.sort((a, b) => {
      if (b.score !== a.score) {
        return b.score - a.score;
      }
      return b.lastActivity.toDate().getTime() - a.lastActivity.toDate().getTime();
    });
  }

  showDemoModeIndicator() {
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (leaderboardContent) {
      // Add demo mode banner
      const demoBanner = document.createElement('div');
      demoBanner.className = 'mb-4 p-3 bg-yellow-100 border-4 border-yellow-500 text-yellow-800';
      demoBanner.innerHTML = `
        <div class="flex items-center space-x-2">
          <i class="fas fa-info-circle"></i>
          <span class="font-bold">Demo Mode:</span>
          <span>Showing sample leaderboard data</span>
        </div>
      `;
      leaderboardContent.insertBefore(demoBanner, leaderboardContent.firstChild);
    }
  }

  handleLeaderboardError() {
    console.error('🐺 Leaderboard error - attempting recovery...');

    // Try to recover with different approaches
    this.attemptErrorRecovery();
  }

  async attemptErrorRecovery() {
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (!leaderboardContent) return;

    // Show recovery attempt message
    leaderboardContent.innerHTML = `
      <div class="text-center py-8">
        <div class="text-4xl mb-4">🔄</div>
        <h3 class="text-xl font-bold mb-2">Attempting to Connect...</h3>
        <p class="text-gray-600 mb-4">Trying to establish database connection</p>
        <div class="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
      </div>
    `;

    // Try multiple recovery strategies
    const recoveryStrategies = [
      () => this.trySimpleQuery(),
      () => this.tryWithDifferentCollection(),
      () => this.checkFirebaseRules(),
      () => this.loadDemoLeaderboard()
    ];

    for (let i = 0; i < recoveryStrategies.length; i++) {
      try {
        console.log(`🐺 Trying recovery strategy ${i + 1}...`);
        await recoveryStrategies[i]();
        return; // Success, exit recovery
      } catch (error) {
        console.warn(`🐺 Recovery strategy ${i + 1} failed:`, error.message);
        continue;
      }
    }

    // All recovery attempts failed
    this.showFinalErrorState();
  }

  async trySimpleQuery() {
    console.log('🐺 Attempting simple Firestore query...');

    const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
    const simpleQuery = query(usersRef, limit(5));
    const snapshot = await getDocs(simpleQuery);

    if (snapshot.empty) {
      // No users yet, show empty state
      this.showEmptyLeaderboard();
    } else {
      // Process the data
      this.handleEnhancedLeaderboardUpdate(snapshot);
    }
  }

  async tryWithDifferentCollection() {
    console.log('🐺 Trying alternative data source...');

    // Try to read from a different collection or use cached data
    const cachedData = localStorage.getItem('ctf_leaderboard_cache');
    if (cachedData) {
      const parsedData = JSON.parse(cachedData);
      if (parsedData && parsedData.length > 0) {
        console.log('🐺 Using cached leaderboard data');
        this.leaderboardData = parsedData;
        this.renderEnhancedLeaderboard();
        return;
      }
    }

    throw new Error('No cached data available');
  }

  async checkFirebaseRules() {
    console.log('🐺 Checking Firebase configuration...');

    // Try to create a test document to check permissions
    const user = authManager.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated - please login first');
    }

    // Try to read user's own document
    const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      // User document doesn't exist, try to create it
      await setDoc(userRef, {
        email: user.email,
        role: 'participant',
        score: 0,
        challengesSolved: 0,
        progress: {
          beginner: { solved: 0, total: 10 },
          intermediate: { solved: 0, total: 20 },
          advanced: { solved: 0, total: 40 }
        },
        solvedChallenges: [],
        createdAt: new Date(),
        lastActivity: new Date()
      });

      console.log('🐺 User document created, retrying leaderboard...');
      await this.trySimpleQuery();
    } else {
      console.log('🐺 User document exists, retrying leaderboard...');
      await this.trySimpleQuery();
    }
  }

  showEmptyLeaderboard() {
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (leaderboardContent) {
      leaderboardContent.innerHTML = `
        <div class="text-center py-12">
          <div class="text-6xl mb-4">🏆</div>
          <h3 class="text-2xl font-bold mb-4">Be the First Hunter!</h3>
          <p class="text-gray-600 mb-6">No participants yet. Start solving challenges to claim the top spot!</p>
          <button onclick="window.location.hash = 'challenges'"
                  class="neo-brutalist bg-blue-500 text-white px-6 py-3 font-bold hover:bg-blue-600">
            START HUNTING 🎯
          </button>
        </div>
      `;
    }
  }

  showFinalErrorState() {
    // Show the service fix panel
    const serviceFixPanel = document.getElementById('service-fix-panel');
    if (serviceFixPanel) {
      serviceFixPanel.classList.remove('hidden');
    }

    const leaderboardContent = document.getElementById('leaderboard-content');
    if (leaderboardContent) {
      leaderboardContent.innerHTML = `
        <div class="text-center py-12">
          <div class="text-4xl mb-4">🚫</div>
          <h3 class="text-xl font-bold mb-2 text-red-600">SERVICE UNAVAILABLE</h3>
          <p class="text-gray-600 mb-4">Unable to load leaderboard data</p>

          <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6 text-left max-w-md mx-auto">
            <h4 class="font-bold text-red-800 mb-2">Common Causes:</h4>
            <ul class="text-sm text-red-700 space-y-1">
              <li>• Firestore rules not deployed</li>
              <li>• User not authenticated</li>
              <li>• Network connection issues</li>
              <li>• Database permissions error</li>
            </ul>
          </div>

          <div class="space-x-4">
            <button onclick="serviceUnavailableFixer.diagnoseAndFix()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold hover:bg-green-600">
              🔧 AUTO-FIX SERVICE
            </button>
            <button onclick="leaderboardManager.attemptErrorRecovery()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold hover:bg-blue-600">
              🔄 RETRY CONNECTION
            </button>
            <button onclick="leaderboardManager.loadDemoLeaderboard()"
                    class="neo-brutalist bg-gray-500 text-white px-4 py-2 font-bold hover:bg-gray-600">
              📊 VIEW DEMO
            </button>
          </div>

          <div class="mt-6 text-xs text-gray-500">
            <p>If the auto-fix doesn't work, please contact the administrator</p>
          </div>
        </div>
      `;
    }
  }

  // Enhanced utility methods
  calculateUserTrend(user) {
    // This would be enhanced with historical data from score events
    // For now, return a simple calculation based on recent activity
    if (!user.lastActivity) return 0;

    const hoursSinceActivity = (Date.now() - user.lastActivity.toDate().getTime()) / (1000 * 60 * 60);
    if (hoursSinceActivity < 1) return Math.floor(Math.random() * 10) + 1; // Active users trending up
    if (hoursSinceActivity < 24) return Math.floor(Math.random() * 5) - 2; // Recent users mixed
    return Math.floor(Math.random() * 3) - 3; // Inactive users trending down
  }

  getRankColor(rank) {
    if (rank === 1) return 'text-yellow-500'; // Gold
    if (rank === 2) return 'text-gray-400'; // Silver
    if (rank === 3) return 'text-orange-600'; // Bronze
    if (rank <= 10) return 'text-blue-600'; // Top 10
    return 'text-gray-600'; // Others
  }

  getRankBadge(rank) {
    if (rank === 1) return '<span class="text-xs bg-yellow-500 text-white px-2 py-1 rounded font-bold">👑 CHAMPION</span>';
    if (rank === 2) return '<span class="text-xs bg-gray-400 text-white px-2 py-1 rounded font-bold">🥈 RUNNER-UP</span>';
    if (rank === 3) return '<span class="text-xs bg-orange-600 text-white px-2 py-1 rounded font-bold">🥉 THIRD</span>';
    if (rank <= 10) return '<span class="text-xs bg-blue-600 text-white px-2 py-1 rounded font-bold">⭐ TOP 10</span>';
    return '';
  }

  getActivityColor(lastActivity) {
    if (!lastActivity) return 'text-gray-500';

    const hoursSince = (Date.now() - lastActivity.toDate().getTime()) / (1000 * 60 * 60);
    if (hoursSince < 1) return 'text-green-600';
    if (hoursSince < 24) return 'text-yellow-600';
    if (hoursSince < 168) return 'text-orange-600'; // 1 week
    return 'text-red-600';
  }

  getActivityStatus(lastActivity) {
    if (!lastActivity) return 'Inactive';

    const hoursSince = (Date.now() - lastActivity.toDate().getTime()) / (1000 * 60 * 60);
    if (hoursSince < 1) return 'Online';
    if (hoursSince < 24) return 'Recent';
    if (hoursSince < 168) return 'This Week';
    return 'Inactive';
  }

  formatTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }

  showUpdateAnimation() {
    // Increment live update counter
    const counter = document.getElementById('live-update-count');
    if (counter) {
      const current = parseInt(counter.textContent) || 0;
      counter.textContent = current + 1;

      // Flash animation
      counter.parentElement.classList.add('animate-pulse');
      setTimeout(() => {
        counter.parentElement.classList.remove('animate-pulse');
      }, 1000);
    }
  }

  showRefreshAnimation() {
    const refreshButton = document.getElementById('refresh-leaderboard');
    if (refreshButton) {
      const icon = refreshButton.querySelector('i');
      if (icon) {
        icon.classList.add('animate-spin');
        setTimeout(() => {
          icon.classList.remove('animate-spin');
        }, 1000);
      }
    }
  }

  renderEnhancedLeaderboardStats() {
    if (this.leaderboardData.length === 0) return '';

    const totalParticipants = this.leaderboardData.length;
    const activeParticipants = this.leaderboardData.filter(user =>
      user.lastActivity && (new Date() - user.lastActivity.toDate()) < 86400000
    ).length;

    const averageScore = totalParticipants > 0
      ? Math.round(this.leaderboardData.reduce((sum, user) => sum + (user.score || 0), 0) / totalParticipants)
      : 0;

    const topScore = this.leaderboardData.length > 0 ? this.leaderboardData[0].score || 0 : 0;
    const completionRates = this.calculateCompletionRates();

    return `
      <div class="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Competition Stats -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">📊 Competition Stats</h4>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="font-bold">Total Hunters:</span>
              <span class="text-blue-600 font-bold">${totalParticipants}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-bold">Active (24h):</span>
              <span class="text-green-600 font-bold">${activeParticipants}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-bold">Average Score:</span>
              <span class="text-purple-600 font-bold">${averageScore}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-bold">Top Score:</span>
              <span class="text-yellow-600 font-bold">${topScore}</span>
            </div>
          </div>
        </div>

        <!-- Category Completion -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">🎯 Category Progress</h4>
          <div class="space-y-3">
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm font-bold">Beginner</span>
                <span class="text-sm">${completionRates.beginner}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-green-600 h-2 rounded-full" style="width: ${completionRates.beginner}%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm font-bold">Intermediate</span>
                <span class="text-sm">${completionRates.intermediate}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-yellow-600 h-2 rounded-full" style="width: ${completionRates.intermediate}%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm font-bold">Advanced</span>
                <span class="text-sm">${completionRates.advanced}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-red-600 h-2 rounded-full" style="width: ${completionRates.advanced}%"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Live Activity -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">⚡ Live Activity</h4>
          <div class="space-y-2 text-sm">
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Real-time updates active</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Showing page ${this.currentPage} of ${this.totalPages}</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>Filters: ${this.getActiveFiltersText()}</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span>Sort: ${this.sortBy} (${this.sortOrder})</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  calculateCompletionRates() {
    if (this.leaderboardData.length === 0) {
      return { beginner: 0, intermediate: 0, advanced: 0 };
    }

    const totals = { beginner: 0, intermediate: 0, advanced: 0 };
    const maxes = {
      beginner: CTF_CONFIG.SCORING.BEGINNER.challenges,
      intermediate: CTF_CONFIG.SCORING.INTERMEDIATE.challenges,
      advanced: CTF_CONFIG.SCORING.ADVANCED.challenges
    };

    this.leaderboardData.forEach(user => {
      const progress = user.progress || {};
      Object.keys(totals).forEach(category => {
        totals[category] += (progress[category]?.solved || 0);
      });
    });

    return {
      beginner: Math.round((totals.beginner / (maxes.beginner * this.leaderboardData.length)) * 100),
      intermediate: Math.round((totals.intermediate / (maxes.intermediate * this.leaderboardData.length)) * 100),
      advanced: Math.round((totals.advanced / (maxes.advanced * this.leaderboardData.length)) * 100)
    };
  }

  getActiveFiltersText() {
    const filters = [];
    if (this.filters.category !== 'all') filters.push(this.filters.category);
    if (this.filters.timeframe !== 'all') filters.push(this.filters.timeframe);
    if (this.filters.showOnlyActive) filters.push('active only');
    return filters.length > 0 ? filters.join(', ') : 'none';
  }

  // Update rankings in database (called by score service)
  async updateRankings() {
    try {
      console.log('🐺 Updating leaderboard rankings...');

      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const usersQuery = query(
        usersRef,
        where('role', '==', CTF_CONFIG.USER_ROLES.PARTICIPANT),
        orderBy('score', 'desc'),
        orderBy('challengesSolved', 'desc'), // Secondary sort by challenges solved
        orderBy('lastActivity', 'desc') // Tertiary sort by last activity
      );

      const snapshot = await getDocs(usersQuery);
      const batch = writeBatch(db);

      let currentRank = 1;
      let previousScore = null;
      let previousChallengesSolved = null;
      let usersAtCurrentRank = 0;

      snapshot.docs.forEach((doc, index) => {
        const userData = doc.data();
        const userScore = userData.score || 0;
        const userChallengesSolved = userData.challengesSolved || 0;

        // Handle ties - users with same score and same challenges solved get same rank
        if (previousScore !== null &&
            (userScore !== previousScore || userChallengesSolved !== previousChallengesSolved)) {
          currentRank += usersAtCurrentRank;
          usersAtCurrentRank = 0;
        }

        usersAtCurrentRank++;
        previousScore = userScore;
        previousChallengesSolved = userChallengesSolved;

        // Update user's rank in database
        batch.update(doc.ref, {
          rank: currentRank,
          lastRankUpdate: serverTimestamp()
        });
      });

      await batch.commit();
      console.log('🐺 Rankings updated successfully with proper tie handling');

      // Trigger leaderboard refresh if currently viewing
      if (this.isRealTimeEnabled) {
        setTimeout(() => {
          this.refreshLeaderboard();
        }, 1000);
      }

    } catch (error) {
      console.error('🐺 Error updating rankings:', error);
    }
  }

  // Enhanced method to detect rank changes and send notifications
  async detectRankChanges(oldData, newData) {
    try {
      const rankChanges = [];

      // Create maps for easy lookup
      const oldRanks = new Map();
      const newRanks = new Map();

      oldData.forEach(user => oldRanks.set(user.id, user.rank));
      newData.forEach(user => newRanks.set(user.id, user.rank));

      // Detect changes
      newData.forEach(user => {
        const oldRank = oldRanks.get(user.id);
        const newRank = user.rank;

        if (oldRank && oldRank !== newRank) {
          rankChanges.push({
            userId: user.id,
            email: user.email,
            oldRank: oldRank,
            newRank: newRank,
            improvement: oldRank > newRank // true if rank improved (lower number = better rank)
          });
        }
      });

      // Send notifications for significant rank changes
      for (const change of rankChanges) {
        if (Math.abs(change.oldRank - change.newRank) >= 3) { // Only notify for changes of 3+ positions
          const message = change.improvement
            ? `🎉 You moved up ${change.oldRank - change.newRank} positions to rank #${change.newRank}!`
            : `You moved down ${change.newRank - change.oldRank} positions to rank #${change.newRank}`;

          // Queue notification
          await this.queueRankChangeNotification(change.userId, change.newRank, message);
        }
      }

      console.log(`🐺 Detected ${rankChanges.length} rank changes`);

    } catch (error) {
      console.error('🐺 Error detecting rank changes:', error);
    }
  }

  async queueRankChangeNotification(userId, newRank, message) {
    try {
      // Add to notifications collection
      const notificationRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.NOTIFICATIONS));
      await setDoc(notificationRef, {
        userId: userId,
        type: CTF_CONFIG.NOTIFICATION_TYPES.RANK_CHANGE,
        title: `Rank Update - #${newRank}`,
        message: message,
        timestamp: serverTimestamp(),
        read: false,
        data: {
          newRank: newRank,
          type: 'rank_change'
        }
      });

    } catch (error) {
      console.error('🐺 Error queuing rank change notification:', error);
    }
  }

  // Manual refresh method
  async refreshLeaderboard() {
    try {
      if (!db || typeof collection === 'undefined') {
        console.log('🐺 Refreshing demo leaderboard');
        this.loadDemoLeaderboard();
        return;
      }

      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      let leaderboardQuery = query(
        usersRef,
        where('role', '==', CTF_CONFIG.USER_ROLES.PARTICIPANT)
      );

      // Apply current sorting
      if (this.sortBy === 'score') {
        leaderboardQuery = query(leaderboardQuery,
          orderBy('score', this.sortOrder),
          orderBy('lastActivity', 'desc')
        );
      } else if (this.sortBy === 'challengesSolved') {
        leaderboardQuery = query(leaderboardQuery,
          orderBy('challengesSolved', this.sortOrder),
          orderBy('score', 'desc')
        );
      } else if (this.sortBy === 'lastActivity') {
        leaderboardQuery = query(leaderboardQuery,
          orderBy('lastActivity', this.sortOrder)
        );
      }

      leaderboardQuery = query(leaderboardQuery, limit(this.pageSize * 2));

      const snapshot = await getDocs(leaderboardQuery);
      this.handleEnhancedLeaderboardUpdate(snapshot);
    } catch (error) {
      console.error('🐺 Error refreshing leaderboard:', error);
      this.handleLeaderboardError();
    }
  }

  // Force refresh method for manual button
  async forceRefresh() {
    console.log('🐺 Force refreshing leaderboard...');

    // Show loading indicator
    const refreshBtn = document.getElementById('refresh-leaderboard');
    if (refreshBtn) {
      refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>REFRESHING';
      refreshBtn.disabled = true;
    }

    try {
      await this.refreshLeaderboard();

      // Show success feedback
      if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-check mr-1"></i>UPDATED';
        setTimeout(() => {
          refreshBtn.innerHTML = '<i class="fas fa-sync-alt mr-1"></i>REFRESH';
          refreshBtn.disabled = false;
        }, 1000);
      }
    } catch (error) {
      console.error('🐺 Force refresh failed:', error);
      if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>ERROR';
        setTimeout(() => {
          refreshBtn.innerHTML = '<i class="fas fa-sync-alt mr-1"></i>REFRESH';
          refreshBtn.disabled = false;
        }, 2000);
      }
    }
  }

  // Cleanup method
  destroy() {
    this.disableRealTimeUpdates();
  }
}

// Initialize leaderboard manager
const leaderboardManager = new LeaderboardManager();

// Expose for debugging
window.leaderboardManager = leaderboardManager;

export default leaderboardManager;
