// Enhanced Admin Panel System for The Wolf Challenge
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import scoreService from './score-service.js';
import pushNotificationService from './push-notification-service.js';
import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  limit,
  where,
  serverTimestamp,
  writeBatch
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AdminManager {
  constructor() {
    this.challenges = [];
    this.users = [];
    this.submissions = [];
    this.currentView = 'overview';
    this.initializeAdmin();
  }

  initializeAdmin() {
    console.log('🐺 Initializing admin manager...');

    // Set up admin tab regardless of current auth state
    this.setupAdminTab();

    // Check auth state and update admin visibility
    this.updateAdminVisibility();

    console.log('🐺 Admin manager initialized');
  }

  updateAdminVisibility() {
    // Check if user is the authorized admin
    const currentUser = authManager && authManager.getCurrentUser();
    const isAuthorizedAdmin = currentUser && currentUser.email === '<EMAIL>';

    console.log('🐺 Admin check result:', isAuthorizedAdmin, 'for user:', currentUser?.email);

    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
      if (isAuthorizedAdmin) {
        adminTab.classList.remove('hidden');
        console.log('🐺 Admin tab shown for authorized admin');
      } else {
        adminTab.classList.add('hidden');
        console.log('🐺 Admin tab hidden - not authorized admin');
      }
    }
  }

  setupAdminTab() {
    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
      adminTab.addEventListener('click', () => {
        console.log('🐺 Admin tab clicked');
        this.loadAdminPanel();
      });
      console.log('🐺 Admin tab event listener added');
    } else {
      console.warn('🐺 Admin tab element not found');
    }
  }

  async loadAdminPanel() {
    console.log('🐺 Loading admin panel...');

    const adminContent = document.getElementById('admin-content');
    if (!adminContent) {
      console.error('🐺 Admin content element not found');
      return;
    }

    // Check if user is the authorized admin
    const currentUser = authManager && authManager.getCurrentUser();
    console.log('🐺 Current user check:', currentUser?.email);

    if (!currentUser || currentUser.email !== '<EMAIL>') {
      console.log('🐺 Access denied for user:', currentUser?.email);
      adminContent.innerHTML = `
        <div class="text-center py-8">
          <div class="text-red-500 text-xl font-bold mb-4">❌ Access Denied</div>
          <p class="text-gray-600">Only authorized admin (<EMAIL>) can access this panel.</p>
          <p class="text-sm text-gray-500 mt-2">Current user: ${currentUser ? currentUser.email : 'Not logged in'}</p>
          <button onclick="location.reload()" class="mt-4 neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
            🔄 RELOAD PAGE
          </button>
        </div>
      `;
      return;
    }

    console.log('🐺 Loading admin panel for authorized admin:', currentUser.email);

    // Show loading message first
    adminContent.innerHTML = `
      <div class="text-center py-8">
        <div class="text-blue-500 text-xl font-bold mb-4">🔄 Loading Admin Panel...</div>
        <p class="text-gray-600">Please wait while we load the admin interface.</p>
      </div>
    `;

    // Small delay to ensure DOM is ready
    setTimeout(() => {
      this.renderAdminInterface();
    }, 100);
  }

  renderAdminInterface() {
    const adminContent = document.getElementById('admin-content');
    if (!adminContent) return;

    console.log('🐺 Rendering admin interface...');

    const currentUser = authManager.getCurrentUser();

    adminContent.innerHTML = `
      <div class="space-y-6">
        <!-- Admin Header -->
        <div class="neo-brutalist bg-green-100 p-4 border-l-4 border-green-500">
          <h3 class="text-lg font-bold text-green-800">👑 Admin Access Granted</h3>
          <p class="text-sm text-green-700">Welcome, ${currentUser.email} - Full administrative privileges enabled</p>
        </div>

        <!-- Admin Navigation -->
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 mb-6">
          <button id="admin-overview" class="admin-nav-btn neo-brutalist bg-red-500 text-white px-3 py-2 text-xs font-bold">
            📊 OVERVIEW
          </button>
          <button id="admin-challenges" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-3 py-2 text-xs font-bold">
            🎯 CHALLENGES
          </button>
          <button id="admin-users" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-3 py-2 text-xs font-bold">
            👥 USERS
          </button>
          <button id="admin-submissions" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-3 py-2 text-xs font-bold">
            📝 SUBMISSIONS
          </button>
          <button id="admin-scores" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-3 py-2 text-xs font-bold">
            🏆 SCORES
          </button>
          <button id="admin-analytics" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-3 py-2 text-xs font-bold">
            📈 ANALYTICS
          </button>
          <button id="admin-notifications" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-3 py-2 text-xs font-bold">
            🔔 NOTIFICATIONS
          </button>
          <button id="admin-settings" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-3 py-2 text-xs font-bold">
            ⚙️ SETTINGS
          </button>
        </div>

        <!-- Quick Actions -->
        <div class="neo-brutalist bg-blue-50 p-4">
          <h4 class="font-bold mb-3">⚡ Quick Actions</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
            <button onclick="window.adminManager.createNewChallenge()" class="neo-brutalist bg-green-500 text-white px-3 py-2 text-xs font-bold">
              ➕ CREATE CTF
            </button>
            <button onclick="window.adminManager.showScoreManager()" class="neo-brutalist bg-yellow-500 text-white px-3 py-2 text-xs font-bold">
              🎯 UPDATE SCORES
            </button>
            <button onclick="window.adminManager.showUserStats()" class="neo-brutalist bg-purple-500 text-white px-3 py-2 text-xs font-bold">
              📊 USER STATS
            </button>
            <button onclick="window.adminManager.exportToExcel()" class="neo-brutalist bg-indigo-500 text-white px-3 py-2 text-xs font-bold">
              📊 EXPORT EXCEL
            </button>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
            <button onclick="window.adminManager.exportAllData()" class="neo-brutalist bg-teal-500 text-white px-3 py-2 text-xs font-bold">
              💾 EXPORT JSON
            </button>
            <button onclick="window.adminManager.createBulkChallenges()" class="neo-brutalist bg-orange-500 text-white px-3 py-2 text-xs font-bold">
              🎯 BULK CTF
            </button>
            <button onclick="window.adminManager.systemBackup()" class="neo-brutalist bg-red-500 text-white px-3 py-2 text-xs font-bold">
              🔄 BACKUP
            </button>
            <button onclick="window.adminManager.generateReport()" class="neo-brutalist bg-purple-500 text-white px-3 py-2 text-xs font-bold">
              📈 REPORT
            </button>
          </div>
        </div>

        <!-- Admin Content Area -->
        <div id="admin-view-content">
          <div class="text-center py-8">
            <div class="text-blue-500 text-lg font-bold">🔄 Initializing Admin Dashboard...</div>
          </div>
        </div>
      </div>
    `;

    console.log('🐺 Admin interface rendered, setting up navigation...');

    // Setup navigation with a small delay to ensure DOM is ready
    setTimeout(() => {
      this.setupAdminNavigation();
      this.loadOverview();
      console.log('🐺 Admin panel loaded successfully');
    }, 200);
  }

  setupAdminNavigation() {
    console.log('🐺 Setting up admin navigation...');
    const navButtons = ['overview', 'challenges', 'users', 'submissions', 'scores', 'analytics', 'notifications', 'settings'];

    navButtons.forEach(view => {
      const button = document.getElementById(`admin-${view}`);
      if (button) {
        button.addEventListener('click', () => {
          console.log(`🐺 Admin nav clicked: ${view}`);
          this.switchAdminView(view);
        });
        console.log(`🐺 Navigation button setup: admin-${view}`);
      } else {
        console.warn(`🐺 Navigation button not found: admin-${view}`);
      }
    });

    console.log('🐺 Admin navigation setup complete');
  }

  switchAdminView(view) {
    // Update navigation buttons
    document.querySelectorAll('.admin-nav-btn').forEach(btn => {
      btn.className = 'admin-nav-btn neo-brutalist bg-gray-300 text-black px-3 py-2 text-xs font-bold';
    });

    const activeBtn = document.getElementById(`admin-${view}`);
    if (activeBtn) {
      activeBtn.className = 'admin-nav-btn neo-brutalist bg-red-500 text-white px-3 py-2 text-xs font-bold';
    }

    this.currentView = view;

    // Load content for the selected view
    switch (view) {
      case 'overview':
        this.loadOverview();
        break;
      case 'challenges':
        this.loadChallengesManagement();
        break;
      case 'users':
        this.loadUsersManagement();
        break;
      case 'submissions':
        this.loadSubmissionsView();
        break;
      case 'scores':
        this.loadScoreManagement();
        break;
      case 'analytics':
        this.loadAnalytics();
        break;
      case 'notifications':
        this.loadNotifications();
        break;
      case 'settings':
        this.loadSettings();
        break;
    }
  }

  async loadOverview() {
    console.log('🐺 Loading admin overview...');
    const content = document.getElementById('admin-view-content');
    if (!content) {
      console.error('🐺 Admin view content element not found');
      return;
    }

    try {
      console.log('🐺 Getting system stats...');

      // Load statistics first
      let stats;
      try {
        stats = await this.getSystemStats();
        console.log('🐺 System stats loaded:', stats);
      } catch (error) {
        console.error('🐺 Error loading stats, using defaults:', error);
        stats = {
          totalUsers: 0,
          totalChallenges: 0,
          totalSubmissions: 0,
          activeUsers: 0
        };
      }

      // Create the initial HTML structure
      console.log('🐺 Creating overview HTML...');
      content.innerHTML = `
        <div class="space-y-6">
          <h3 class="text-2xl font-bold">📊 System Overview</h3>

          <!-- Debug Info -->
          <div class="neo-brutalist bg-yellow-100 p-3 text-sm">
            <strong>🔍 Debug:</strong> Admin panel loaded successfully at ${new Date().toLocaleTimeString()}
          </div>

          <!-- Statistics Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="neo-brutalist bg-blue-50 p-4 text-center">
              <div class="text-3xl font-bold text-blue-600">${stats.totalUsers}</div>
              <div class="text-sm">Total Users</div>
            </div>
            <div class="neo-brutalist bg-green-50 p-4 text-center">
              <div class="text-3xl font-bold text-green-600">${stats.totalChallenges}</div>
              <div class="text-sm">Total Challenges</div>
            </div>
            <div class="neo-brutalist bg-purple-50 p-4 text-center">
              <div class="text-3xl font-bold text-purple-600">${stats.totalSubmissions}</div>
              <div class="text-sm">Total Submissions</div>
            </div>
            <div class="neo-brutalist bg-yellow-50 p-4 text-center">
              <div class="text-3xl font-bold text-yellow-600">${stats.activeUsers}</div>
              <div class="text-sm">Active Users</div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">⚡ Quick Actions</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button onclick="adminManager.createNewChallenge()"
                      class="neo-brutalist bg-green-500 text-white p-4 text-center font-bold">
                <i class="fas fa-plus mb-2 block text-2xl"></i>
                CREATE CHALLENGE
              </button>
              <button onclick="adminManager.exportData()"
                      class="neo-brutalist bg-blue-500 text-white p-4 text-center font-bold">
                <i class="fas fa-download mb-2 block text-2xl"></i>
                EXPORT DATA
              </button>
              <button onclick="adminManager.resetLeaderboard()"
                      class="neo-brutalist bg-red-500 text-white p-4 text-center font-bold">
                <i class="fas fa-refresh mb-2 block text-2xl"></i>
                RESET SCORES
              </button>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">🕒 Recent Activity</h4>
            <div id="recent-admin-activity">
              <div class="text-center py-4">
                <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                <div class="text-gray-600">Loading recent activity...</div>
              </div>
            </div>
          </div>
        </div>
      `;

      // Load recent activity separately to avoid blocking the main UI
      this.loadRecentActivityAsync();

    } catch (error) {
      console.error('🐺 Error loading admin overview:', error);
      content.innerHTML = `
        <div class="text-center py-8">
          <div class="text-4xl mb-4 text-red-500">⚠️</div>
          <h3 class="text-xl font-bold mb-2 text-red-600">Error Loading Overview</h3>
          <p class="text-gray-600 mb-4">Unable to load admin overview</p>
          <button onclick="adminManager.loadOverview()"
                  class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
            🔄 RETRY
          </button>
        </div>
      `;
    }
  }

  async loadRecentActivityAsync() {
    try {
      console.log('🐺 Loading recent activity asynchronously...');
      const activityHTML = await this.getRecentActivity();

      const activityContainer = document.getElementById('recent-admin-activity');
      if (activityContainer) {
        activityContainer.innerHTML = activityHTML;
        console.log('🐺 Recent activity loaded successfully');
      }
    } catch (error) {
      console.error('🐺 Error loading recent activity:', error);
      const activityContainer = document.getElementById('recent-admin-activity');
      if (activityContainer) {
        activityContainer.innerHTML = `
          <div class="text-center py-4">
            <div class="text-red-500 mb-2">⚠️ Error loading activity</div>
            <div class="text-sm text-gray-600">Unable to fetch recent submissions</div>
            <button onclick="adminManager.loadRecentActivityAsync()"
                    class="mt-2 text-xs bg-blue-500 text-white px-3 py-1 rounded font-bold">
              RETRY
            </button>
          </div>
        `;
      }
    }
  }

  async getSystemStats() {
    console.log('🐺 Loading system statistics...');

    try {
      // Load each collection separately with individual error handling
      let usersSnap, challengesSnap, submissionsSnap;

      try {
        usersSnap = await getDocs(collection(db, CTF_CONFIG.COLLECTIONS.USERS));
        console.log('🐺 Users loaded:', usersSnap.size);
      } catch (error) {
        console.warn('🐺 Error loading users:', error);
        usersSnap = { size: 0, docs: [] };
      }

      try {
        challengesSnap = await getDocs(collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES));
        console.log('🐺 Challenges loaded:', challengesSnap.size);
      } catch (error) {
        console.warn('🐺 Error loading challenges:', error);
        challengesSnap = { size: 0, docs: [] };
      }

      try {
        submissionsSnap = await getDocs(collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS));
        console.log('🐺 Submissions loaded:', submissionsSnap.size);
      } catch (error) {
        console.warn('🐺 Error loading submissions:', error);
        submissionsSnap = { size: 0, docs: [] };
      }

      // Calculate active users safely
      let activeUsers = 0;
      try {
        const users = usersSnap.docs.map(doc => doc.data());
        activeUsers = users.filter(user => {
          if (!user.lastActivity) return false;
          try {
            const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            return user.lastActivity.toDate() > dayAgo;
          } catch (error) {
            console.warn('🐺 Error parsing user activity date:', error);
            return false;
          }
        }).length;
      } catch (error) {
        console.warn('🐺 Error calculating active users:', error);
      }

      const stats = {
        totalUsers: usersSnap.size || 0,
        totalChallenges: challengesSnap.size || 0,
        totalSubmissions: submissionsSnap.size || 0,
        activeUsers: activeUsers
      };

      console.log('🐺 System stats calculated:', stats);
      return stats;

    } catch (error) {
      console.error('🐺 Critical error getting system stats:', error);
      return {
        totalUsers: '?',
        totalChallenges: '?',
        totalSubmissions: '?',
        activeUsers: '?'
      };
    }
  }

  async getRecentActivity() {
    console.log('🐺 Loading recent activity...');

    try {
      // Check if database is available
      if (!db) {
        throw new Error('Database not initialized');
      }

      const submissionsRef = collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS);

      let recentSnap;
      let submissions = [];

      try {
        // Try with ordering first
        console.log('🐺 Attempting ordered query for recent activity...');
        const recentQuery = query(submissionsRef, orderBy('timestamp', 'desc'), limit(10));
        recentSnap = await getDocs(recentQuery);
        console.log('🐺 Ordered query successful, found:', recentSnap.size, 'submissions');
      } catch (queryError) {
        console.warn('🐺 Error with ordered query, trying simple query:', queryError.message);
        try {
          // Fallback to simple query
          recentSnap = await getDocs(submissionsRef);
          console.log('🐺 Simple query successful, found:', recentSnap.size, 'submissions');
        } catch (simpleError) {
          console.error('🐺 Simple query also failed:', simpleError.message);
          throw simpleError;
        }
      }

      if (recentSnap.empty) {
        console.log('🐺 No submissions found in database');
        return `
          <div class="text-center py-8">
            <div class="text-4xl mb-2">📊</div>
            <div class="text-gray-500 mb-2">No recent activity</div>
            <div class="text-sm text-gray-400">Activity will appear here when users solve challenges</div>
            <div class="mt-4 p-3 bg-blue-50 border-l-4 border-blue-400 text-left max-w-md mx-auto">
              <div class="text-sm text-blue-700">
                <strong>To see activity:</strong><br>
                • Create challenges in the Challenges section<br>
                • Users need to solve challenges<br>
                • Submissions will appear here automatically
              </div>
            </div>
          </div>
        `;
      }

      // Convert to array and validate data
      submissions = recentSnap.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          userId: data.userId || 'Unknown',
          challengeId: data.challengeId || 'Unknown',
          timestamp: data.timestamp || null,
          isCorrect: data.isCorrect,
          points: data.points || 0,
          ...data
        };
      }).filter(submission => submission.userId && submission.challengeId);

      console.log('🐺 Processed submissions:', submissions.length);

      if (submissions.length === 0) {
        return `
          <div class="text-center py-6">
            <div class="text-3xl mb-2">📝</div>
            <div class="text-gray-500 mb-2">No valid submissions found</div>
            <div class="text-sm text-gray-400">Submissions may be missing required data</div>
          </div>
        `;
      }

      // Sort by timestamp (newest first) and limit to 10
      submissions.sort((a, b) => {
        try {
          const timeA = a.timestamp ? a.timestamp.toDate() : new Date(0);
          const timeB = b.timestamp ? b.timestamp.toDate() : new Date(0);
          return timeB - timeA;
        } catch (error) {
          console.warn('🐺 Error sorting submissions by timestamp:', error);
          return 0;
        }
      });

      submissions = submissions.slice(0, 10);

      const activityHTML = submissions.map(submission => {
        let time = 'Unknown time';
        try {
          time = submission.timestamp ?
            submission.timestamp.toDate().toLocaleString() :
            'Unknown time';
        } catch (error) {
          console.warn('🐺 Error formatting timestamp:', error);
        }

        const userId = submission.userId.length > 8 ?
          submission.userId.substring(0, 8) + '...' :
          submission.userId;

        const challengeId = submission.challengeId.length > 20 ?
          submission.challengeId.substring(0, 20) + '...' :
          submission.challengeId;

        const status = submission.isCorrect === true ? '✅' :
                      submission.isCorrect === false ? '❌' : '📝';

        const points = submission.points > 0 ? `(+${submission.points} pts)` : '';

        return `
          <div class="flex justify-between items-center py-3 border-b border-gray-200 last:border-b-0 hover:bg-gray-50">
            <div class="flex-1">
              <div class="flex items-center space-x-2">
                <span class="text-lg">${status}</span>
                <span class="font-bold text-sm text-blue-600">${userId}</span>
                <span class="text-gray-600 text-sm">submitted flag for</span>
                <span class="text-purple-600 text-sm font-medium">${challengeId}</span>
                ${points ? `<span class="text-green-600 text-xs font-bold">${points}</span>` : ''}
              </div>
            </div>
            <div class="text-xs text-gray-500 text-right">
              <div>${time}</div>
            </div>
          </div>
        `;
      }).join('');

      console.log('🐺 Recent activity HTML generated successfully');
      return activityHTML;

    } catch (error) {
      console.error('🐺 Critical error getting recent activity:', error);
      return `
        <div class="text-center py-6">
          <div class="text-red-500 mb-2 text-2xl">⚠️</div>
          <div class="text-red-600 font-bold mb-2">Error Loading Activity</div>
          <div class="text-sm text-gray-600 mb-3">Unable to fetch recent submissions</div>
          <div class="text-xs text-gray-500 mb-4">Error: ${error.message}</div>
          <button onclick="adminManager.loadRecentActivityAsync()"
                  class="neo-brutalist bg-blue-500 text-white px-4 py-2 text-sm font-bold">
            🔄 RETRY
          </button>
        </div>
      `;
    }
  }

  async loadChallengesManagement() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    console.log('🐺 Loading challenges management...');

    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">🎯 Challenge Management</h3>
          <div class="space-x-2">
            <button onclick="window.adminManager.showCreateChallengeModal()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold">
              ➕ CREATE CHALLENGE
            </button>
            <button onclick="window.adminManager.createBulkChallenges()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              🎯 BULK CREATE
            </button>
            <button onclick="window.adminManager.refreshChallenges()"
                    class="neo-brutalist bg-purple-500 text-white px-4 py-2 font-bold">
              🔄 REFRESH
            </button>
          </div>
        </div>

        <!-- Challenge Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="neo-brutalist bg-blue-50 p-4 text-center">
            <div class="text-2xl font-bold text-blue-600" id="total-challenges-count">0</div>
            <div class="text-sm">Total Challenges</div>
          </div>
          <div class="neo-brutalist bg-green-50 p-4 text-center">
            <div class="text-2xl font-bold text-green-600" id="active-challenges-count">0</div>
            <div class="text-sm">Active Challenges</div>
          </div>
          <div class="neo-brutalist bg-yellow-50 p-4 text-center">
            <div class="text-2xl font-bold text-yellow-600" id="total-points-available">0</div>
            <div class="text-sm">Total Points</div>
          </div>
          <div class="neo-brutalist bg-purple-50 p-4 text-center">
            <div class="text-2xl font-bold text-purple-600" id="categories-count">0</div>
            <div class="text-sm">Categories</div>
          </div>
        </div>

        <!-- Challenge Filters -->
        <div class="neo-brutalist bg-gray-50 p-4">
          <h4 class="font-bold mb-3">🔍 Filters</h4>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <select id="filter-category" class="p-2 border-2 border-gray-300">
              <option value="">All Categories</option>
              <option value="web">Web Security</option>
              <option value="crypto">Cryptography</option>
              <option value="forensics">Digital Forensics</option>
              <option value="pwn">Binary Exploitation</option>
              <option value="reverse">Reverse Engineering</option>
              <option value="misc">Miscellaneous</option>
            </select>
            <select id="filter-difficulty" class="p-2 border-2 border-gray-300">
              <option value="">All Difficulties</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
            <select id="filter-status" class="p-2 border-2 border-gray-300">
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <button onclick="window.adminManager.applyChallengeFilters()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              🔍 APPLY FILTERS
            </button>
          </div>
        </div>

        <!-- Challenge List -->
        <div class="neo-brutalist bg-white p-4">
          <h4 class="font-bold mb-4">📋 Challenge List</h4>
          <div id="challenges-list">
            <div class="text-center py-8">
              <div class="text-blue-500 text-lg font-bold">🔄 Loading challenges...</div>
            </div>
          </div>
        </div>
      </div>
    `;

    await this.loadChallengesList();
    this.displayChallengesList();
    this.updateChallengeStats();
  }

  async loadChallengesList() {
    try {
      console.log('🐺 Loading challenges list...');

      if (window.db && window.CTF_CONFIG) {
        // Load from Firebase
        const challengesRef = collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES);
        const challengesSnap = await getDocs(query(challengesRef, orderBy('category'), orderBy('order')));

        this.challenges = challengesSnap.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
      } else {
        // Fallback: Load from localStorage
        this.challenges = JSON.parse(localStorage.getItem('ctf_challenges') || '[]');
        console.log('🐺 Loading challenges from localStorage (demo mode)');
      }

      const challengesList = document.getElementById('challenges-list');
      if (!challengesList) return;

      if (this.challenges.length === 0) {
        challengesList.innerHTML = `
          <div class="text-center py-12">
            <div class="text-4xl mb-4">🎯</div>
            <h4 class="text-xl font-bold mb-2">No Challenges Yet</h4>
            <p class="text-gray-600 mb-4">Create your first challenge to get started</p>
            <button onclick="adminManager.initializeDefaultChallenges()" 
                    class="neo-brutalist bg-blue-500 text-white px-6 py-3 font-bold">
              INITIALIZE DEFAULT CHALLENGES
            </button>
          </div>
        `;
        return;
      }

      // Group challenges by category
      const categories = {
        beginner: [],
        intermediate: [],
        advanced: []
      };

      this.challenges.forEach(challenge => {
        if (categories[challenge.category]) {
          categories[challenge.category].push(challenge);
        }
      });

      challengesList.innerHTML = Object.keys(categories).map(category => {
        const challenges = categories[category];
        if (challenges.length === 0) return '';

        return `
          <div class="neo-brutalist bg-white p-6 mb-4">
            <h4 class="text-xl font-bold mb-4 capitalize">${category} Challenges (${challenges.length})</h4>
            <div class="space-y-2">
              ${challenges.map(challenge => `
                <div class="flex justify-between items-center p-3 bg-gray-50 border-2 border-gray-300">
                  <div class="flex-1">
                    <div class="font-bold">${challenge.title}</div>
                    <div class="text-sm text-gray-600">${challenge.description}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      ${challenge.type} • ${challenge.points} points • Order: ${challenge.order}
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <button onclick="adminManager.editChallenge('${challenge.id}')" 
                            class="neo-brutalist bg-blue-500 text-white px-3 py-1 text-sm font-bold">
                      EDIT
                    </button>
                    <button onclick="adminManager.deleteChallenge('${challenge.id}')" 
                            class="neo-brutalist bg-red-500 text-white px-3 py-1 text-sm font-bold">
                      DELETE
                    </button>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        `;
      }).join('');

    } catch (error) {
      console.error('Error loading challenges:', error);

      // Try localStorage fallback
      try {
        this.challenges = JSON.parse(localStorage.getItem('ctf_challenges') || '[]');
        console.log('🐺 Fallback to localStorage challenges:', this.challenges.length);

        if (this.challenges.length > 0) {
          // Re-run the display logic with localStorage data
          const challengesList = document.getElementById('challenges-list');
          if (challengesList) {
            challengesList.innerHTML = this.challenges.map(challenge => `
              <div class="neo-brutalist bg-gray-50 p-4 mb-4">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h5 class="text-lg font-bold">${challenge.title || 'Untitled Challenge'}</h5>
                    <p class="text-gray-600 text-sm mb-2">${(challenge.description || '').substring(0, 100)}...</p>
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                      <span>🏆 ${challenge.points || 0} points</span>
                      <span>📅 ${challenge.createdAt ? new Date(challenge.createdAt).toLocaleDateString() : 'Unknown'}</span>
                    </div>
                  </div>
                </div>
              </div>
            `).join('');
          }
          return;
        }
      } catch (fallbackError) {
        console.error('Error loading from localStorage:', fallbackError);
        this.challenges = [];
      }

      const challengesList = document.getElementById('challenges-list');
      if (challengesList) {
        challengesList.innerHTML = `
          <div class="text-center py-8">
            <div class="text-red-500 text-lg font-bold mb-2">⚠️ Error loading challenges</div>
            <p class="text-gray-600 mb-4">There was an issue loading challenges from the database.</p>
            <button onclick="window.adminManager.refreshChallenges()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              🔄 RETRY
            </button>
          </div>
        `;
      }
    }
  }

  displayChallengesList() {
    // This method is called from loadChallengesManagement but the display logic
    // is already handled in loadChallengesList method above
    console.log('🐺 displayChallengesList called - display handled in loadChallengesList');
  }

  updateChallengeStats() {
    if (this.challenges.length === 0) return;

    const totalChallenges = this.challenges.length;
    const activeChallenges = this.challenges.filter(c => c.isActive !== false).length;
    const totalPoints = this.challenges.reduce((sum, c) => sum + (c.points || 0), 0);
    const categories = [...new Set(this.challenges.map(c => c.category))].length;

    const totalEl = document.getElementById('total-challenges-count');
    const activeEl = document.getElementById('active-challenges-count');
    const pointsEl = document.getElementById('total-points-available');
    const categoriesEl = document.getElementById('categories-count');

    if (totalEl) totalEl.textContent = totalChallenges;
    if (activeEl) activeEl.textContent = activeChallenges;
    if (pointsEl) pointsEl.textContent = totalPoints;
    if (categoriesEl) categoriesEl.textContent = categories;
  }

  async refreshChallenges() {
    await this.loadChallengesList();
    this.updateChallengeStats();
    alert('Challenges refreshed!');
  }

  applyChallengeFilters() {
    // Simple filter implementation
    alert('Challenge filters applied! (Feature in development)');
  }

  async initializeDefaultChallenges() {
    if (!confirm('This will create default challenges. Continue?')) return;

    try {
      const defaultChallenges = this.getDefaultChallenges();
      
      for (const challenge of defaultChallenges) {
        const challengeRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES));
        await setDoc(challengeRef, {
          ...challenge,
          createdAt: serverTimestamp(),
          createdBy: authManager.getCurrentUser().uid
        });
      }

      alert('Default challenges created successfully!');
      await this.loadChallengesList();
    } catch (error) {
      console.error('Error creating default challenges:', error);
      alert('Error creating challenges. Please try again.');
    }
  }

  getDefaultChallenges() {
    return [
      // Beginner challenges (10)
      {
        title: "Welcome Hunter",
        description: "Find the hidden flag in the page source",
        category: "beginner",
        type: "HTML/Client-side",
        difficulty: "Easy",
        points: 10,
        order: 1,
        flag: "wolf{welcome_to_the_hunt}",
        instructions: "View the page source to find the hidden flag",
        content: "<!-- wolf{welcome_to_the_hunt} -->",
        hints: ["Try viewing the page source", "Look for HTML comments"]
      },
      {
        title: "Cookie Monster",
        description: "Manipulate cookies to gain access",
        category: "beginner",
        type: "Cookie manipulation",
        difficulty: "Easy",
        points: 10,
        order: 2,
        flag: "wolf{cookie_manipulation_101}",
        instructions: "Change the 'role' cookie value to 'admin'",
        hints: ["Use browser developer tools", "Look at the cookies tab"]
      },
      // Add more challenges here...
      // Intermediate challenges (20)
      {
        title: "SQL Injection Basics",
        description: "Bypass login using SQL injection",
        category: "intermediate",
        type: "Database attacks",
        difficulty: "Medium",
        points: 10,
        order: 1,
        flag: "wolf{sql_injection_master}",
        instructions: "Use SQL injection to bypass the login form",
        hints: ["Try ' OR '1'='1", "Look for vulnerable parameters"]
      },
      // Advanced challenges (40)
      {
        title: "Advanced XSS",
        description: "Execute JavaScript in a filtered environment",
        category: "advanced",
        type: "Web exploitation",
        difficulty: "Hard",
        points: 10,
        order: 1,
        flag: "wolf{xss_filter_bypass}",
        instructions: "Bypass the XSS filter and execute JavaScript",
        hints: ["Try different encoding methods", "Look for filter bypasses"]
      }
    ];
  }

  // Enhanced User Management
  async loadUsersManagement() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">👥 User Management</h3>
          <div class="flex space-x-2">
            <button onclick="adminManager.exportUsers()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-download mr-2"></i>EXPORT USERS
            </button>
            <button onclick="adminManager.showBulkScoreModal()"
                    class="neo-brutalist bg-purple-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-calculator mr-2"></i>BULK SCORE
            </button>
          </div>
        </div>

        <!-- User Filters -->
        <div class="neo-brutalist bg-gray-50 p-4">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <select id="user-role-filter" class="neo-brutalist p-2 bg-white border-2 border-black">
              <option value="all">All Roles</option>
              <option value="participant">Participants</option>
              <option value="admin">Admins</option>
            </select>
            <select id="user-activity-filter" class="neo-brutalist p-2 bg-white border-2 border-black">
              <option value="all">All Users</option>
              <option value="active">Active (24h)</option>
              <option value="recent">Recent (7d)</option>
              <option value="inactive">Inactive</option>
            </select>
            <input type="text" id="user-search" placeholder="Search users..."
                   class="neo-brutalist p-2 bg-white border-2 border-black">
            <button onclick="adminManager.refreshUsers()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-refresh mr-1"></i>REFRESH
            </button>
          </div>
        </div>

        <div id="users-list">
          <div class="text-center py-8">Loading users...</div>
        </div>
      </div>
    `;

    await this.loadUsersList();
  }

  async loadUsersList() {
    try {
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const usersSnap = await getDocs(query(usersRef, orderBy('score', 'desc')));

      this.users = usersSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const usersList = document.getElementById('users-list');
      if (!usersList) return;

      if (this.users.length === 0) {
        usersList.innerHTML = `
          <div class="text-center py-12">
            <div class="text-4xl mb-4">👥</div>
            <h4 class="text-xl font-bold mb-2">No Users Found</h4>
            <p class="text-gray-600">No users are registered yet</p>
          </div>
        `;
        return;
      }

      usersList.innerHTML = `
        <div class="neo-brutalist bg-white p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b-4 border-black bg-gray-100">
                  <th class="text-left p-3 font-bold">User</th>
                  <th class="text-center p-3 font-bold">Role</th>
                  <th class="text-center p-3 font-bold">Score</th>
                  <th class="text-center p-3 font-bold">Solved</th>
                  <th class="text-center p-3 font-bold">Last Active</th>
                  <th class="text-center p-3 font-bold">Actions</th>
                </tr>
              </thead>
              <tbody>
                ${this.users.map(user => this.renderUserRow(user)).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Error loading users:', error);
      const usersList = document.getElementById('users-list');
      if (usersList) {
        usersList.innerHTML = '<div class="text-center py-8 text-red-500">Error loading users</div>';
      }
    }
  }

  renderUserRow(user) {
    const lastActive = user.lastActivity ?
      new Date(user.lastActivity.toDate()).toLocaleDateString() : 'Never';

    const roleColor = user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500';

    return `
      <tr class="border-b border-gray-200 hover:bg-gray-50">
        <td class="p-3">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-sm">
              ${user.email ? user.email.charAt(0).toUpperCase() : '?'}
            </div>
            <div>
              <div class="font-bold">${user.email || 'Unknown'}</div>
              <div class="text-sm text-gray-600">ID: ${user.id.substring(0, 8)}...</div>
            </div>
          </div>
        </td>
        <td class="p-3 text-center">
          <span class="px-2 py-1 ${roleColor} text-white text-xs font-bold rounded">
            ${user.role?.toUpperCase() || 'UNKNOWN'}
          </span>
        </td>
        <td class="p-3 text-center">
          <div class="text-lg font-bold text-green-600">${user.score || 0}</div>
        </td>
        <td class="p-3 text-center">
          <div class="text-lg font-bold">${user.challengesSolved || 0}</div>
        </td>
        <td class="p-3 text-center">
          <div class="text-sm">${lastActive}</div>
        </td>
        <td class="p-3 text-center">
          <div class="flex justify-center space-x-2">
            <button onclick="adminManager.showUserDetails('${user.id}')"
                    class="neo-brutalist bg-blue-500 text-white px-2 py-1 text-xs font-bold">
              VIEW
            </button>
            <button onclick="adminManager.showScoreAdjustModal('${user.id}')"
                    class="neo-brutalist bg-yellow-500 text-white px-2 py-1 text-xs font-bold">
              SCORE
            </button>
            ${user.role !== 'admin' ? `
              <button onclick="adminManager.resetUserProgress('${user.id}')"
                      class="neo-brutalist bg-red-500 text-white px-2 py-1 text-xs font-bold">
                RESET
              </button>
            ` : ''}
          </div>
        </td>
      </tr>
    `;
  }

  // Score Management Functions
  async showScoreAdjustModal(userId) {
    const user = this.users.find(u => u.id === userId);
    if (!user) return;

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-md w-full mx-4">
        <h3 class="text-xl font-bold mb-4">Adjust Score for ${user.email}</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-bold mb-2">Current Score</label>
            <div class="text-2xl font-bold text-green-600">${user.score || 0}</div>
          </div>
          <div>
            <label class="block text-sm font-bold mb-2">Score Adjustment</label>
            <input type="number" id="score-adjustment"
                   class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                   placeholder="Enter positive or negative number">
          </div>
          <div>
            <label class="block text-sm font-bold mb-2">Reason</label>
            <textarea id="adjustment-reason"
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-20"
                      placeholder="Reason for adjustment..."></textarea>
          </div>
          <div class="flex space-x-2">
            <button onclick="adminManager.applyScoreAdjustment('${userId}')"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold flex-1">
              APPLY
            </button>
            <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()"
                    class="neo-brutalist bg-gray-500 text-white px-4 py-2 font-bold flex-1">
              CANCEL
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  async applyScoreAdjustment(userId) {
    try {
      const adjustment = parseInt(document.getElementById('score-adjustment').value);
      const reason = document.getElementById('adjustment-reason').value;

      if (isNaN(adjustment) || adjustment === 0) {
        alert('Please enter a valid score adjustment');
        return;
      }

      if (!reason.trim()) {
        alert('Please provide a reason for the adjustment');
        return;
      }

      const currentUser = authManager.getCurrentUser();
      await scoreService.adjustScore(userId, adjustment, reason, currentUser.uid);

      alert('Score adjustment applied successfully!');

      // Close modal and refresh
      document.querySelector('.fixed.inset-0').remove();
      await this.loadUsersList();

    } catch (error) {
      console.error('Error applying score adjustment:', error);
      alert('Error applying score adjustment: ' + error.message);
    }
  }

  // Enhanced Submissions View
  async loadSubmissionsView() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">📝 Submissions & Activity</h3>
          <div class="flex space-x-2">
            <button onclick="adminManager.exportSubmissions()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-download mr-2"></i>EXPORT
            </button>
            <button onclick="adminManager.refreshSubmissions()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-refresh mr-2"></i>REFRESH
            </button>
          </div>
        </div>

        <!-- Submission Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="neo-brutalist bg-blue-50 p-4 text-center">
            <div class="text-3xl font-bold text-blue-600" id="total-submissions">0</div>
            <div class="text-sm">Total Submissions</div>
          </div>
          <div class="neo-brutalist bg-green-50 p-4 text-center">
            <div class="text-3xl font-bold text-green-600" id="successful-submissions">0</div>
            <div class="text-sm">Successful</div>
          </div>
          <div class="neo-brutalist bg-purple-50 p-4 text-center">
            <div class="text-3xl font-bold text-purple-600" id="unique-solvers">0</div>
            <div class="text-sm">Unique Solvers</div>
          </div>
          <div class="neo-brutalist bg-yellow-50 p-4 text-center">
            <div class="text-3xl font-bold text-yellow-600" id="avg-solve-time">0</div>
            <div class="text-sm">Avg Solve Time</div>
          </div>
        </div>

        <div id="submissions-list">
          <div class="text-center py-8">Loading submissions...</div>
        </div>
      </div>
    `;

    await this.loadSubmissionsList();
  }

  async loadSubmissionsList() {
    console.log('🐺 Loading submissions list...');

    try {
      const submissionsRef = collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS);

      // Try to load submissions with error handling
      let submissionsSnap;
      try {
        submissionsSnap = await getDocs(query(submissionsRef, orderBy('timestamp', 'desc'), limit(100)));
      } catch (queryError) {
        console.warn('🐺 Error with ordered query, trying simple query:', queryError);
        // Fallback to simple query without ordering
        submissionsSnap = await getDocs(submissionsRef);
      }

      this.submissions = submissionsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log('🐺 Loaded submissions:', this.submissions.length);

      // Update stats with safe access
      const totalSubmissionsEl = document.getElementById('total-submissions');
      const successfulSubmissionsEl = document.getElementById('successful-submissions');
      const uniqueSolversEl = document.getElementById('unique-solvers');

      if (totalSubmissionsEl) totalSubmissionsEl.textContent = this.submissions.length;
      if (successfulSubmissionsEl) {
        const successfulCount = this.submissions.filter(s => s.isCorrect === true || s.isCorrect !== false).length;
        successfulSubmissionsEl.textContent = successfulCount;
      }
      if (uniqueSolversEl) {
        const uniqueUsers = new Set(this.submissions.map(s => s.userId).filter(id => id));
        uniqueSolversEl.textContent = uniqueUsers.size;
      }

      const submissionsList = document.getElementById('submissions-list');
      if (!submissionsList) return;

      if (this.submissions.length === 0) {
        submissionsList.innerHTML = `
          <div class="text-center py-12">
            <div class="text-4xl mb-4">📝</div>
            <h4 class="text-xl font-bold mb-2">No Submissions Yet</h4>
            <p class="text-gray-600 mb-4">No challenge submissions found in the database</p>
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 max-w-md mx-auto text-left">
              <h5 class="font-bold text-blue-800 mb-2">To see submissions here:</h5>
              <ul class="text-sm text-blue-700 space-y-1">
                <li>• Users need to solve challenges</li>
                <li>• Submissions are automatically recorded</li>
                <li>• Check that challenges are created and accessible</li>
              </ul>
            </div>
          </div>
        `;
        return;
      }

      // Sort submissions by timestamp (newest first)
      this.submissions.sort((a, b) => {
        const timeA = a.timestamp ? a.timestamp.toDate() : new Date(0);
        const timeB = b.timestamp ? b.timestamp.toDate() : new Date(0);
        return timeB - timeA;
      });

      submissionsList.innerHTML = `
        <div class="neo-brutalist bg-white p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b-4 border-black bg-gray-100">
                  <th class="text-left p-3 font-bold">Time</th>
                  <th class="text-left p-3 font-bold">User</th>
                  <th class="text-left p-3 font-bold">Challenge</th>
                  <th class="text-center p-3 font-bold">Points</th>
                  <th class="text-center p-3 font-bold">Status</th>
                </tr>
              </thead>
              <tbody>
                ${this.submissions.map(submission => this.renderSubmissionRow(submission)).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

      console.log('🐺 Submissions list rendered successfully');

    } catch (error) {
      console.error('🐺 Error loading submissions:', error);

      const submissionsList = document.getElementById('submissions-list');
      if (submissionsList) {
        submissionsList.innerHTML = `
          <div class="text-center py-8">
            <div class="text-4xl mb-4 text-red-500">⚠️</div>
            <h4 class="text-xl font-bold mb-2 text-red-600">Error Loading Submissions</h4>
            <p class="text-gray-600 mb-4">Unable to load submission data</p>
            <div class="bg-red-50 border-l-4 border-red-400 p-4 max-w-md mx-auto text-left">
              <h5 class="font-bold text-red-800 mb-2">Possible causes:</h5>
              <ul class="text-sm text-red-700 space-y-1">
                <li>• Database connection issues</li>
                <li>• Firestore security rules</li>
                <li>• Collection doesn't exist yet</li>
                <li>• Network connectivity problems</li>
              </ul>
            </div>
            <button onclick="adminManager.loadSubmissionsList()"
                    class="mt-4 neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              🔄 RETRY
            </button>
          </div>
        `;
      }

      // Update stats to show error state
      const totalSubmissionsEl = document.getElementById('total-submissions');
      const successfulSubmissionsEl = document.getElementById('successful-submissions');
      const uniqueSolversEl = document.getElementById('unique-solvers');

      if (totalSubmissionsEl) totalSubmissionsEl.textContent = '?';
      if (successfulSubmissionsEl) successfulSubmissionsEl.textContent = '?';
      if (uniqueSolversEl) uniqueSolversEl.textContent = '?';
    }
  }

  renderSubmissionRow(submission) {
    const timestamp = submission.timestamp ?
      new Date(submission.timestamp.toDate()).toLocaleString() : 'Unknown';

    const statusColor = submission.isCorrect !== false ? 'text-green-600' : 'text-red-600';
    const statusText = submission.isCorrect !== false ? '✅ Correct' : '❌ Incorrect';

    return `
      <tr class="border-b border-gray-200 hover:bg-gray-50">
        <td class="p-3">
          <div class="text-sm">${timestamp}</div>
        </td>
        <td class="p-3">
          <div class="text-sm font-bold">${submission.userId?.substring(0, 8) || 'Unknown'}...</div>
        </td>
        <td class="p-3">
          <div class="text-sm">${submission.challengeId || 'Unknown Challenge'}</div>
        </td>
        <td class="p-3 text-center">
          <div class="font-bold">${submission.points || 0}</div>
        </td>
        <td class="p-3 text-center">
          <div class="${statusColor} font-bold text-sm">${statusText}</div>
        </td>
      </tr>
    `;
  }

  // Enhanced Settings
  async loadSettings() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    content.innerHTML = `
      <div class="space-y-6">
        <h3 class="text-2xl font-bold">⚙️ System Settings</h3>

        <!-- Platform Settings -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">🏆 Competition Settings</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-bold mb-2">Competition Status</label>
              <select class="neo-brutalist w-full p-3 bg-white border-2 border-black">
                <option>Active</option>
                <option>Paused</option>
                <option>Ended</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Registration</label>
              <select class="neo-brutalist w-full p-3 bg-white border-2 border-black">
                <option>Open</option>
                <option>Closed</option>
                <option>Invite Only</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Scoring Settings -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">📊 Scoring Configuration</h4>
          <div class="space-y-4">
            <div class="grid grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-bold mb-2">Beginner Points</label>
                <input type="number" value="10" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
              </div>
              <div>
                <label class="block text-sm font-bold mb-2">Intermediate Points</label>
                <input type="number" value="10" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
              </div>
              <div>
                <label class="block text-sm font-bold mb-2">Advanced Points</label>
                <input type="number" value="10" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
              </div>
            </div>
          </div>
        </div>

        <!-- Push Notifications -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">📱 Push Notifications</h4>
          <div class="space-y-4">
            <!-- Notification Composer -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-bold mb-2">Notification Title</label>
                <input type="text" id="notification-title"
                       class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                       placeholder="Enter notification title...">
              </div>
              <div>
                <label class="block text-sm font-bold mb-2">Notification Type</label>
                <select id="notification-type" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
                  <option value="admin_message">Admin Message</option>
                  <option value="score_update">Score Update</option>
                  <option value="achievement">Achievement</option>
                  <option value="rank_change">Rank Change</option>
                </select>
              </div>
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Message</label>
              <textarea id="notification-message"
                        class="neo-brutalist w-full p-3 bg-white border-2 border-black h-24"
                        placeholder="Enter your message..."></textarea>
            </div>
            <div class="flex space-x-4">
              <button onclick="adminManager.sendPushNotificationToAll()"
                      class="neo-brutalist bg-red-500 text-white px-6 py-3 font-bold">
                <i class="fas fa-broadcast-tower mr-2"></i>SEND TO ALL USERS
              </button>
              <button onclick="adminManager.sendTestNotification()"
                      class="neo-brutalist bg-blue-500 text-white px-6 py-3 font-bold">
                <i class="fas fa-test-tube mr-2"></i>TEST NOTIFICATION
              </button>
              <button onclick="adminManager.viewNotificationStats()"
                      class="neo-brutalist bg-purple-500 text-white px-6 py-3 font-bold">
                <i class="fas fa-chart-pie mr-2"></i>VIEW STATS
              </button>
            </div>
          </div>
        </div>

        <!-- System Actions -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">🔧 System Actions</h4>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button onclick="adminManager.backupData()"
                    class="neo-brutalist bg-blue-500 text-white p-4 font-bold">
              <i class="fas fa-database mb-2 block text-2xl"></i>
              BACKUP DATA
            </button>
            <button onclick="adminManager.clearCache()"
                    class="neo-brutalist bg-yellow-500 text-white p-4 font-bold">
              <i class="fas fa-broom mb-2 block text-2xl"></i>
              CLEAR CACHE
            </button>
            <button onclick="adminManager.generateReport()"
                    class="neo-brutalist bg-green-500 text-white p-4 font-bold">
              <i class="fas fa-chart-bar mb-2 block text-2xl"></i>
              GENERATE REPORT
            </button>
            <button onclick="adminManager.resetLeaderboard()"
                    class="neo-brutalist bg-red-500 text-white p-4 font-bold">
              <i class="fas fa-refresh mb-2 block text-2xl"></i>
              RESET LEADERBOARD
            </button>
          </div>
        </div>
      </div>
    `;
  }

  // Utility Functions
  async exportUsers() {
    try {
      const csvContent = this.generateUsersCSV();
      this.downloadCSV(csvContent, 'users_export.csv');
    } catch (error) {
      console.error('Error exporting users:', error);
      alert('Error exporting users');
    }
  }

  async exportSubmissions() {
    try {
      const csvContent = this.generateSubmissionsCSV();
      this.downloadCSV(csvContent, 'submissions_export.csv');
    } catch (error) {
      console.error('Error exporting submissions:', error);
      alert('Error exporting submissions');
    }
  }

  generateUsersCSV() {
    const headers = ['ID', 'Email', 'Role', 'Score', 'Challenges Solved', 'Last Activity'];
    const rows = this.users.map(user => [
      user.id,
      user.email || '',
      user.role || '',
      user.score || 0,
      user.challengesSolved || 0,
      user.lastActivity ? new Date(user.lastActivity.toDate()).toISOString() : ''
    ]);

    return [headers, ...rows].map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n');
  }

  generateSubmissionsCSV() {
    const headers = ['ID', 'User ID', 'Challenge ID', 'Points', 'Timestamp', 'Status'];
    const rows = this.submissions.map(submission => [
      submission.id,
      submission.userId || '',
      submission.challengeId || '',
      submission.points || 0,
      submission.timestamp ? new Date(submission.timestamp.toDate()).toISOString() : '',
      submission.isCorrect !== false ? 'Correct' : 'Incorrect'
    ]);

    return [headers, ...rows].map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n');
  }

  downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  async resetUserProgress(userId) {
    if (!confirm('Are you sure you want to reset this user\'s progress? This action cannot be undone.')) {
      return;
    }

    try {
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);
      await updateDoc(userRef, {
        score: 0,
        challengesSolved: 0,
        solvedChallenges: [],
        progress: {
          beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
          intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
          advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
        },
        lastActivity: serverTimestamp()
      });

      alert('User progress reset successfully!');
      await this.loadUsersList();

    } catch (error) {
      console.error('Error resetting user progress:', error);
      alert('Error resetting user progress: ' + error.message);
    }
  }

  async showUserDetails(userId) {
    const user = this.users.find(u => u.id === userId);
    if (!user) return;

    // Get user's score history
    const scoreHistory = await scoreService.getScoreHistory(userId, 20);

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">User Details: ${user.email}</h3>
          <button onclick="this.parentElement.parentElement.parentElement.remove()"
                  class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
            ✕
          </button>
        </div>

        <div class="space-y-4">
          <!-- User Info -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <strong>User ID:</strong> ${user.id}
            </div>
            <div>
              <strong>Role:</strong> ${user.role || 'Unknown'}
            </div>
            <div>
              <strong>Score:</strong> ${user.score || 0}
            </div>
            <div>
              <strong>Challenges Solved:</strong> ${user.challengesSolved || 0}
            </div>
          </div>

          <!-- Progress Breakdown -->
          <div>
            <h4 class="font-bold mb-2">Progress by Category:</h4>
            <div class="space-y-2">
              ${Object.entries(user.progress || {}).map(([category, progress]) => `
                <div class="flex justify-between items-center">
                  <span class="capitalize">${category}:</span>
                  <span>${progress.solved || 0} / ${progress.total || 0}</span>
                </div>
              `).join('')}
            </div>
          </div>

          <!-- Recent Score Events -->
          <div>
            <h4 class="font-bold mb-2">Recent Score Events:</h4>
            <div class="max-h-40 overflow-y-auto">
              ${scoreHistory.length > 0 ? scoreHistory.map(event => `
                <div class="text-sm border-b border-gray-200 py-2">
                  <div class="flex justify-between">
                    <span>${event.type.replace('_', ' ')}</span>
                    <span class="font-bold ${event.pointsAwarded > 0 ? 'text-green-600' : 'text-red-600'}">
                      ${event.pointsAwarded > 0 ? '+' : ''}${event.pointsAwarded}
                    </span>
                  </div>
                  <div class="text-gray-600">
                    ${event.timestamp ? new Date(event.timestamp.toDate()).toLocaleString() : 'Unknown time'}
                  </div>
                </div>
              `).join('') : '<div class="text-gray-500">No score events found</div>'}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  async refreshUsers() {
    await this.loadUsersList();
  }

  async refreshSubmissions() {
    await this.loadSubmissionsList();
  }

  async backupData() {
    try {
      const data = {
        users: this.users,
        submissions: this.submissions,
        challenges: this.challenges,
        timestamp: new Date().toISOString()
      };

      const jsonContent = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `ctf_backup_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Data backup completed successfully!');
    } catch (error) {
      console.error('Error backing up data:', error);
      alert('Error creating backup: ' + error.message);
    }
  }

  async clearCache() {
    if (!confirm('Are you sure you want to clear all cached data?')) {
      return;
    }

    try {
      // Clear localStorage
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('ctf_')) {
          localStorage.removeItem(key);
        }
      });

      alert('Cache cleared successfully!');
    } catch (error) {
      console.error('Error clearing cache:', error);
      alert('Error clearing cache: ' + error.message);
    }
  }

  async generateReport() {
    try {
      const report = {
        generatedAt: new Date().toISOString(),
        summary: {
          totalUsers: this.users.length,
          totalSubmissions: this.submissions.length,
          totalChallenges: this.challenges.length,
          activeUsers: this.users.filter(u =>
            u.lastActivity && (new Date() - u.lastActivity.toDate()) < 86400000
          ).length
        },
        topUsers: this.users
          .sort((a, b) => (b.score || 0) - (a.score || 0))
          .slice(0, 10)
          .map(u => ({ email: u.email, score: u.score || 0 })),
        categoryStats: this.calculateCategoryStats()
      };

      const jsonContent = JSON.stringify(report, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `ctf_report_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Report generated successfully!');
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Error generating report: ' + error.message);
    }
  }

  calculateCategoryStats() {
    const stats = {
      beginner: { totalSolved: 0, uniqueSolvers: new Set() },
      intermediate: { totalSolved: 0, uniqueSolvers: new Set() },
      advanced: { totalSolved: 0, uniqueSolvers: new Set() }
    };

    this.users.forEach(user => {
      const progress = user.progress || {};
      Object.keys(stats).forEach(category => {
        if (progress[category] && progress[category].solved > 0) {
          stats[category].totalSolved += progress[category].solved;
          stats[category].uniqueSolvers.add(user.id);
        }
      });
    });

    // Convert Sets to counts
    Object.keys(stats).forEach(category => {
      stats[category].uniqueSolvers = stats[category].uniqueSolvers.size;
    });

    return stats;
  }

  // Challenge Management Methods
  async createNewChallenge() {
    this.showCreateChallengeModal();
  }

  async showCreateChallengeModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">🎯 Create New Challenge</h3>
          <button onclick="this.parentElement.parentElement.parentElement.remove()"
                  class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
            ✕
          </button>
        </div>

        <div class="bg-blue-50 border-l-4 border-blue-400 p-3 mb-4">
          <p class="text-sm text-blue-700">
            <strong>Required fields are marked with *</strong> - Please fill all required fields before submitting.
          </p>
        </div>

        <form id="create-challenge-form" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-bold mb-2">Challenge Title *</label>
              <input type="text" id="challenge-title" required
                     class="neo-brutalist w-full p-3 bg-white border-2 border-black focus:border-blue-500"
                     placeholder="Enter challenge title">
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Category *</label>
              <select id="challenge-category" required
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black focus:border-blue-500">
                <option value="">Select Category</option>
                <option value="web">Web Security</option>
                <option value="crypto">Cryptography</option>
                <option value="forensics">Digital Forensics</option>
                <option value="pwn">Binary Exploitation</option>
                <option value="reverse">Reverse Engineering</option>
                <option value="misc">Miscellaneous</option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-bold mb-2">Difficulty *</label>
              <select id="challenge-difficulty" required
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black focus:border-blue-500">
                <option value="">Select Difficulty</option>
                <option value="beginner">Beginner (100-200 pts)</option>
                <option value="intermediate">Intermediate (300-500 pts)</option>
                <option value="advanced">Advanced (600-1000 pts)</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Points *</label>
              <input type="number" id="challenge-points" required min="50" max="1000"
                     class="neo-brutalist w-full p-3 bg-white border-2 border-black focus:border-blue-500"
                     placeholder="100">
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Order</label>
              <input type="number" id="challenge-order" min="1"
                     class="neo-brutalist w-full p-3 bg-white border-2 border-black focus:border-blue-500"
                     placeholder="Auto-generated">
            </div>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Description *</label>
            <textarea id="challenge-description" required
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black focus:border-blue-500 h-20"
                      placeholder="Brief description of the challenge"></textarea>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Instructions *</label>
            <textarea id="challenge-instructions" required
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black focus:border-blue-500 h-24"
                      placeholder="Detailed instructions for participants"></textarea>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Flag *</label>
            <input type="text" id="challenge-flag" required
                   class="neo-brutalist w-full p-3 bg-white border-2 border-black focus:border-blue-500"
                   placeholder="wolf{example_flag}">
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Content (Optional)</label>
            <textarea id="challenge-content"
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-20"
                      placeholder="Additional content, code, or resources"></textarea>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Hints (One per line)</label>
            <textarea id="challenge-hints"
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-16"
                      placeholder="Hint 1&#10;Hint 2&#10;Hint 3"></textarea>
          </div>

          <div class="flex space-x-2">
            <button type="submit"
                    class="neo-brutalist bg-green-500 text-white px-6 py-3 font-bold flex-1">
              CREATE CHALLENGE
            </button>
            <button type="button" onclick="window.adminManager.testChallengeForm()"
                    class="neo-brutalist bg-blue-500 text-white px-3 py-3 font-bold text-xs">
              🧪 TEST
            </button>
            <button type="button" onclick="window.adminManager.fillSampleData()"
                    class="neo-brutalist bg-yellow-500 text-white px-3 py-3 font-bold text-xs">
              📝 SAMPLE
            </button>
            <button type="button" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()"
                    class="neo-brutalist bg-gray-500 text-white px-4 py-3 font-bold">
              CANCEL
            </button>
          </div>
        </form>
      </div>
    `;

    document.body.appendChild(modal);

    // Add difficulty-based point suggestions
    const difficultySelect = document.getElementById('challenge-difficulty');
    const pointsInput = document.getElementById('challenge-points');

    if (difficultySelect && pointsInput) {
      difficultySelect.addEventListener('change', (e) => {
        const difficulty = e.target.value;
        const pointSuggestions = {
          beginner: 100,
          intermediate: 300,
          advanced: 600
        };

        if (pointSuggestions[difficulty]) {
          pointsInput.value = pointSuggestions[difficulty];
          pointsInput.placeholder = `Suggested: ${pointSuggestions[difficulty]}`;
        }
      });
    }

    // Add visual feedback for required fields
    const requiredFields = ['challenge-title', 'challenge-category', 'challenge-difficulty', 'challenge-points', 'challenge-description', 'challenge-instructions', 'challenge-flag'];

    requiredFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        field.addEventListener('blur', () => {
          if (!field.value.trim()) {
            field.style.borderColor = '#ef4444';
            field.style.backgroundColor = '#fef2f2';
          } else {
            field.style.borderColor = '#000';
            field.style.backgroundColor = '#fff';
          }
        });

        field.addEventListener('input', () => {
          if (field.value.trim()) {
            field.style.borderColor = '#22c55e';
            field.style.backgroundColor = '#f0fdf4';
          }
        });
      }
    });

    // Handle form submission
    const form = document.getElementById('create-challenge-form');
    if (form) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        console.log('🐺 Form submitted, calling handleCreateChallenge...');
        await this.handleCreateChallenge();
      });
    } else {
      console.error('🐺 Create challenge form not found!');
    }
  }

  async handleCreateChallenge() {
    try {
      console.log('🐺 Creating new challenge...');

      // Get form elements first and check if they exist
      const titleEl = document.getElementById('challenge-title');
      const categoryEl = document.getElementById('challenge-category');
      const difficultyEl = document.getElementById('challenge-difficulty');
      const pointsEl = document.getElementById('challenge-points');
      const orderEl = document.getElementById('challenge-order');
      const descriptionEl = document.getElementById('challenge-description');
      const instructionsEl = document.getElementById('challenge-instructions');
      const flagEl = document.getElementById('challenge-flag');
      const contentEl = document.getElementById('challenge-content');
      const hintsEl = document.getElementById('challenge-hints');

      // Debug: Check if elements exist
      console.log('🐺 Form elements check:', {
        title: !!titleEl,
        category: !!categoryEl,
        difficulty: !!difficultyEl,
        points: !!pointsEl,
        description: !!descriptionEl,
        instructions: !!instructionsEl,
        flag: !!flagEl
      });

      // Get form values with validation
      const title = titleEl?.value?.trim() || '';
      const category = categoryEl?.value?.trim() || '';
      const difficulty = difficultyEl?.value?.trim() || '';
      const pointsValue = pointsEl?.value?.trim() || '';
      const orderValue = orderEl?.value?.trim() || '';
      const description = descriptionEl?.value?.trim() || '';
      const instructions = instructionsEl?.value?.trim() || '';
      const flag = flagEl?.value?.trim() || '';
      const content = contentEl?.value?.trim() || '';
      const hintsValue = hintsEl?.value?.trim() || '';

      // Debug: Log form values
      console.log('🐺 Form values:', {
        title: `"${title}"`,
        category: `"${category}"`,
        difficulty: `"${difficulty}"`,
        points: `"${pointsValue}"`,
        description: `"${description}"`,
        instructions: `"${instructions}"`,
        flag: `"${flag}"`
      });

      // Validate required fields
      const validationErrors = [];

      if (!title || title.length === 0) {
        validationErrors.push('Challenge title is required');
        console.log('🐺 Title validation failed:', title);
      }

      if (!category || category.length === 0) {
        validationErrors.push('Category is required');
        console.log('🐺 Category validation failed:', category);
      }

      if (!difficulty || difficulty.length === 0) {
        validationErrors.push('Difficulty level is required');
        console.log('🐺 Difficulty validation failed:', difficulty);
      }

      if (!pointsValue || pointsValue.length === 0 || isNaN(parseInt(pointsValue)) || parseInt(pointsValue) < 50) {
        validationErrors.push('Valid points value (50-1000) is required');
        console.log('🐺 Points validation failed:', pointsValue);
      }

      if (!description || description.length === 0) {
        validationErrors.push('Challenge description is required');
        console.log('🐺 Description validation failed:', description);
      }

      if (!instructions || instructions.length === 0) {
        validationErrors.push('Challenge instructions are required');
        console.log('🐺 Instructions validation failed:', instructions);
      }

      if (!flag || flag.length === 0) {
        validationErrors.push('Challenge flag is required');
        console.log('🐺 Flag validation failed:', flag);
      }

      // Validate flag format
      if (flag && flag.length > 0 && !flag.toLowerCase().startsWith('wolf{') && !flag.toLowerCase().startsWith('ctf{')) {
        validationErrors.push('Flag should start with "wolf{" or "CTF{" and end with "}"');
      }

      // Show validation errors if any
      if (validationErrors.length > 0) {
        console.log('🐺 Validation errors:', validationErrors);
        const errorMessage = 'Please fix the following errors:\n\n' + validationErrors.map(error => '• ' + error).join('\n');
        alert(errorMessage);
        return;
      }

      // Parse numeric values safely
      const points = parseInt(pointsValue);
      const order = orderValue ? parseInt(orderValue) : Date.now(); // Auto-generate if not provided

      // Validate numeric ranges
      if (points < 50 || points > 1000) {
        alert('Points must be between 50 and 1000');
        return;
      }

      // Validate points based on difficulty
      const pointRanges = {
        beginner: [50, 200],
        intermediate: [200, 500],
        advanced: [500, 1000]
      };

      const [minPoints, maxPoints] = pointRanges[difficulty] || [50, 1000];
      if (points < minPoints || points > maxPoints) {
        alert(`Points for ${difficulty} difficulty should be between ${minPoints} and ${maxPoints}`);
        return;
      }

      // Process hints
      const hints = hintsValue ? hintsValue.split('\n').filter(h => h.trim()).map(h => h.trim()) : [];

      // Create challenge data object
      const challengeData = {
        title: title,
        category: category,
        difficulty: difficulty,
        points: points,
        order: order,
        description: description,
        instructions: instructions,
        flag: flag,
        content: content,
        hints: hints,
        createdAt: serverTimestamp(),
        createdBy: authManager.getCurrentUser()?.uid || 'admin',
        isActive: true,
        solvedBy: [],
        totalSolves: 0
      };

      console.log('🐺 Challenge data prepared:', challengeData);

      // Show loading message
      const submitBtn = document.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'CREATING...';
      submitBtn.disabled = true;

      try {
        // Save to Firebase
        if (window.db && window.CTF_CONFIG) {
          const challengeRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES));
          await setDoc(challengeRef, challengeData);
          console.log('🐺 Challenge created successfully with ID:', challengeRef.id);
        } else {
          // Fallback: Save to localStorage for demo
          const challenges = JSON.parse(localStorage.getItem('ctf_challenges') || '[]');
          challengeData.id = Date.now().toString();
          challenges.push(challengeData);
          localStorage.setItem('ctf_challenges', JSON.stringify(challenges));
          console.log('🐺 Challenge saved to localStorage (demo mode)');
        }

        alert(`✅ Challenge "${title}" created successfully!\n\n📊 Details:\n• Category: ${category}\n• Difficulty: ${difficulty}\n• Points: ${points}\n• Flag: ${flag}`);

        // Close modal and refresh list
        const modal = document.querySelector('.fixed.inset-0');
        if (modal) {
          modal.remove();
        }

        // Refresh challenges list
        await this.loadChallengesList();
        this.updateChallengeStats();

        // Send notification to users about new challenge
        if (window.adminManager && window.adminManager.broadcastNotificationToUsers) {
          const notification = {
            title: 'New Challenge Available!',
            message: `A new ${difficulty} ${category} challenge "${title}" worth ${points} points has been added!`,
            type: 'challenge',
            sentAt: new Date().toISOString(),
            sentBy: 'Admin'
          };
          window.adminManager.broadcastNotificationToUsers(notification);
        }

      } catch (saveError) {
        console.error('🐺 Error saving challenge:', saveError);
        throw saveError;
      } finally {
        // Restore button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
      }

    } catch (error) {
      console.error('🐺 Error creating challenge:', error);

      let errorMessage = '❌ Error creating challenge:\n\n';

      if (error.message.includes('invalid data')) {
        errorMessage += '• Please ensure all required fields are filled correctly.';
      } else if (error.message.includes('permission')) {
        errorMessage += '• You do not have permission to create challenges.';
      } else if (error.message.includes('network')) {
        errorMessage += '• Network error. Please check your connection and try again.';
      } else {
        errorMessage += `• ${error.message}`;
      }

      errorMessage += '\n\n💡 Tip: Make sure all required fields are filled and try again.';
      alert(errorMessage);
    }
  }

  getDifficultyFromCategory(category) {
    const difficultyMap = {
      'beginner': 'Easy',
      'intermediate': 'Medium',
      'advanced': 'Hard'
    };
    return difficultyMap[category] || 'Medium';
  }

  async editChallenge(challengeId) {
    const challenge = this.challenges.find(c => c.id === challengeId);
    if (!challenge) {
      alert('Challenge not found');
      return;
    }

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">✏️ Edit Challenge: ${challenge.title}</h3>
          <button onclick="this.parentElement.parentElement.parentElement.remove()"
                  class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
            ✕
          </button>
        </div>

        <form id="edit-challenge-form" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-bold mb-2">Challenge Title</label>
              <input type="text" id="edit-challenge-title" required
                     class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                     value="${challenge.title}">
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Category</label>
              <select id="edit-challenge-category" required
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black">
                <option value="beginner" ${challenge.category === 'beginner' ? 'selected' : ''}>Beginner</option>
                <option value="intermediate" ${challenge.category === 'intermediate' ? 'selected' : ''}>Intermediate</option>
                <option value="advanced" ${challenge.category === 'advanced' ? 'selected' : ''}>Advanced</option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-bold mb-2">Type</label>
              <input type="text" id="edit-challenge-type" required
                     class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                     value="${challenge.type}">
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Points</label>
              <input type="number" id="edit-challenge-points" required min="1" max="100"
                     class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                     value="${challenge.points}">
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Order</label>
              <input type="number" id="edit-challenge-order" required min="1"
                     class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                     value="${challenge.order}">
            </div>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Description</label>
            <textarea id="edit-challenge-description" required
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-20">${challenge.description}</textarea>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Instructions</label>
            <textarea id="edit-challenge-instructions" required
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-24">${challenge.instructions}</textarea>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Flag</label>
            <input type="text" id="edit-challenge-flag" required
                   class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                   value="${challenge.flag}">
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Content (Optional)</label>
            <textarea id="edit-challenge-content"
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-20">${challenge.content || ''}</textarea>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Hints (One per line)</label>
            <textarea id="edit-challenge-hints"
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-16">${(challenge.hints || []).join('\n')}</textarea>
          </div>

          <div class="flex space-x-2">
            <button type="submit"
                    class="neo-brutalist bg-blue-500 text-white px-6 py-3 font-bold flex-1">
              UPDATE CHALLENGE
            </button>
            <button type="button" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()"
                    class="neo-brutalist bg-gray-500 text-white px-6 py-3 font-bold flex-1">
              CANCEL
            </button>
          </div>
        </form>
      </div>
    `;

    document.body.appendChild(modal);

    // Handle form submission
    const form = document.getElementById('edit-challenge-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      await this.handleEditChallenge(challengeId);
    });
  }

  async handleEditChallenge(challengeId) {
    try {
      console.log('🐺 Updating challenge:', challengeId);

      // Get form values with validation
      const title = document.getElementById('edit-challenge-title').value?.trim();
      const category = document.getElementById('edit-challenge-category').value?.trim();
      const type = document.getElementById('edit-challenge-type').value?.trim();
      const pointsValue = document.getElementById('edit-challenge-points').value?.trim();
      const orderValue = document.getElementById('edit-challenge-order').value?.trim();
      const description = document.getElementById('edit-challenge-description').value?.trim();
      const instructions = document.getElementById('edit-challenge-instructions').value?.trim();
      const flag = document.getElementById('edit-challenge-flag').value?.trim();
      const content = document.getElementById('edit-challenge-content').value?.trim() || '';
      const hintsValue = document.getElementById('edit-challenge-hints').value?.trim() || '';

      // Validate required fields
      const validationErrors = [];

      if (!title) {
        validationErrors.push('Challenge title is required');
      }

      if (!category) {
        validationErrors.push('Category is required');
      }

      if (!type) {
        validationErrors.push('Challenge type is required');
      }

      if (!pointsValue || isNaN(parseInt(pointsValue)) || parseInt(pointsValue) < 1) {
        validationErrors.push('Valid points value (1-100) is required');
      }

      if (!orderValue || isNaN(parseInt(orderValue)) || parseInt(orderValue) < 1) {
        validationErrors.push('Valid order value is required');
      }

      if (!description) {
        validationErrors.push('Challenge description is required');
      }

      if (!instructions) {
        validationErrors.push('Challenge instructions are required');
      }

      if (!flag) {
        validationErrors.push('Challenge flag is required');
      }

      // Show validation errors if any
      if (validationErrors.length > 0) {
        const errorMessage = 'Please fix the following errors:\n\n' + validationErrors.map(error => '• ' + error).join('\n');
        alert(errorMessage);
        return;
      }

      // Parse numeric values safely
      const points = parseInt(pointsValue);
      const order = parseInt(orderValue);

      // Validate numeric ranges
      if (points < 1 || points > 100) {
        alert('Points must be between 1 and 100');
        return;
      }

      if (order < 1) {
        alert('Order must be a positive number');
        return;
      }

      // Process hints
      const hints = hintsValue ? hintsValue.split('\n').filter(h => h.trim()).map(h => h.trim()) : [];

      // Create challenge data object
      const challengeData = {
        title: title,
        category: category,
        type: type,
        points: points,
        order: order,
        description: description,
        instructions: instructions,
        flag: flag,
        content: content,
        hints: hints,
        difficulty: this.getDifficultyFromCategory(category),
        updatedAt: serverTimestamp(),
        updatedBy: authManager.getCurrentUser().uid
      };

      console.log('🐺 Challenge update data prepared:', challengeData);

      // Update in Firebase
      const challengeRef = doc(db, CTF_CONFIG.COLLECTIONS.CHALLENGES, challengeId);
      await updateDoc(challengeRef, challengeData);

      console.log('🐺 Challenge updated successfully');
      alert('Challenge updated successfully!');

      // Close modal and refresh list
      const modal = document.querySelector('.fixed.inset-0');
      if (modal) {
        modal.remove();
      }

      await this.loadChallengesList();

    } catch (error) {
      console.error('🐺 Error updating challenge:', error);

      let errorMessage = 'Error updating challenge: ';

      if (error.message.includes('invalid data')) {
        errorMessage += 'Please ensure all required fields are filled correctly.';
      } else if (error.message.includes('permission')) {
        errorMessage += 'You do not have permission to update challenges.';
      } else {
        errorMessage += error.message;
      }

      alert(errorMessage);
    }
  }

  async deleteChallenge(challengeId) {
    const challenge = this.challenges.find(c => c.id === challengeId);
    if (!challenge) {
      alert('Challenge not found');
      return;
    }

    if (!confirm(`Are you sure you want to delete the challenge "${challenge.title}"?\n\nThis action cannot be undone and will affect all user progress.`)) {
      return;
    }

    try {
      await deleteDoc(doc(db, CTF_CONFIG.COLLECTIONS.CHALLENGES, challengeId));

      alert('Challenge deleted successfully!');
      await this.loadChallengesList();

    } catch (error) {
      console.error('Error deleting challenge:', error);
      alert('Error deleting challenge: ' + error.message);
    }
  }

  // Push Notification Methods
  async sendPushNotificationToAll() {
    console.log('🐺 Starting push notification to all users...');

    const title = document.getElementById('notification-title')?.value?.trim();
    const message = document.getElementById('notification-message')?.value?.trim();
    const type = document.getElementById('notification-type')?.value || 'general';

    // Validate inputs
    if (!title || !message) {
      alert('Please enter both title and message');
      return;
    }

    if (title.length > 100) {
      alert('Title must be 100 characters or less');
      return;
    }

    if (message.length > 500) {
      alert('Message must be 500 characters or less');
      return;
    }

    if (!confirm(`Are you sure you want to send this notification to ALL users?\n\nTitle: ${title}\nMessage: ${message}`)) {
      return;
    }

    try {
      // Check if push notification service is available
      if (!window.pushNotificationService) {
        throw new Error('Push notification service not available');
      }

      console.log('🐺 Sending notification with data:', { title, message, type });

      const result = await pushNotificationService.sendNotificationToAll(title, message, { type });

      console.log('🐺 Notification result:', result);

      if (result && result.success) {
        alert(`Notification sent successfully to ${result.count || 0} users!`);

        // Clear form
        document.getElementById('notification-title').value = '';
        document.getElementById('notification-message').value = '';

        // Log the action
        console.log('🐺 Admin broadcast notification sent:', { title, message, type, count: result.count });
      } else {
        const errorMsg = result?.error || 'Unknown error occurred';
        alert('Failed to send notification: ' + errorMsg);
      }
    } catch (error) {
      console.error('🐺 Error sending push notification:', error);

      let errorMessage = 'Error sending notification: ';

      if (error.message.includes('permission')) {
        errorMessage += 'Insufficient permissions. Please check Firestore security rules for push_subscriptions and notifications collections.';
      } else if (error.message.includes('not available')) {
        errorMessage += 'Push notification service is not properly initialized.';
      } else if (error.message.includes('admin')) {
        errorMessage += 'Admin privileges required for this operation.';
      } else {
        errorMessage += error.message;
      }

      alert(errorMessage);
    }
  }

  async sendTestNotification() {
    try {
      console.log('🐺 Sending test notification...');

      // Check if push notification service is available
      if (!window.pushNotificationService) {
        throw new Error('Push notification service not available');
      }

      // Check notification permission
      if (Notification.permission !== 'granted') {
        const permission = await Notification.requestPermission();
        if (permission !== 'granted') {
          throw new Error('Notification permission denied');
        }
      }

      await pushNotificationService.sendTestNotification();
      alert('Test notification sent! Check your browser notifications.');
      console.log('🐺 Test notification sent successfully');
    } catch (error) {
      console.error('🐺 Error sending test notification:', error);

      let errorMessage = 'Error sending test notification: ';

      if (error.message.includes('permission')) {
        errorMessage += 'Notification permission denied. Please allow notifications in your browser.';
      } else if (error.message.includes('not available')) {
        errorMessage += 'Push notification service is not properly initialized.';
      } else {
        errorMessage += error.message;
      }

      alert(errorMessage);
    }
  }

  async viewNotificationStats() {
    console.log('🐺 Loading notification statistics...');

    try {
      let subscriptions = [];
      let hasPermissionError = false;
      let errorMessage = '';

      // Try to get subscriptions with error handling
      try {
        if (window.pushNotificationService && typeof window.pushNotificationService.getAllSubscriptions === 'function') {
          subscriptions = await pushNotificationService.getAllSubscriptions();
        } else {
          console.warn('🐺 pushNotificationService not available, trying direct collection access');
          // Fallback: try to read from notifications collection directly
          const notificationsRef = collection(db, 'notifications');
          const notificationsSnap = await getDocs(notificationsRef);
          subscriptions = notificationsSnap.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            isActive: true // Assume active if in collection
          }));
        }
      } catch (permissionError) {
        console.warn('🐺 Permission error accessing notifications:', permissionError.message);
        hasPermissionError = true;
        errorMessage = permissionError.message;

        // Create placeholder data to show the UI works
        subscriptions = [
          { id: 'demo-1', userId: 'demo-user-1', isActive: true, createdAt: { toDate: () => new Date() } },
          { id: 'demo-2', userId: 'demo-user-2', isActive: false, createdAt: { toDate: () => new Date() } }
        ];
      }

      const activeSubscriptions = subscriptions.filter(sub => sub.isActive);

      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      modal.innerHTML = `
        <div class="neo-brutalist bg-white p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold">📱 Push Notification Statistics</h3>
            <button onclick="this.parentElement.parentElement.parentElement.remove()"
                    class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
              ✕
            </button>
          </div>

          ${hasPermissionError ? `
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-yellow-700">
                    <strong>Limited Access:</strong> Unable to access notification data due to permissions.
                    <br><small>Error: ${errorMessage}</small>
                  </p>
                  <p class="text-xs text-yellow-600 mt-1">
                    Showing demo data. Update Firestore security rules to access real notification data.
                  </p>
                </div>
              </div>
            </div>
          ` : ''}

          <div class="space-y-4">
            <!-- Stats Overview -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="neo-brutalist bg-blue-50 p-3 text-center">
                <div class="text-2xl font-bold text-blue-600">${subscriptions.length}</div>
                <div class="text-xs">${hasPermissionError ? 'Demo ' : ''}Total Subscriptions</div>
              </div>
              <div class="neo-brutalist bg-green-50 p-3 text-center">
                <div class="text-2xl font-bold text-green-600">${activeSubscriptions.length}</div>
                <div class="text-xs">Active Subscriptions</div>
              </div>
              <div class="neo-brutalist bg-yellow-50 p-3 text-center">
                <div class="text-2xl font-bold text-yellow-600">${subscriptions.length - activeSubscriptions.length}</div>
                <div class="text-xs">Inactive</div>
              </div>
              <div class="neo-brutalist bg-purple-50 p-3 text-center">
                <div class="text-2xl font-bold text-purple-600">${subscriptions.length > 0 ? Math.round((activeSubscriptions.length / subscriptions.length) * 100) : 0}%</div>
                <div class="text-xs">Active Rate</div>
              </div>
            </div>

            ${subscriptions.length > 0 ? `
              <!-- Recent Subscriptions -->
              <div>
                <h4 class="font-bold mb-2">Recent Subscriptions:</h4>
                <div class="max-h-40 overflow-y-auto">
                  ${subscriptions.slice(0, 10).map(sub => `
                    <div class="text-sm border-b border-gray-200 py-2 flex justify-between">
                      <span>User: ${sub.userId ? (sub.userId.length > 12 ? sub.userId.substring(0, 8) + '...' : sub.userId) : 'Anonymous'}</span>
                      <span class="${sub.isActive ? 'text-green-600' : 'text-red-600'}">
                        ${sub.isActive ? '✅ Active' : '❌ Inactive'}
                      </span>
                    </div>
                  `).join('')}
                </div>
              </div>
            ` : `
              <div class="text-center py-8">
                <div class="text-4xl mb-2">📱</div>
                <div class="text-gray-500 mb-2">No notification subscriptions found</div>
                <div class="text-sm text-gray-400">Users need to enable notifications to appear here</div>
              </div>
            `}

            <!-- Actions -->
            <div class="flex space-x-2">
              ${!hasPermissionError ? `
                <button onclick="adminManager.exportNotificationData()"
                        class="neo-brutalist bg-blue-500 text-white px-4 py-2 text-sm font-bold">
                  EXPORT DATA
                </button>
                <button onclick="adminManager.cleanupInactiveSubscriptions()"
                        class="neo-brutalist bg-red-500 text-white px-4 py-2 text-sm font-bold">
                  CLEANUP INACTIVE
                </button>
              ` : `
                <div class="text-center w-full">
                  <p class="text-sm text-gray-600 mb-2">Limited functionality due to permissions</p>
                  <button onclick="this.parentElement.parentElement.parentElement.parentElement.parentElement.remove()"
                          class="neo-brutalist bg-gray-500 text-white px-4 py-2 text-sm font-bold">
                    CLOSE
                  </button>
                </div>
              `}
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      console.log('🐺 Notification statistics modal displayed');

    } catch (error) {
      console.error('🐺 Critical error getting notification stats:', error);

      // Show user-friendly error modal instead of alert
      const errorModal = document.createElement('div');
      errorModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      errorModal.innerHTML = `
        <div class="neo-brutalist bg-white p-6 max-w-md w-full mx-4">
          <div class="text-center">
            <div class="text-4xl mb-4 text-red-500">⚠️</div>
            <h3 class="text-xl font-bold mb-2 text-red-600">Notification Stats Error</h3>
            <p class="text-gray-600 mb-4">Unable to load notification statistics</p>
            <div class="bg-red-50 border-l-4 border-red-400 p-3 mb-4 text-left">
              <p class="text-sm text-red-700"><strong>Error:</strong> ${error.message}</p>
              ${error.message.includes('permission') ? `
                <p class="text-xs text-red-600 mt-2">
                  This usually means Firestore security rules need to be updated to allow admin access to the notifications collection.
                </p>
              ` : ''}
            </div>
            <div class="flex space-x-2">
              <button onclick="adminManager.viewNotificationStats()"
                      class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold flex-1">
                RETRY
              </button>
              <button onclick="this.parentElement.parentElement.parentElement.remove()"
                      class="neo-brutalist bg-gray-500 text-white px-4 py-2 font-bold flex-1">
                CLOSE
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(errorModal);
    }
  }

  async exportNotificationData() {
    try {
      let subscriptions = [];

      try {
        if (window.pushNotificationService && typeof window.pushNotificationService.getAllSubscriptions === 'function') {
          subscriptions = await pushNotificationService.getAllSubscriptions();
        } else {
          console.warn('🐺 pushNotificationService not available, trying direct collection access');
          const notificationsRef = collection(db, 'notifications');
          const notificationsSnap = await getDocs(notificationsRef);
          subscriptions = notificationsSnap.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            isActive: true
          }));
        }
      } catch (permissionError) {
        console.warn('🐺 Permission error, creating demo export:', permissionError.message);
        alert('Unable to access notification data due to permissions. Creating demo export instead.');
        subscriptions = [
          { userId: 'demo-user-1', isActive: true, createdAt: new Date().toISOString(), endpoint: 'demo' },
          { userId: 'demo-user-2', isActive: false, createdAt: new Date().toISOString(), endpoint: 'demo' }
        ];
      }

      const data = {
        exportedAt: new Date().toISOString(),
        totalSubscriptions: subscriptions.length,
        activeSubscriptions: subscriptions.filter(sub => sub.isActive).length,
        note: subscriptions.length <= 2 ? 'Demo data due to permission restrictions' : 'Real notification data',
        subscriptions: subscriptions.map(sub => ({
          userId: sub.userId,
          isActive: sub.isActive,
          createdAt: sub.createdAt,
          endpoint: sub.endpoint ? 'present' : 'missing'
        }))
      };

      const jsonContent = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `notification_data_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Notification data exported successfully!');
    } catch (error) {
      console.error('🐺 Error exporting notification data:', error);
      alert('Error exporting data: ' + error.message);
    }
  }

  async cleanupInactiveSubscriptions() {
    if (!confirm('Are you sure you want to remove all inactive push notification subscriptions?')) {
      return;
    }

    try {
      // This would typically be implemented server-side
      alert('Cleanup functionality would be implemented server-side for security.');
    } catch (error) {
      console.error('🐺 Error cleaning up subscriptions:', error);
      alert('Error during cleanup: ' + error.message);
    }
  }

  async resetLeaderboard() {
    if (!confirm('Are you sure you want to reset the entire leaderboard? This will reset all user scores to 0. This action cannot be undone!')) {
      return;
    }

    const confirmText = prompt('Type "RESET LEADERBOARD" to confirm this action:');
    if (confirmText !== 'RESET LEADERBOARD') {
      alert('Reset cancelled - confirmation text did not match.');
      return;
    }

    try {
      // Reset all user scores
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const usersSnap = await getDocs(usersRef);

      const batch = writeBatch(db);
      let resetCount = 0;

      usersSnap.docs.forEach(doc => {
        const userData = doc.data();
        if (userData.role === CTF_CONFIG.USER_ROLES.PARTICIPANT) {
          batch.update(doc.ref, {
            score: 0,
            challengesSolved: 0,
            solvedChallenges: [],
            progress: {
              beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
              intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
              advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
            },
            rank: null,
            lastActivity: serverTimestamp()
          });
          resetCount++;
        }
      });

      await batch.commit();

      // Send notification to all users about the reset
      await pushNotificationService.sendNotificationToAll(
        '🔄 Leaderboard Reset',
        'The leaderboard has been reset by an administrator. All scores are now back to zero. Good luck!',
        { type: 'admin_message' }
      );

      alert(`Leaderboard reset successfully! ${resetCount} user scores have been reset to 0.`);

      // Refresh the users list if it's currently displayed
      if (this.currentView === 'users') {
        await this.loadUsersList();
      }

    } catch (error) {
      console.error('🐺 Error resetting leaderboard:', error);
      alert('Error resetting leaderboard: ' + error.message);
    }
  }

  async showBulkScoreModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">📊 Bulk Score Adjustment</h3>
          <button onclick="this.parentElement.parentElement.parentElement.remove()"
                  class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
            ✕
          </button>
        </div>

        <form id="bulk-score-form" class="space-y-4">
          <div>
            <label class="block text-sm font-bold mb-2">Target Users</label>
            <select id="bulk-target" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
              <option value="all">All Users</option>
              <option value="participants">Participants Only</option>
              <option value="top10">Top 10 Users</option>
              <option value="bottom10">Bottom 10 Users</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Score Adjustment</label>
            <input type="number" id="bulk-score-adjustment" required
                   class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                   placeholder="Enter positive or negative number">
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Reason</label>
            <textarea id="bulk-adjustment-reason" required
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-20"
                      placeholder="Reason for bulk adjustment..."></textarea>
          </div>

          <div class="flex space-x-2">
            <button type="submit"
                    class="neo-brutalist bg-purple-500 text-white px-4 py-2 font-bold flex-1">
              APPLY BULK ADJUSTMENT
            </button>
            <button type="button" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()"
                    class="neo-brutalist bg-gray-500 text-white px-4 py-2 font-bold flex-1">
              CANCEL
            </button>
          </div>
        </form>
      </div>
    `;

    document.body.appendChild(modal);

    // Handle form submission
    const form = document.getElementById('bulk-score-form');
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      await this.handleBulkScoreAdjustment();
    });
  }

  async handleBulkScoreAdjustment() {
    try {
      const target = document.getElementById('bulk-target').value;
      const adjustment = parseInt(document.getElementById('bulk-score-adjustment').value);
      const reason = document.getElementById('bulk-adjustment-reason').value;

      if (isNaN(adjustment) || adjustment === 0) {
        alert('Please enter a valid score adjustment');
        return;
      }

      if (!reason.trim()) {
        alert('Please provide a reason for the adjustment');
        return;
      }

      let targetUsers = [];

      switch (target) {
        case 'all':
          targetUsers = this.users.filter(u => u.role !== 'admin');
          break;
        case 'participants':
          targetUsers = this.users.filter(u => u.role === 'participant');
          break;
        case 'top10':
          targetUsers = this.users
            .filter(u => u.role !== 'admin')
            .sort((a, b) => (b.score || 0) - (a.score || 0))
            .slice(0, 10);
          break;
        case 'bottom10':
          targetUsers = this.users
            .filter(u => u.role !== 'admin')
            .sort((a, b) => (a.score || 0) - (b.score || 0))
            .slice(0, 10);
          break;
      }

      if (targetUsers.length === 0) {
        alert('No users found for the selected target');
        return;
      }

      if (!confirm(`Apply ${adjustment > 0 ? '+' : ''}${adjustment} points to ${targetUsers.length} users?\n\nReason: ${reason}`)) {
        return;
      }

      const currentUser = authManager.getCurrentUser();

      // Apply adjustments (using a simple approach since scoreService might not be available)
      for (const user of targetUsers) {
        try {
          const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.id);
          const newScore = Math.max(0, (user.score || 0) + adjustment);
          await updateDoc(userRef, {
            score: newScore,
            lastActivity: serverTimestamp()
          });
        } catch (error) {
          console.error(`Error updating user ${user.id}:`, error);
        }
      }

      alert(`Bulk score adjustment applied to ${targetUsers.length} users successfully!`);

      // Close modal and refresh
      document.querySelector('.fixed.inset-0').remove();
      await this.loadUsersList();

    } catch (error) {
      console.error('Error applying bulk score adjustment:', error);
      alert('Error applying bulk adjustment: ' + error.message);
    }
  }

  async exportData() {
    try {
      // Load all data if not already loaded
      if (this.users.length === 0) await this.loadUsersList();
      if (this.challenges.length === 0) await this.loadChallengesList();
      if (this.submissions.length === 0) await this.loadSubmissionsList();

      const exportData = {
        timestamp: new Date().toISOString(),
        users: this.users,
        challenges: this.challenges,
        submissions: this.submissions,
        statistics: {
          totalUsers: this.users.length,
          totalChallenges: this.challenges.length,
          totalSubmissions: this.submissions.length,
          activeUsers: this.users.filter(u =>
            u.lastActivity && (new Date() - u.lastActivity.toDate()) < 86400000
          ).length
        }
      };

      const jsonContent = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `ctf_full_export_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Full data export completed successfully!');
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Error exporting data: ' + error.message);
    }
  }

  // Method to refresh admin panel when auth state changes
  refreshAdminAccess() {
    console.log('🐺 Refreshing admin access...');
    this.updateAdminVisibility();

    // If admin panel is currently visible, reload it
    const adminSection = document.getElementById('admin-section');
    if (adminSection && !adminSection.classList.contains('hidden')) {
      this.loadAdminPanel();
    }
  }

  // Force show admin panel (for debugging)
  forceShowAdminPanel() {
    console.log('🐺 Force showing admin panel...');
    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
      adminTab.classList.remove('hidden');
      adminTab.click();
    }
  }

  // Score Management View
  async loadScoreManagement() {
    const content = document.getElementById('admin-view-content');
    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">🏆 Score Management</h3>
          <button onclick="adminManager.showBulkScoreUpdate()" class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold">
            📊 BULK UPDATE
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="neo-brutalist bg-blue-50 p-4">
            <h4 class="font-bold mb-2">🎯 Quick Score Actions</h4>
            <div class="space-y-2">
              <button onclick="adminManager.addBonusPoints()" class="w-full neo-brutalist bg-green-500 text-white py-2 text-sm font-bold">
                ➕ ADD BONUS POINTS
              </button>
              <button onclick="adminManager.resetUserScore()" class="w-full neo-brutalist bg-yellow-500 text-white py-2 text-sm font-bold">
                🔄 RESET USER SCORE
              </button>
              <button onclick="adminManager.adjustScores()" class="w-full neo-brutalist bg-purple-500 text-white py-2 text-sm font-bold">
                ⚖️ ADJUST SCORES
              </button>
              <button onclick="adminManager.showAdvancedScoreManager()" class="w-full neo-brutalist bg-blue-600 text-white py-2 text-sm font-bold">
                🎯 ADVANCED MANAGER
              </button>
              <button onclick="adminManager.exportScores()" class="w-full neo-brutalist bg-gray-600 text-white py-2 text-sm font-bold">
                📊 EXPORT SCORES
              </button>
              <button onclick="adminManager.testScoreUpdate()" class="w-full neo-brutalist bg-orange-500 text-white py-2 text-sm font-bold">
                🧪 TEST SCORE UPDATE
              </button>
              <button onclick="adminManager.quickScoreUpdate()" class="w-full neo-brutalist bg-red-500 text-white py-2 text-sm font-bold">
                ⚡ QUICK UPDATE
              </button>
            </div>
          </div>

          <div class="neo-brutalist bg-green-50 p-4">
            <h4 class="font-bold mb-2">📈 Score Statistics</h4>
            <div id="score-stats">Loading...</div>
          </div>

          <div class="neo-brutalist bg-yellow-50 p-4">
            <h4 class="font-bold mb-2">🏆 Top Performers</h4>
            <div id="top-performers">Loading...</div>
          </div>
        </div>

        <div class="neo-brutalist bg-white p-4">
          <h4 class="font-bold mb-4">👥 User Scores</h4>
          <div id="user-scores-table">Loading user scores...</div>
        </div>
      </div>
    `;

    this.loadScoreStats();
    this.loadUserScoresTable();
  }

  // Analytics View
  async loadAnalytics() {
    const content = document.getElementById('admin-view-content');
    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">📈 Analytics Dashboard</h3>
          <button onclick="adminManager.refreshAnalytics()" class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
            🔄 REFRESH
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="neo-brutalist bg-blue-100 p-4 text-center">
            <div class="text-3xl font-bold text-blue-600" id="total-users-stat">0</div>
            <div class="text-sm">Total Users</div>
          </div>
          <div class="neo-brutalist bg-green-100 p-4 text-center">
            <div class="text-3xl font-bold text-green-600" id="active-users-stat">0</div>
            <div class="text-sm">Active Users</div>
          </div>
          <div class="neo-brutalist bg-yellow-100 p-4 text-center">
            <div class="text-3xl font-bold text-yellow-600" id="total-challenges-stat">0</div>
            <div class="text-sm">Total Challenges</div>
          </div>
          <div class="neo-brutalist bg-purple-100 p-4 text-center">
            <div class="text-3xl font-bold text-purple-600" id="total-submissions-stat">0</div>
            <div class="text-sm">Total Submissions</div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="neo-brutalist bg-white p-4">
            <h4 class="font-bold mb-4">📊 User Activity</h4>
            <div id="user-activity-chart">Loading activity data...</div>
          </div>
          <div class="neo-brutalist bg-white p-4">
            <h4 class="font-bold mb-4">🎯 Challenge Statistics</h4>
            <div id="challenge-stats-chart">Loading challenge data...</div>
          </div>
        </div>

        <div class="neo-brutalist bg-white p-4">
          <h4 class="font-bold mb-4">📈 Performance Trends</h4>
          <div id="performance-trends">Loading trends...</div>
        </div>
      </div>
    `;

    this.loadAnalyticsData();
  }

  // Notifications View
  async loadNotifications() {
    const content = document.getElementById('admin-view-content');
    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">🔔 Notification Center</h3>
          <div class="space-x-2">
            <button onclick="window.adminManager.sendBroadcastNotification()" class="neo-brutalist bg-red-500 text-white px-4 py-2 font-bold">
              📢 BROADCAST
            </button>
            <button onclick="window.adminManager.sendTestNotification()" class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              🧪 TEST
            </button>
          </div>
        </div>

        <!-- Notification Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="neo-brutalist bg-blue-50 p-4 text-center">
            <div class="text-2xl font-bold text-blue-600" id="total-notifications-sent">0</div>
            <div class="text-sm">Total Sent</div>
          </div>
          <div class="neo-brutalist bg-green-50 p-4 text-center">
            <div class="text-2xl font-bold text-green-600" id="active-users-count">0</div>
            <div class="text-sm">Active Users</div>
          </div>
          <div class="neo-brutalist bg-yellow-50 p-4 text-center">
            <div class="text-2xl font-bold text-yellow-600" id="email-notifications-sent">0</div>
            <div class="text-sm">Email Notifications</div>
          </div>
          <div class="neo-brutalist bg-purple-50 p-4 text-center">
            <div class="text-2xl font-bold text-purple-600" id="in-app-notifications">0</div>
            <div class="text-sm">In-App Notifications</div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="neo-brutalist bg-blue-50 p-4">
            <h4 class="font-bold mb-4">📤 Send Notification</h4>
            <div class="space-y-3">
              <input type="text" id="notification-title" placeholder="Notification Title"
                     class="w-full p-2 border-2 border-gray-300">
              <textarea id="notification-message" placeholder="Notification Message"
                        class="w-full p-2 border-2 border-gray-300 h-20"></textarea>
              <select id="notification-type" class="w-full p-2 border-2 border-gray-300">
                <option value="info">📢 Information</option>
                <option value="success">✅ Success</option>
                <option value="warning">⚠️ Warning</option>
                <option value="error">❌ Error</option>
                <option value="announcement">📣 Announcement</option>
                <option value="challenge">🎯 New Challenge</option>
              </select>
              <select id="notification-target" class="w-full p-2 border-2 border-gray-300">
                <option value="all">📢 All Users</option>
                <option value="active">🟢 Active Users Only</option>
                <option value="top10">🏆 Top 10 Users</option>
                <option value="email">📧 Email Notification</option>
              </select>
              <button onclick="window.adminManager.sendNotification()"
                      class="w-full neo-brutalist bg-green-500 text-white py-2 font-bold">
                📤 SEND NOTIFICATION
              </button>
            </div>
          </div>

          <div class="neo-brutalist bg-green-50 p-4">
            <h4 class="font-bold mb-4">📋 Notification Templates</h4>
            <div class="space-y-2">
              <button onclick="window.adminManager.useTemplate('welcome')"
                      class="w-full neo-brutalist bg-blue-500 text-white py-2 text-sm font-bold">
                👋 Welcome Message
              </button>
              <button onclick="window.adminManager.useTemplate('challenge')"
                      class="w-full neo-brutalist bg-green-500 text-white py-2 text-sm font-bold">
                🎯 New Challenge Alert
              </button>
              <button onclick="window.adminManager.useTemplate('maintenance')"
                      class="w-full neo-brutalist bg-yellow-500 text-white py-2 text-sm font-bold">
                🔧 Maintenance Notice
              </button>
              <button onclick="window.adminManager.useTemplate('winner')"
                      class="w-full neo-brutalist bg-purple-500 text-white py-2 text-sm font-bold">
                🏆 Winner Announcement
              </button>
            </div>

            <div class="mt-4">
              <h5 class="font-bold mb-2">📋 Recent Notifications</h5>
              <div id="recent-notifications" class="text-sm">Loading...</div>
            </div>
          </div>
        </div>

        <!-- Notification History -->
        <div class="neo-brutalist bg-white p-4">
          <h4 class="font-bold mb-4">📜 Notification History</h4>
          <div id="notification-history">
            <div class="text-center py-4 text-gray-500">Loading notification history...</div>
          </div>
        </div>
      </div>
    `;

    this.loadRecentNotifications();
    this.updateNotificationStats();
  }

  // Helper methods for new functionality
  async loadScoreStats() {
    try {
      if (this.users.length === 0) await this.loadUsersList();

      const totalScore = this.users.reduce((sum, user) => sum + (user.score || 0), 0);
      const avgScore = this.users.length > 0 ? (totalScore / this.users.length).toFixed(1) : 0;
      const highestScore = Math.max(...this.users.map(u => u.score || 0));

      document.getElementById('score-stats').innerHTML = `
        <div class="space-y-2 text-sm">
          <div>Total Score: <strong>${totalScore}</strong></div>
          <div>Average Score: <strong>${avgScore}</strong></div>
          <div>Highest Score: <strong>${highestScore}</strong></div>
          <div>Users with Score: <strong>${this.users.filter(u => u.score > 0).length}</strong></div>
        </div>
      `;

      // Top performers
      const topUsers = this.users
        .filter(u => u.score > 0)
        .sort((a, b) => (b.score || 0) - (a.score || 0))
        .slice(0, 5);

      document.getElementById('top-performers').innerHTML = `
        <div class="space-y-1 text-sm">
          ${topUsers.map((user, index) => `
            <div class="flex justify-between">
              <span>${index + 1}. ${user.displayName || user.email}</span>
              <strong>${user.score}</strong>
            </div>
          `).join('')}
        </div>
      `;
    } catch (error) {
      console.error('Error loading score stats:', error);
    }
  }

  async loadUserScoresTable() {
    try {
      if (this.users.length === 0) await this.loadUsersList();

      const sortedUsers = this.users.sort((a, b) => (b.score || 0) - (a.score || 0));

      document.getElementById('user-scores-table').innerHTML = `
        <div class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="bg-gray-100">
                <th class="p-2 text-left">Rank</th>
                <th class="p-2 text-left">User</th>
                <th class="p-2 text-left">Score</th>
                <th class="p-2 text-left">Challenges</th>
                <th class="p-2 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              ${sortedUsers.map((user, index) => `
                <tr class="border-b">
                  <td class="p-2">#${index + 1}</td>
                  <td class="p-2">${user.displayName || user.email}</td>
                  <td class="p-2"><strong>${user.score || 0}</strong></td>
                  <td class="p-2">${user.challengesSolved || 0}</td>
                  <td class="p-2">
                    <button onclick="adminManager.editUserScore('${user.id}')"
                            class="neo-brutalist bg-blue-500 text-white px-2 py-1 text-xs mr-1">
                      ✏️ EDIT
                    </button>
                    <button onclick="adminManager.directScoreEdit('${user.id}')"
                            class="neo-brutalist bg-red-500 text-white px-2 py-1 text-xs">
                      ⚡ DIRECT
                    </button>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    } catch (error) {
      console.error('Error loading user scores table:', error);
    }
  }

  async loadAnalyticsData() {
    try {
      if (this.users.length === 0) await this.loadUsersList();
      if (this.challenges.length === 0) await this.loadChallengesList();
      if (this.submissions.length === 0) await this.loadSubmissionsList();

      // Update statistics
      document.getElementById('total-users-stat').textContent = this.users.length;
      document.getElementById('active-users-stat').textContent = this.users.filter(u =>
        u.lastActivity && (new Date() - u.lastActivity.toDate()) < 86400000
      ).length;
      document.getElementById('total-challenges-stat').textContent = this.challenges.length;
      document.getElementById('total-submissions-stat').textContent = this.submissions.length;

      // User activity chart (simple text representation)
      const activityData = this.users.reduce((acc, user) => {
        const lastActive = user.lastActivity ? user.lastActivity.toDate() : null;
        if (lastActive) {
          const daysAgo = Math.floor((new Date() - lastActive) / (1000 * 60 * 60 * 24));
          if (daysAgo <= 7) acc.week++;
          else if (daysAgo <= 30) acc.month++;
          else acc.older++;
        } else {
          acc.never++;
        }
        return acc;
      }, { week: 0, month: 0, older: 0, never: 0 });

      document.getElementById('user-activity-chart').innerHTML = `
        <div class="space-y-2 text-sm">
          <div>Last 7 days: <strong>${activityData.week}</strong></div>
          <div>Last 30 days: <strong>${activityData.month}</strong></div>
          <div>Older: <strong>${activityData.older}</strong></div>
          <div>Never active: <strong>${activityData.never}</strong></div>
        </div>
      `;

      // Challenge statistics
      const challengeStats = this.challenges.reduce((acc, challenge) => {
        acc[challenge.category] = (acc[challenge.category] || 0) + 1;
        return acc;
      }, {});

      document.getElementById('challenge-stats-chart').innerHTML = `
        <div class="space-y-2 text-sm">
          ${Object.entries(challengeStats).map(([category, count]) => `
            <div>${category}: <strong>${count}</strong></div>
          `).join('')}
        </div>
      `;

    } catch (error) {
      console.error('Error loading analytics data:', error);
    }
  }

  async loadRecentNotifications() {
    const history = this.getNotificationHistory();
    const recentNotificationsEl = document.getElementById('recent-notifications');
    const notificationHistoryEl = document.getElementById('notification-history');

    if (history.length === 0) {
      if (recentNotificationsEl) {
        recentNotificationsEl.innerHTML = `
          <div class="text-center py-4 text-gray-500">
            <div>📭 No notifications sent yet</div>
            <div class="text-xs mt-1">Send your first notification above!</div>
          </div>
        `;
      }
      if (notificationHistoryEl) {
        notificationHistoryEl.innerHTML = `
          <div class="text-center py-4 text-gray-500">No notification history available</div>
        `;
      }
      return;
    }

    // Recent notifications (last 3)
    if (recentNotificationsEl) {
      const recent = history.slice(0, 3);
      recentNotificationsEl.innerHTML = `
        <div class="space-y-2 text-sm">
          ${recent.map(notification => `
            <div class="p-2 ${this.getNotificationBgColor(notification.type)} rounded">
              <div class="font-bold">${notification.title}</div>
              <div class="text-xs text-gray-600">${this.getTimeAgo(notification.sentAt)}</div>
              <div class="text-xs text-gray-500">${notification.targetCount} recipients</div>
            </div>
          `).join('')}
        </div>
      `;
    }

    // Full notification history
    if (notificationHistoryEl) {
      notificationHistoryEl.innerHTML = `
        <div class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="bg-gray-100">
                <th class="p-2 text-left">Title</th>
                <th class="p-2 text-left">Type</th>
                <th class="p-2 text-left">Target</th>
                <th class="p-2 text-left">Recipients</th>
                <th class="p-2 text-left">Sent At</th>
                <th class="p-2 text-left">Sent By</th>
              </tr>
            </thead>
            <tbody>
              ${history.map(notification => `
                <tr class="border-b">
                  <td class="p-2 font-medium">${notification.title}</td>
                  <td class="p-2">
                    <span class="px-2 py-1 text-xs rounded ${this.getNotificationBadgeColor(notification.type)}">
                      ${notification.type.toUpperCase()}
                    </span>
                  </td>
                  <td class="p-2">${notification.target.toUpperCase()}</td>
                  <td class="p-2">${notification.targetCount}</td>
                  <td class="p-2">${new Date(notification.sentAt).toLocaleString()}</td>
                  <td class="p-2">${notification.sentBy}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    }
  }

  getNotificationBgColor(type) {
    const colors = {
      info: 'bg-blue-100',
      success: 'bg-green-100',
      warning: 'bg-yellow-100',
      error: 'bg-red-100',
      announcement: 'bg-purple-100',
      challenge: 'bg-indigo-100'
    };
    return colors[type] || 'bg-gray-100';
  }

  getNotificationBadgeColor(type) {
    const colors = {
      info: 'bg-blue-100 text-blue-800',
      success: 'bg-green-100 text-green-800',
      warning: 'bg-yellow-100 text-yellow-800',
      error: 'bg-red-100 text-red-800',
      announcement: 'bg-purple-100 text-purple-800',
      challenge: 'bg-indigo-100 text-indigo-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  }

  getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    return `${diffDays} days ago`;
  }

  // Quick action methods
  async createNewChallenge() {
    this.switchAdminView('challenges');
    setTimeout(() => {
      this.showCreateChallengeModal();
    }, 500);
  }

  async showScoreManager() {
    this.switchAdminView('scores');
  }

  async showUserStats() {
    this.switchAdminView('analytics');
  }

  // Enhanced Score Service - Robust score updating with better error handling
  async updateUserScore(userId, newScore, operation = 'update') {
    try {
      console.log(`🏆 ${operation} score for user ${userId} to ${newScore}`);

      // Validate inputs
      if (!userId) throw new Error('User ID is required');
      if (typeof newScore !== 'number' || newScore < 0) throw new Error('Invalid score value');

      let firebaseSuccess = false;
      let localStorageSuccess = false;

      // Try Firebase with enhanced error handling
      if (window.db && window.CTF_CONFIG) {
        try {
          console.log(`🔥 Attempting Firebase update for user ${userId}...`);

          // First, verify the user exists
          const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);

          // Try different update approaches
          try {
            // Method 1: Standard updateDoc
            await updateDoc(userRef, {
              score: newScore,
              lastUpdated: serverTimestamp(),
              scoreUpdatedBy: 'admin',
              scoreUpdateReason: operation
            });
            console.log(`✅ Firebase updateDoc successful for user ${userId}`);
            firebaseSuccess = true;
          } catch (updateError) {
            console.warn(`⚠️ updateDoc failed, trying setDoc merge:`, updateError);

            // Method 2: setDoc with merge
            await setDoc(userRef, {
              score: newScore,
              lastUpdated: serverTimestamp(),
              scoreUpdatedBy: 'admin',
              scoreUpdateReason: operation
            }, { merge: true });
            console.log(`✅ Firebase setDoc merge successful for user ${userId}`);
            firebaseSuccess = true;
          }
        } catch (firebaseError) {
          console.error(`❌ All Firebase methods failed for user ${userId}:`, firebaseError);
          console.error('Firebase error details:', {
            code: firebaseError.code,
            message: firebaseError.message,
            userId: userId,
            newScore: newScore
          });
        }
      }

      // Always update localStorage as backup/fallback
      try {
        console.log(`💾 Updating localStorage for user ${userId}...`);

        // Get current users from localStorage
        let users = JSON.parse(localStorage.getItem('ctf_users') || '[]');

        // Find user index
        const userIndex = users.findIndex(u => u.id === userId);

        if (userIndex === -1) {
          // User not found in localStorage, try to add from current users array
          const currentUser = this.users.find(u => u.id === userId);
          if (currentUser) {
            users.push({
              ...currentUser,
              score: newScore,
              lastUpdated: new Date().toISOString(),
              scoreUpdatedBy: 'admin',
              scoreUpdateReason: operation
            });
            console.log(`➕ Added user ${userId} to localStorage with new score`);
          } else {
            throw new Error(`User ${userId} not found in localStorage or current users array`);
          }
        } else {
          // Update existing user
          users[userIndex].score = newScore;
          users[userIndex].lastUpdated = new Date().toISOString();
          users[userIndex].scoreUpdatedBy = 'admin';
          users[userIndex].scoreUpdateReason = operation;
          console.log(`📝 Updated existing user ${userId} in localStorage`);
        }

        // Save back to localStorage
        localStorage.setItem('ctf_users', JSON.stringify(users));
        console.log(`✅ localStorage update successful for user ${userId}`);
        localStorageSuccess = true;

        // Also update the current users array in memory
        const memoryUserIndex = this.users.findIndex(u => u.id === userId);
        if (memoryUserIndex !== -1) {
          this.users[memoryUserIndex].score = newScore;
          this.users[memoryUserIndex].lastUpdated = new Date().toISOString();
          console.log(`🧠 Updated user ${userId} in memory array`);
        }

      } catch (localStorageError) {
        console.error(`❌ localStorage update failed for user ${userId}:`, localStorageError);
      }

      // Check if at least one method succeeded
      if (firebaseSuccess || localStorageSuccess) {
        console.log(`✅ Score update completed for user ${userId} (Firebase: ${firebaseSuccess}, localStorage: ${localStorageSuccess})`);
        return true;
      } else {
        throw new Error(`Both Firebase and localStorage updates failed for user ${userId}`);
      }

    } catch (error) {
      console.error(`❌ Critical error updating score for user ${userId}:`, error);
      throw error;
    }
  }

  // Score management actions
  async addBonusPoints() {
    try {
      const points = prompt('Enter bonus points to add to all users:');
      if (!points || isNaN(points)) {
        alert('Please enter a valid number of points.');
        return;
      }

      const bonusPoints = parseInt(points);
      if (bonusPoints <= 0) {
        alert('Please enter a positive number of points.');
        return;
      }

      // Load users if not already loaded
      if (this.users.length === 0) await this.loadUsersList();

      const confirmed = confirm(`Add ${bonusPoints} bonus points to all ${this.users.length} users?\n\nThis operation may take a few moments...`);
      if (!confirmed) return;

      // Show progress
      const progressMsg = `Adding ${bonusPoints} points to ${this.users.length} users...`;
      console.log(`🏆 ${progressMsg}`);

      let updatedCount = 0;
      const errors = [];
      const results = [];

      // Update each user's score with enhanced error handling
      for (let i = 0; i < this.users.length; i++) {
        const user = this.users[i];
        try {
          const currentScore = user.score || 0;
          const newScore = currentScore + bonusPoints;

          await this.updateUserScore(user.id, newScore, 'bonus_points');

          // Update local cache
          user.score = newScore;

          results.push({
            user: user.displayName || user.email,
            oldScore: currentScore,
            newScore: newScore
          });

          updatedCount++;

          // Progress feedback for large operations
          if (this.users.length > 10 && (i + 1) % 5 === 0) {
            console.log(`🏆 Progress: ${i + 1}/${this.users.length} users updated`);
          }

        } catch (error) {
          console.error(`Error updating score for user ${user.email}:`, error);
          errors.push(`${user.displayName || user.email}: ${error.message}`);
        }
      }

      // Show detailed results
      let message = `✅ Successfully added ${bonusPoints} bonus points!\n\n`;
      message += `📊 Results:\n`;
      message += `• Updated: ${updatedCount}/${this.users.length} users\n`;
      message += `• Errors: ${errors.length}\n\n`;

      // Show sample results
      if (results.length > 0) {
        message += `Sample updates:\n`;
        results.slice(0, 3).forEach(result => {
          message += `${result.user}: ${result.oldScore} → ${result.newScore} (+${bonusPoints})\n`;
        });
        if (results.length > 3) {
          message += `... and ${results.length - 3} more users\n`;
        }
      }

      if (errors.length > 0) {
        message += `\n⚠️ Errors:\n${errors.slice(0, 2).join('\n')}`;
        if (errors.length > 2) {
          message += `\n... and ${errors.length - 2} more errors`;
        }
      }

      alert(message);

      // Force refresh all displays
      console.log('🔄 Refreshing displays...');
      await this.loadUsersList();
      await this.loadUserScoresTable();
      await this.loadScoreStats();

    } catch (error) {
      console.error('Error adding bonus points:', error);
      alert(`❌ Error adding bonus points: ${error.message}\n\nPlease try again or check the console for details.`);
    }
  }

  async resetUserScore() {
    try {
      const email = prompt('Enter user email to reset score:');
      if (!email || !email.trim()) {
        alert('Please enter a valid email address.');
        return;
      }

      // Load users if not already loaded
      if (this.users.length === 0) await this.loadUsersList();

      // Find user by email (case-insensitive)
      const user = this.users.find(u => u.email && u.email.toLowerCase() === email.toLowerCase().trim());
      if (!user) {
        alert(`❌ User with email "${email}" not found.\n\nPlease check the email address and try again.`);
        return;
      }

      const currentScore = user.score || 0;
      const confirmed = confirm(`Reset score for ${user.displayName || user.email}?\n\nCurrent score: ${currentScore}\nNew score: 0\n\nThis action cannot be undone.`);
      if (!confirmed) return;

      try {
        console.log(`🔄 Resetting score for user: ${user.email}`);

        // Use the robust updateUserScore method
        await this.updateUserScore(user.id, 0, 'admin_reset');

        // Update local cache
        user.score = 0;

        alert(`✅ Score reset successfully!\n\n${user.displayName || user.email}: ${currentScore} → 0 points`);

        // Force refresh displays
        console.log('🔄 Refreshing displays after score reset...');
        await this.loadUsersList();
        await this.loadUserScoresTable();
        await this.loadScoreStats();

      } catch (error) {
        console.error('Error resetting user score:', error);
        alert(`❌ Error resetting score: ${error.message}\n\nPlease try again or check the console for details.`);
      }

    } catch (error) {
      console.error('Error in resetUserScore:', error);
      alert(`❌ Error: ${error.message}`);
    }
  }

  async adjustScores() {
    try {
      const adjustment = prompt('Enter score adjustment (positive or negative):');
      if (!adjustment || isNaN(adjustment)) {
        alert('Please enter a valid number (positive or negative).');
        return;
      }

      const scoreAdjustment = parseInt(adjustment);
      if (scoreAdjustment === 0) {
        alert('Score adjustment cannot be zero.');
        return;
      }

      // Load users if not already loaded
      if (this.users.length === 0) await this.loadUsersList();

      const usersWithScores = this.users.filter(u => (u.score || 0) > 0);
      const action = scoreAdjustment > 0 ? 'add' : 'subtract';
      const absAdjustment = Math.abs(scoreAdjustment);

      const confirmed = confirm(
        `${action === 'add' ? 'Add' : 'Subtract'} ${absAdjustment} points ${action === 'add' ? 'to' : 'from'} all users?\n\n` +
        `• Total users: ${this.users.length}\n` +
        `• Users with scores: ${usersWithScores.length}\n` +
        `• Adjustment: ${scoreAdjustment > 0 ? '+' : ''}${scoreAdjustment} points`
      );

      if (!confirmed) return;

      let updatedCount = 0;
      const errors = [];
      const results = [];

      // Update each user's score
      for (const user of this.users) {
        try {
          const currentScore = user.score || 0;
          let newScore = currentScore + scoreAdjustment;

          // Prevent negative scores
          if (newScore < 0) {
            newScore = 0;
          }

          if (window.db && window.CTF_CONFIG) {
            // Update in Firebase
            const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.id);
            await updateDoc(userRef, {
              score: newScore,
              lastUpdated: serverTimestamp()
            });
          } else {
            // Update in localStorage
            user.score = newScore;
            user.lastUpdated = new Date().toISOString();
          }

          results.push({
            user: user.displayName || user.email,
            oldScore: currentScore,
            newScore: newScore,
            change: newScore - currentScore
          });

          updatedCount++;
        } catch (error) {
          console.error(`Error adjusting score for user ${user.email}:`, error);
          errors.push(`${user.email}: ${error.message}`);
        }
      }

      // Update localStorage if using fallback
      if (!window.db || !window.CTF_CONFIG) {
        localStorage.setItem('ctf_users', JSON.stringify(this.users));
      }

      // Show results
      let message = `✅ Successfully adjusted scores for ${updatedCount} users!\n\n`;

      // Show sample results
      const sampleResults = results.slice(0, 3);
      sampleResults.forEach(result => {
        message += `${result.user}: ${result.oldScore} → ${result.newScore} (${result.change >= 0 ? '+' : ''}${result.change})\n`;
      });

      if (results.length > 3) {
        message += `... and ${results.length - 3} more users\n`;
      }

      if (errors.length > 0) {
        message += `\n⚠️ Errors (${errors.length}):\n${errors.slice(0, 2).join('\n')}`;
        if (errors.length > 2) {
          message += `\n... and ${errors.length - 2} more errors`;
        }
      }

      alert(message);

      // Refresh displays
      await this.loadUsersList();
      this.loadUserScoresTable();
      this.loadScoreStats();

    } catch (error) {
      console.error('Error adjusting scores:', error);
      alert(`Error adjusting scores: ${error.message}`);
    }
  }

  async editUserScore(userId) {
    try {
      console.log(`✏️ Editing score for user: ${userId}`);

      // Load users if not already loaded
      if (this.users.length === 0) await this.loadUsersList();

      const user = this.users.find(u => u.id === userId);
      if (!user) {
        alert('❌ User not found.\n\nPlease refresh the page and try again.');
        return;
      }

      const currentScore = user.score || 0;
      const newScoreInput = prompt(
        `Edit score for: ${user.displayName || user.email}\n\nCurrent score: ${currentScore}\nEnter new score:`,
        currentScore
      );

      if (newScoreInput === null) return; // User cancelled

      const newScore = parseInt(newScoreInput);
      if (isNaN(newScore)) {
        alert('❌ Please enter a valid number.');
        return;
      }

      if (newScore < 0) {
        alert('❌ Score cannot be negative.');
        return;
      }

      if (newScore === currentScore) {
        alert('ℹ️ Score is already set to this value.');
        return;
      }

      const change = newScore - currentScore;
      const confirmed = confirm(
        `Update score for ${user.displayName || user.email}?\n\n` +
        `Current: ${currentScore} points\n` +
        `New: ${newScore} points\n` +
        `Change: ${change > 0 ? '+' : ''}${change} points\n\n` +
        `This action cannot be undone.`
      );

      if (!confirmed) return;

      try {
        console.log(`🔄 Updating score: ${currentScore} → ${newScore} for ${user.email}`);

        // Use the robust updateUserScore method
        await this.updateUserScore(user.id, newScore, 'admin_edit');

        // Update local cache
        user.score = newScore;

        alert(`✅ Score updated successfully!\n\n${user.displayName || user.email}: ${currentScore} → ${newScore} points`);

        // Force refresh displays
        console.log('🔄 Refreshing displays after score edit...');
        await this.loadUsersList();
        await this.loadUserScoresTable();
        await this.loadScoreStats();

      } catch (error) {
        console.error('Error updating user score:', error);
        alert(`❌ Error updating score: ${error.message}\n\nPlease try again or check the console for details.`);
      }

    } catch (error) {
      console.error('Error in editUserScore:', error);
      alert(`❌ Error: ${error.message}`);
    }
  }

  async sendNotification() {
    const title = document.getElementById('notification-title').value;
    const message = document.getElementById('notification-message').value;
    const type = document.getElementById('notification-type').value;
    const target = document.getElementById('notification-target').value;

    if (!title || !message) {
      alert('Please fill in both title and message.');
      return;
    }

    try {
      // Load users if not already loaded
      if (this.users.length === 0) await this.loadUsersList();

      let targetUsers = [];
      switch (target) {
        case 'all':
          targetUsers = this.users;
          break;
        case 'active':
          targetUsers = this.users.filter(u =>
            u.lastActivity && (new Date() - new Date(u.lastActivity.seconds * 1000)) < 86400000
          );
          break;
        case 'top10':
          targetUsers = [...this.users]
            .sort((a, b) => (b.score || 0) - (a.score || 0))
            .slice(0, 10);
          break;
        case 'email':
          targetUsers = this.users;
          break;
        default:
          targetUsers = this.users;
      }

      const confirmed = confirm(`Send "${title}" to ${targetUsers.length} users?`);
      if (!confirmed) return;

      // Create notification record
      const notification = {
        title: title,
        message: message,
        type: type,
        target: target,
        targetCount: targetUsers.length,
        sentAt: new Date().toISOString(),
        sentBy: authManager.getCurrentUser()?.email || 'Unknown'
      };

      // Save to notification history (simulate database save)
      this.saveNotificationToHistory(notification);

      // Send notification to all users (add to their notification feed)
      this.broadcastNotificationToUsers(notification);

      // Show success message with details
      const successMessage = `
        ✅ Notification sent successfully!

        📊 Details:
        • Title: ${title}
        • Type: ${type.toUpperCase()}
        • Target: ${target.toUpperCase()}
        • Recipients: ${targetUsers.length} users
        • Sent at: ${new Date().toLocaleString()}

        🔔 Users will see this notification in their notification panel!
      `;

      alert(successMessage);

      // Clear form
      document.getElementById('notification-title').value = '';
      document.getElementById('notification-message').value = '';

      // Refresh notification history
      this.loadRecentNotifications();
      this.updateNotificationStats();

    } catch (error) {
      console.error('Error sending notification:', error);
      alert('Error sending notification: ' + error.message);
    }
  }

  async sendBroadcastNotification() {
    const title = prompt('Enter broadcast title:');
    const message = prompt('Enter broadcast message:');

    if (title && message) {
      const confirmed = confirm(`Send broadcast: "${title}" to all users?`);
      if (confirmed) {
        // Implementation would go here
        alert('Broadcast notification sent!');
      }
    }
  }

  async refreshAnalytics() {
    this.loadAnalyticsData();
    alert('Analytics refreshed!');
  }

  // Notification helper methods
  updateNotificationStats() {
    const notificationHistory = this.getNotificationHistory();

    document.getElementById('total-notifications-sent').textContent = notificationHistory.length;
    document.getElementById('active-users-count').textContent = this.users.filter(u =>
      u.lastActivity && (new Date() - new Date(u.lastActivity.seconds * 1000)) < 86400000
    ).length;
    document.getElementById('email-notifications-sent').textContent = notificationHistory.filter(n => n.target === 'email').length;
    document.getElementById('in-app-notifications').textContent = notificationHistory.filter(n => n.target !== 'email').length;
  }

  saveNotificationToHistory(notification) {
    let history = JSON.parse(localStorage.getItem('ctf_notification_history') || '[]');
    history.unshift(notification);
    // Keep only last 50 notifications
    history = history.slice(0, 50);
    localStorage.setItem('ctf_notification_history', JSON.stringify(history));
  }

  getNotificationHistory() {
    return JSON.parse(localStorage.getItem('ctf_notification_history') || '[]');
  }

  async sendTestNotification() {
    const testNotification = {
      title: 'Test Notification',
      message: 'This is a test notification from the admin panel. If you can see this, the notification system is working correctly!',
      type: 'info',
      target: 'all',
      targetCount: this.users.length,
      sentAt: new Date().toISOString(),
      sentBy: authManager.getCurrentUser()?.email || 'Unknown'
    };

    this.saveNotificationToHistory(testNotification);

    alert('✅ Test notification sent successfully!\n\nThe notification has been logged and would be delivered to all users in a real system.');

    this.loadRecentNotifications();
    this.updateNotificationStats();
  }

  useTemplate(templateType) {
    const templates = {
      welcome: {
        title: 'Welcome to The Wolf Challenge!',
        message: 'Welcome to our CTF platform! Get ready to test your cybersecurity skills with exciting challenges. Good luck!',
        type: 'success'
      },
      challenge: {
        title: 'New Challenge Available!',
        message: 'A new challenge has been added to the platform. Check it out and earn some points!',
        type: 'challenge'
      },
      maintenance: {
        title: 'Scheduled Maintenance',
        message: 'The platform will undergo scheduled maintenance. Please save your progress. Expected downtime: 30 minutes.',
        type: 'warning'
      },
      winner: {
        title: 'Congratulations to Our Winners!',
        message: 'The competition has ended! Congratulations to all participants and especially our top performers!',
        type: 'success'
      }
    };

    const template = templates[templateType];
    if (template) {
      document.getElementById('notification-title').value = template.title;
      document.getElementById('notification-message').value = template.message;
      document.getElementById('notification-type').value = template.type;

      alert(`Template "${templateType}" loaded! You can modify the content before sending.`);
    }
  }

  // Broadcast notification to all users
  broadcastNotificationToUsers(notification) {
    try {
      // Add notification to global notification feed that all users can access
      const globalNotifications = JSON.parse(localStorage.getItem('ctf_global_notifications') || '[]');

      const userNotification = {
        id: Date.now() + Math.random(),
        title: notification.title,
        message: notification.message,
        type: notification.type,
        sentAt: notification.sentAt,
        sentBy: notification.sentBy,
        isRead: false,
        isImportant: notification.type === 'error' || notification.type === 'warning',
        source: 'admin'
      };

      globalNotifications.unshift(userNotification);

      // Keep only last 100 notifications
      const limitedNotifications = globalNotifications.slice(0, 100);
      localStorage.setItem('ctf_global_notifications', JSON.stringify(limitedNotifications));

      // Trigger notification update for all open user sessions
      this.triggerNotificationUpdate();

      console.log('🔔 Notification broadcasted to all users:', userNotification);

    } catch (error) {
      console.error('Error broadcasting notification:', error);
    }
  }

  // Trigger notification update for users
  triggerNotificationUpdate() {
    // Use localStorage event to notify other tabs/windows
    localStorage.setItem('ctf_notification_trigger', Date.now().toString());

    // Also trigger immediate update if user notification system is available
    if (window.addNotificationForUsers) {
      const globalNotifications = JSON.parse(localStorage.getItem('ctf_global_notifications') || '[]');
      if (globalNotifications.length > 0) {
        window.addNotificationForUsers(globalNotifications[0]);
      }
    }
  }

  // Excel Export Functionality
  async exportToExcel() {
    try {
      console.log('🐺 Starting Excel export...');

      // Load all data if not already loaded
      if (this.users.length === 0) await this.loadUsersList();
      if (this.challenges.length === 0) await this.loadChallengesList();
      if (this.submissions.length === 0) await this.loadSubmissionsList();

      // Create Excel workbook data
      const workbookData = this.createExcelWorkbook();

      // Convert to Excel format and download
      this.downloadExcelFile(workbookData);

      alert('Excel export completed successfully!');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      alert('Error exporting to Excel: ' + error.message);
    }
  }

  createExcelWorkbook() {
    const workbook = {
      SheetNames: ['Users', 'Challenges', 'Submissions', 'Leaderboard', 'Statistics'],
      Sheets: {}
    };

    // Users Sheet
    const usersData = [
      ['ID', 'Email', 'Display Name', 'Role', 'Score', 'Challenges Solved', 'Created At', 'Last Activity', 'Status']
    ];

    this.users.forEach(user => {
      usersData.push([
        user.id || '',
        user.email || '',
        user.displayName || '',
        user.role || 'participant',
        user.score || 0,
        user.challengesSolved || 0,
        user.createdAt ? new Date(user.createdAt.seconds * 1000).toLocaleDateString() : '',
        user.lastActivity ? new Date(user.lastActivity.seconds * 1000).toLocaleDateString() : '',
        user.isActive ? 'Active' : 'Inactive'
      ]);
    });
    workbook.Sheets['Users'] = this.arrayToSheet(usersData);

    // Challenges Sheet
    const challengesData = [
      ['ID', 'Title', 'Category', 'Difficulty', 'Points', 'Flag', 'Description', 'Created At', 'Status']
    ];

    this.challenges.forEach(challenge => {
      challengesData.push([
        challenge.id || '',
        challenge.title || '',
        challenge.category || '',
        challenge.difficulty || '',
        challenge.points || 0,
        challenge.flag || '',
        challenge.description ? challenge.description.substring(0, 100) + '...' : '',
        challenge.createdAt ? new Date(challenge.createdAt.seconds * 1000).toLocaleDateString() : '',
        challenge.isActive ? 'Active' : 'Inactive'
      ]);
    });
    workbook.Sheets['Challenges'] = this.arrayToSheet(challengesData);

    // Submissions Sheet
    const submissionsData = [
      ['ID', 'User Email', 'Challenge Title', 'Flag Submitted', 'Correct', 'Points Earned', 'Submitted At']
    ];

    this.submissions.forEach(submission => {
      const user = this.users.find(u => u.id === submission.userId);
      const challenge = this.challenges.find(c => c.id === submission.challengeId);

      submissionsData.push([
        submission.id || '',
        user ? user.email : submission.userId,
        challenge ? challenge.title : submission.challengeId,
        submission.flag || '',
        submission.isCorrect ? 'Yes' : 'No',
        submission.pointsEarned || 0,
        submission.timestamp ? new Date(submission.timestamp.seconds * 1000).toLocaleString() : ''
      ]);
    });
    workbook.Sheets['Submissions'] = this.arrayToSheet(submissionsData);

    // Leaderboard Sheet
    const leaderboardData = [
      ['Rank', 'User Email', 'Display Name', 'Total Score', 'Challenges Solved', 'Last Activity']
    ];

    const sortedUsers = [...this.users].sort((a, b) => (b.score || 0) - (a.score || 0));
    sortedUsers.forEach((user, index) => {
      leaderboardData.push([
        index + 1,
        user.email || '',
        user.displayName || '',
        user.score || 0,
        user.challengesSolved || 0,
        user.lastActivity ? new Date(user.lastActivity.seconds * 1000).toLocaleDateString() : 'Never'
      ]);
    });
    workbook.Sheets['Leaderboard'] = this.arrayToSheet(leaderboardData);

    // Statistics Sheet
    const statsData = [
      ['Metric', 'Value'],
      ['Total Users', this.users.length],
      ['Active Users', this.users.filter(u => u.lastActivity && (new Date() - new Date(u.lastActivity.seconds * 1000)) < 86400000).length],
      ['Total Challenges', this.challenges.length],
      ['Active Challenges', this.challenges.filter(c => c.isActive).length],
      ['Total Submissions', this.submissions.length],
      ['Correct Submissions', this.submissions.filter(s => s.isCorrect).length],
      ['Total Points Awarded', this.users.reduce((sum, user) => sum + (user.score || 0), 0)],
      ['Average Score', this.users.length > 0 ? (this.users.reduce((sum, user) => sum + (user.score || 0), 0) / this.users.length).toFixed(2) : 0],
      ['Highest Score', Math.max(...this.users.map(u => u.score || 0))],
      ['Export Date', new Date().toLocaleString()]
    ];
    workbook.Sheets['Statistics'] = this.arrayToSheet(statsData);

    return workbook;
  }

  arrayToSheet(data) {
    const sheet = {};
    const range = { s: { c: 0, r: 0 }, e: { c: 0, r: 0 } };

    for (let R = 0; R < data.length; R++) {
      for (let C = 0; C < data[R].length; C++) {
        if (range.s.r > R) range.s.r = R;
        if (range.s.c > C) range.s.c = C;
        if (range.e.r < R) range.e.r = R;
        if (range.e.c < C) range.e.c = C;

        const cell = { v: data[R][C] };
        if (cell.v == null) continue;

        const cell_ref = this.encodeCell({ c: C, r: R });

        if (typeof cell.v === 'number') cell.t = 'n';
        else if (typeof cell.v === 'boolean') cell.t = 'b';
        else if (cell.v instanceof Date) {
          cell.t = 'n';
          cell.z = 'mm/dd/yyyy';
          cell.v = (cell.v - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
        } else cell.t = 's';

        sheet[cell_ref] = cell;
      }
    }

    if (range.s.c < 10000000) sheet['!ref'] = this.encodeRange(range);
    return sheet;
  }

  encodeCell(cell) {
    return String.fromCharCode(65 + cell.c) + (cell.r + 1);
  }

  encodeRange(range) {
    return this.encodeCell(range.s) + ':' + this.encodeCell(range.e);
  }

  downloadExcelFile(workbook) {
    // Convert workbook to CSV format (simplified Excel export)
    const csvContent = this.workbookToCSV(workbook);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `ctf_complete_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  workbookToCSV(workbook) {
    let csvContent = '';

    workbook.SheetNames.forEach((sheetName, index) => {
      if (index > 0) csvContent += '\n\n';
      csvContent += `=== ${sheetName.toUpperCase()} ===\n`;

      const sheet = workbook.Sheets[sheetName];
      const range = sheet['!ref'];
      if (!range) return;

      const [start, end] = range.split(':');
      const startCol = start.charCodeAt(0) - 65;
      const startRow = parseInt(start.slice(1)) - 1;
      const endCol = end.charCodeAt(0) - 65;
      const endRow = parseInt(end.slice(1)) - 1;

      for (let r = startRow; r <= endRow; r++) {
        const row = [];
        for (let c = startCol; c <= endCol; c++) {
          const cellRef = String.fromCharCode(65 + c) + (r + 1);
          const cell = sheet[cellRef];
          row.push(cell ? `"${cell.v}"` : '');
        }
        csvContent += row.join(',') + '\n';
      }
    });

    return csvContent;
  }

  // Enhanced export all data method
  async exportAllData() {
    try {
      console.log('🐺 Starting comprehensive data export...');

      // Load all data if not already loaded
      if (this.users.length === 0) await this.loadUsersList();
      if (this.challenges.length === 0) await this.loadChallengesList();
      if (this.submissions.length === 0) await this.loadSubmissionsList();

      const exportData = {
        metadata: {
          exportDate: new Date().toISOString(),
          exportedBy: authManager.getCurrentUser()?.email || 'Unknown',
          platform: 'The Wolf Challenge CTF',
          version: '1.0'
        },
        statistics: {
          totalUsers: this.users.length,
          totalChallenges: this.challenges.length,
          totalSubmissions: this.submissions.length,
          activeUsers: this.users.filter(u =>
            u.lastActivity && (new Date() - new Date(u.lastActivity.seconds * 1000)) < 86400000
          ).length,
          totalPointsAwarded: this.users.reduce((sum, user) => sum + (user.score || 0), 0),
          averageScore: this.users.length > 0 ? (this.users.reduce((sum, user) => sum + (user.score || 0), 0) / this.users.length).toFixed(2) : 0
        },
        users: this.users.map(user => ({
          id: user.id,
          email: user.email,
          displayName: user.displayName,
          role: user.role,
          score: user.score || 0,
          challengesSolved: user.challengesSolved || 0,
          progress: user.progress,
          solvedChallenges: user.solvedChallenges || [],
          createdAt: user.createdAt,
          lastActivity: user.lastActivity,
          isActive: user.isActive
        })),
        challenges: this.challenges.map(challenge => ({
          id: challenge.id,
          title: challenge.title,
          description: challenge.description,
          category: challenge.category,
          difficulty: challenge.difficulty,
          points: challenge.points,
          flag: challenge.flag,
          hints: challenge.hints,
          createdAt: challenge.createdAt,
          isActive: challenge.isActive
        })),
        submissions: this.submissions.map(submission => ({
          id: submission.id,
          userId: submission.userId,
          challengeId: submission.challengeId,
          flag: submission.flag,
          isCorrect: submission.isCorrect,
          pointsEarned: submission.pointsEarned,
          timestamp: submission.timestamp
        })),
        leaderboard: [...this.users]
          .sort((a, b) => (b.score || 0) - (a.score || 0))
          .map((user, index) => ({
            rank: index + 1,
            email: user.email,
            displayName: user.displayName,
            score: user.score || 0,
            challengesSolved: user.challengesSolved || 0
          }))
      };

      const jsonContent = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `ctf_comprehensive_export_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Comprehensive data export completed successfully!');
    } catch (error) {
      console.error('Error exporting comprehensive data:', error);
      alert('Error exporting data: ' + error.message);
    }
  }

  // Bulk Challenge Creation
  async createBulkChallenges() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">🎯 Bulk Challenge Creation</h3>
          <button onclick="this.parentElement.parentElement.parentElement.remove()"
                  class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
            ✕
          </button>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-bold mb-2">Number of Challenges:</label>
            <input type="number" id="bulk-challenge-count" value="5" min="1" max="50"
                   class="w-full p-2 border-2 border-gray-300">
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Category:</label>
            <select id="bulk-challenge-category" class="w-full p-2 border-2 border-gray-300">
              <option value="web">Web Security</option>
              <option value="crypto">Cryptography</option>
              <option value="forensics">Digital Forensics</option>
              <option value="pwn">Binary Exploitation</option>
              <option value="reverse">Reverse Engineering</option>
              <option value="misc">Miscellaneous</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-bold mb-2">Difficulty:</label>
            <select id="bulk-challenge-difficulty" class="w-full p-2 border-2 border-gray-300">
              <option value="beginner">Beginner (100-200 pts)</option>
              <option value="intermediate">Intermediate (300-500 pts)</option>
              <option value="advanced">Advanced (600-1000 pts)</option>
            </select>
          </div>

          <button onclick="window.adminManager.executeBulkChallengeCreation()"
                  class="w-full neo-brutalist bg-green-500 text-white py-3 font-bold">
            🚀 CREATE CHALLENGES
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  async executeBulkChallengeCreation() {
    const count = parseInt(document.getElementById('bulk-challenge-count').value);
    const category = document.getElementById('bulk-challenge-category').value;
    const difficulty = document.getElementById('bulk-challenge-difficulty').value;

    if (count < 1 || count > 50) {
      alert('Please enter a number between 1 and 50');
      return;
    }

    const confirmed = confirm(`Create ${count} ${difficulty} ${category} challenges?`);
    if (!confirmed) return;

    try {
      const challenges = [];
      const pointsMap = {
        beginner: [100, 150, 200],
        intermediate: [300, 400, 500],
        advanced: [600, 800, 1000]
      };

      for (let i = 1; i <= count; i++) {
        const points = pointsMap[difficulty][Math.floor(Math.random() * pointsMap[difficulty].length)];
        const challenge = {
          title: `${category.charAt(0).toUpperCase() + category.slice(1)} Challenge ${i}`,
          description: `This is a ${difficulty} level ${category} challenge. Solve it to earn ${points} points!`,
          category: category,
          difficulty: difficulty,
          points: points,
          flag: `CTF{${category}_${difficulty}_${i}_${Math.random().toString(36).substring(7)}}`,
          hints: [`Hint 1 for ${category} challenge`, `Hint 2 for ${category} challenge`],
          isActive: true,
          createdAt: serverTimestamp()
        };
        challenges.push(challenge);
      }

      // Save challenges to Firestore
      for (const challenge of challenges) {
        const challengeRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES));
        await setDoc(challengeRef, challenge);
      }

      // Close modal
      document.querySelector('.fixed.inset-0').remove();

      alert(`Successfully created ${count} challenges!`);

      // Refresh challenges list
      await this.loadChallengesList();
      if (this.currentView === 'challenges') {
        this.loadChallengesManagement();
      }

    } catch (error) {
      console.error('Error creating bulk challenges:', error);
      alert('Error creating challenges: ' + error.message);
    }
  }

  // System Backup
  async systemBackup() {
    const confirmed = confirm('Create a complete system backup? This may take a few minutes.');
    if (!confirmed) return;

    try {
      console.log('🐺 Starting system backup...');

      // Load all data
      if (this.users.length === 0) await this.loadUsersList();
      if (this.challenges.length === 0) await this.loadChallengesList();
      if (this.submissions.length === 0) await this.loadSubmissionsList();

      const backupData = {
        backup: {
          timestamp: new Date().toISOString(),
          version: '1.0',
          createdBy: authManager.getCurrentUser()?.email || 'Unknown'
        },
        collections: {
          users: this.users,
          challenges: this.challenges,
          submissions: this.submissions
        },
        metadata: {
          totalUsers: this.users.length,
          totalChallenges: this.challenges.length,
          totalSubmissions: this.submissions.length,
          backupSize: JSON.stringify(this.users).length + JSON.stringify(this.challenges).length + JSON.stringify(this.submissions).length
        }
      };

      const jsonContent = JSON.stringify(backupData, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `ctf_system_backup_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('System backup completed successfully!');
    } catch (error) {
      console.error('Error creating system backup:', error);
      alert('Error creating backup: ' + error.message);
    }
  }

  // Generate Report
  async generateReport() {
    try {
      console.log('🐺 Generating comprehensive report...');

      // Load all data
      if (this.users.length === 0) await this.loadUsersList();
      if (this.challenges.length === 0) await this.loadChallengesList();
      if (this.submissions.length === 0) await this.loadSubmissionsList();

      const report = this.createComprehensiveReport();

      // Create HTML report
      const htmlContent = this.generateHTMLReport(report);
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `ctf_report_${new Date().toISOString().split('T')[0]}.html`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Comprehensive report generated successfully!');
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Error generating report: ' + error.message);
    }
  }

  createComprehensiveReport() {
    const now = new Date();
    const activeUsers = this.users.filter(u =>
      u.lastActivity && (now - new Date(u.lastActivity.seconds * 1000)) < 86400000
    );

    return {
      metadata: {
        title: 'The Wolf Challenge CTF - Comprehensive Report',
        generatedAt: now.toLocaleString(),
        generatedBy: authManager.getCurrentUser()?.email || 'Unknown'
      },
      summary: {
        totalUsers: this.users.length,
        activeUsers: activeUsers.length,
        totalChallenges: this.challenges.length,
        activeChallenges: this.challenges.filter(c => c.isActive).length,
        totalSubmissions: this.submissions.length,
        correctSubmissions: this.submissions.filter(s => s.isCorrect).length,
        totalPointsAwarded: this.users.reduce((sum, user) => sum + (user.score || 0), 0)
      },
      topPerformers: [...this.users]
        .sort((a, b) => (b.score || 0) - (a.score || 0))
        .slice(0, 10)
        .map((user, index) => ({
          rank: index + 1,
          email: user.email,
          displayName: user.displayName,
          score: user.score || 0,
          challengesSolved: user.challengesSolved || 0
        })),
      challengeStats: this.challenges.reduce((acc, challenge) => {
        acc[challenge.category] = (acc[challenge.category] || 0) + 1;
        return acc;
      }, {}),
      activityStats: {
        last24h: this.submissions.filter(s =>
          s.timestamp && (now - new Date(s.timestamp.seconds * 1000)) < 86400000
        ).length,
        last7days: this.submissions.filter(s =>
          s.timestamp && (now - new Date(s.timestamp.seconds * 1000)) < 604800000
        ).length,
        last30days: this.submissions.filter(s =>
          s.timestamp && (now - new Date(s.timestamp.seconds * 1000)) < 2592000000
        ).length
      }
    };
  }

  generateHTMLReport(report) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${report.metadata.title}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: #2563eb; color: white; border-radius: 8px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8fafc; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #2563eb; }
        .stat-number { font-size: 2em; font-weight: bold; color: #2563eb; }
        .stat-label { color: #64748b; margin-top: 5px; }
        .section { margin: 30px 0; }
        .section h2 { color: #1e293b; border-bottom: 2px solid #e2e8f0; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; }
        th { background: #f1f5f9; font-weight: bold; }
        .footer { text-align: center; margin-top: 40px; padding: 20px; background: #f8fafc; border-radius: 8px; color: #64748b; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐺 ${report.metadata.title}</h1>
            <p>Generated on ${report.metadata.generatedAt}</p>
            <p>By: ${report.metadata.generatedBy}</p>
        </div>

        <div class="section">
            <h2>📊 Platform Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${report.summary.totalUsers}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${report.summary.activeUsers}</div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${report.summary.totalChallenges}</div>
                    <div class="stat-label">Total Challenges</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${report.summary.totalSubmissions}</div>
                    <div class="stat-label">Total Submissions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${report.summary.correctSubmissions}</div>
                    <div class="stat-label">Correct Submissions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${report.summary.totalPointsAwarded}</div>
                    <div class="stat-label">Points Awarded</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🏆 Top Performers</h2>
            <table>
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>User</th>
                        <th>Score</th>
                        <th>Challenges Solved</th>
                    </tr>
                </thead>
                <tbody>
                    ${report.topPerformers.map(user => `
                        <tr>
                            <td>#${user.rank}</td>
                            <td>${user.displayName || user.email}</td>
                            <td>${user.score}</td>
                            <td>${user.challengesSolved}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎯 Challenge Categories</h2>
            <table>
                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Number of Challenges</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.entries(report.challengeStats).map(([category, count]) => `
                        <tr>
                            <td>${category.charAt(0).toUpperCase() + category.slice(1)}</td>
                            <td>${count}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>📈 Activity Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${report.activityStats.last24h}</div>
                    <div class="stat-label">Submissions (24h)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${report.activityStats.last7days}</div>
                    <div class="stat-label">Submissions (7 days)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${report.activityStats.last30days}</div>
                    <div class="stat-label">Submissions (30 days)</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>This report was automatically generated by The Wolf Challenge CTF Admin Panel</p>
            <p>For more information, contact the system administrator</p>
        </div>
    </div>
</body>
</html>
    `;
  }

  // Test function to debug challenge form
  testChallengeForm() {
    console.log('🧪 Testing challenge form...');

    const fields = [
      'challenge-title',
      'challenge-category',
      'challenge-difficulty',
      'challenge-points',
      'challenge-description',
      'challenge-instructions',
      'challenge-flag'
    ];

    const results = {};
    fields.forEach(fieldId => {
      const element = document.getElementById(fieldId);
      results[fieldId] = {
        exists: !!element,
        value: element?.value || '',
        trimmed: element?.value?.trim() || '',
        isEmpty: !element?.value?.trim()
      };
    });

    console.log('🧪 Form test results:', results);

    const summary = Object.entries(results)
      .map(([field, data]) => `${field}: ${data.exists ? '✅' : '❌'} exists, value: "${data.trimmed}", empty: ${data.isEmpty}`)
      .join('\n');

    alert(`Form Test Results:\n\n${summary}`);
  }

  // Advanced Score Manager
  async showAdvancedScoreManager() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-4xl w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">🎯 Advanced Score Manager</h3>
          <button onclick="this.parentElement.parentElement.parentElement.remove()"
                  class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
            ✕
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Bulk Operations -->
          <div class="neo-brutalist bg-blue-50 p-4">
            <h4 class="font-bold mb-3">🔄 Bulk Operations</h4>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-bold mb-1">Set All Scores To:</label>
                <div class="flex space-x-2">
                  <input type="number" id="bulk-set-score" class="neo-brutalist flex-1 p-2 text-sm" placeholder="0" min="0">
                  <button onclick="window.adminManager.bulkSetScores()" class="neo-brutalist bg-orange-500 text-white px-3 py-2 text-sm font-bold">
                    SET ALL
                  </button>
                </div>
              </div>

              <div>
                <label class="block text-sm font-bold mb-1">Multiply All Scores By:</label>
                <div class="flex space-x-2">
                  <input type="number" id="score-multiplier" class="neo-brutalist flex-1 p-2 text-sm" placeholder="1.0" step="0.1" min="0">
                  <button onclick="window.adminManager.multiplyScores()" class="neo-brutalist bg-purple-500 text-white px-3 py-2 text-sm font-bold">
                    MULTIPLY
                  </button>
                </div>
              </div>

              <div>
                <label class="block text-sm font-bold mb-1">Reset Scores Above:</label>
                <div class="flex space-x-2">
                  <input type="number" id="reset-threshold" class="neo-brutalist flex-1 p-2 text-sm" placeholder="1000" min="0">
                  <button onclick="window.adminManager.resetScoresAbove()" class="neo-brutalist bg-red-500 text-white px-3 py-2 text-sm font-bold">
                    RESET
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Score Analysis -->
          <div class="neo-brutalist bg-green-50 p-4">
            <h4 class="font-bold mb-3">📊 Score Analysis</h4>
            <div id="score-analysis" class="text-sm space-y-2">
              Loading analysis...
            </div>
            <button onclick="window.adminManager.refreshScoreAnalysis()" class="mt-3 neo-brutalist bg-blue-500 text-white px-3 py-2 text-sm font-bold w-full">
              🔄 REFRESH ANALYSIS
            </button>
          </div>
        </div>

        <div class="mt-6 flex space-x-2">
          <button onclick="window.adminManager.exportScores()" class="neo-brutalist bg-green-600 text-white px-4 py-2 font-bold">
            📊 EXPORT SCORES
          </button>
          <button onclick="this.parentElement.parentElement.parentElement.remove()" class="neo-brutalist bg-gray-500 text-white px-4 py-2 font-bold">
            CLOSE
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
    this.refreshScoreAnalysis();
  }

  // Export scores to CSV
  async exportScores() {
    try {
      if (this.users.length === 0) await this.loadUsersList();

      const sortedUsers = this.users.sort((a, b) => (b.score || 0) - (a.score || 0));

      let csv = 'Rank,Name,Email,Score,Challenges Solved,Last Activity\n';

      sortedUsers.forEach((user, index) => {
        const rank = index + 1;
        const name = (user.displayName || '').replace(/,/g, ';');
        const email = user.email || '';
        const score = user.score || 0;
        const challenges = user.challengesSolved || 0;
        const lastActivity = user.lastActivity ? new Date(user.lastActivity).toLocaleDateString() : 'Never';

        csv += `${rank},"${name}","${email}",${score},${challenges},"${lastActivity}"\n`;
      });

      // Create and download file
      const blob = new Blob([csv], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ctf_scores_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      alert(`✅ Scores exported successfully!\n\nFile: ctf_scores_${new Date().toISOString().split('T')[0]}.csv\nUsers: ${sortedUsers.length}`);

    } catch (error) {
      console.error('Error exporting scores:', error);
      alert(`Error exporting scores: ${error.message}`);
    }
  }

  // Refresh score analysis
  async refreshScoreAnalysis() {
    try {
      if (this.users.length === 0) await this.loadUsersList();

      const scores = this.users.map(u => u.score || 0).sort((a, b) => b - a);
      const totalUsers = this.users.length;
      const usersWithScores = scores.filter(s => s > 0).length;
      const totalScore = scores.reduce((sum, score) => sum + score, 0);
      const avgScore = totalUsers > 0 ? (totalScore / totalUsers).toFixed(1) : 0;
      const medianScore = scores.length > 0 ? scores[Math.floor(scores.length / 2)] : 0;
      const maxScore = scores.length > 0 ? scores[0] : 0;
      const minScore = scores.length > 0 ? scores[scores.length - 1] : 0;

      const analysisEl = document.getElementById('score-analysis');
      if (analysisEl) {
        analysisEl.innerHTML = `
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div><strong>Total Users:</strong> ${totalUsers}</div>
            <div><strong>Users with Scores:</strong> ${usersWithScores}</div>
            <div><strong>Total Points:</strong> ${totalScore}</div>
            <div><strong>Average Score:</strong> ${avgScore}</div>
            <div><strong>Median Score:</strong> ${medianScore}</div>
            <div><strong>Highest Score:</strong> ${maxScore}</div>
            <div><strong>Lowest Score:</strong> ${minScore}</div>
            <div><strong>Score Range:</strong> ${maxScore - minScore}</div>
          </div>
          <div class="mt-3 text-xs">
            <strong>Score Distribution:</strong><br>
            0 points: ${scores.filter(s => s === 0).length} users<br>
            1-100: ${scores.filter(s => s > 0 && s <= 100).length} users<br>
            101-500: ${scores.filter(s => s > 100 && s <= 500).length} users<br>
            500+: ${scores.filter(s => s > 500).length} users
          </div>
        `;
      }
    } catch (error) {
      console.error('Error refreshing score analysis:', error);
    }
  }

  // Bulk set all scores
  async bulkSetScores() {
    try {
      const scoreInput = document.getElementById('bulk-set-score');
      const newScore = parseInt(scoreInput?.value || '0');

      if (isNaN(newScore) || newScore < 0) {
        alert('Please enter a valid score (0 or higher).');
        return;
      }

      const confirmed = confirm(`Set ALL user scores to ${newScore} points?\n\nThis will affect ${this.users.length} users and cannot be undone.`);
      if (!confirmed) return;

      let updatedCount = 0;
      for (const user of this.users) {
        try {
          if (window.db && window.CTF_CONFIG) {
            const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.id);
            await updateDoc(userRef, { score: newScore, lastUpdated: serverTimestamp() });
          } else {
            user.score = newScore;
            user.lastUpdated = new Date().toISOString();
          }
          updatedCount++;
        } catch (error) {
          console.error(`Error updating ${user.email}:`, error);
        }
      }

      if (!window.db || !window.CTF_CONFIG) {
        localStorage.setItem('ctf_users', JSON.stringify(this.users));
      }

      alert(`✅ Set ${updatedCount} user scores to ${newScore} points!`);
      await this.loadUsersList();
      this.loadUserScoresTable();
      this.loadScoreStats();
      this.refreshScoreAnalysis();

    } catch (error) {
      console.error('Error bulk setting scores:', error);
      alert(`Error: ${error.message}`);
    }
  }

  // Multiply all scores
  async multiplyScores() {
    try {
      const multiplierInput = document.getElementById('score-multiplier');
      const multiplier = parseFloat(multiplierInput?.value || '1');

      if (isNaN(multiplier) || multiplier < 0) {
        alert('Please enter a valid multiplier (0 or higher).');
        return;
      }

      const confirmed = confirm(`Multiply ALL user scores by ${multiplier}?\n\nThis will affect ${this.users.length} users and cannot be undone.`);
      if (!confirmed) return;

      let updatedCount = 0;
      for (const user of this.users) {
        try {
          const currentScore = user.score || 0;
          const newScore = Math.floor(currentScore * multiplier);

          if (window.db && window.CTF_CONFIG) {
            const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.id);
            await updateDoc(userRef, { score: newScore, lastUpdated: serverTimestamp() });
          } else {
            user.score = newScore;
            user.lastUpdated = new Date().toISOString();
          }
          updatedCount++;
        } catch (error) {
          console.error(`Error updating ${user.email}:`, error);
        }
      }

      if (!window.db || !window.CTF_CONFIG) {
        localStorage.setItem('ctf_users', JSON.stringify(this.users));
      }

      alert(`✅ Multiplied ${updatedCount} user scores by ${multiplier}!`);
      await this.loadUsersList();
      this.loadUserScoresTable();
      this.loadScoreStats();
      this.refreshScoreAnalysis();

    } catch (error) {
      console.error('Error multiplying scores:', error);
      alert(`Error: ${error.message}`);
    }
  }

  // Reset scores above threshold
  async resetScoresAbove() {
    try {
      const thresholdInput = document.getElementById('reset-threshold');
      const threshold = parseInt(thresholdInput?.value || '1000');

      if (isNaN(threshold) || threshold < 0) {
        alert('Please enter a valid threshold (0 or higher).');
        return;
      }

      const usersAboveThreshold = this.users.filter(u => (u.score || 0) > threshold);

      if (usersAboveThreshold.length === 0) {
        alert(`No users have scores above ${threshold} points.`);
        return;
      }

      const confirmed = confirm(`Reset scores to 0 for ${usersAboveThreshold.length} users with scores above ${threshold} points?\n\nThis cannot be undone.`);
      if (!confirmed) return;

      let updatedCount = 0;
      for (const user of usersAboveThreshold) {
        try {
          if (window.db && window.CTF_CONFIG) {
            const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.id);
            await updateDoc(userRef, { score: 0, lastUpdated: serverTimestamp() });
          } else {
            user.score = 0;
            user.lastUpdated = new Date().toISOString();
          }
          updatedCount++;
        } catch (error) {
          console.error(`Error updating ${user.email}:`, error);
        }
      }

      if (!window.db || !window.CTF_CONFIG) {
        localStorage.setItem('ctf_users', JSON.stringify(this.users));
      }

      alert(`✅ Reset scores for ${updatedCount} users who had scores above ${threshold} points!`);
      await this.loadUsersList();
      this.loadUserScoresTable();
      this.loadScoreStats();
      this.refreshScoreAnalysis();

    } catch (error) {
      console.error('Error resetting scores above threshold:', error);
      alert(`Error: ${error.message}`);
    }
  }

  // Fill sample data for testing
  fillSampleData() {
    console.log('📝 Filling sample data...');

    const sampleData = {
      'challenge-title': 'Sample Web Challenge',
      'challenge-category': 'web',
      'challenge-difficulty': 'beginner',
      'challenge-points': '100',
      'challenge-description': 'This is a sample web security challenge for testing purposes.',
      'challenge-instructions': 'Find the hidden flag in the web application. Look for common web vulnerabilities.',
      'challenge-flag': 'wolf{sample_flag_for_testing}',
      'challenge-content': 'Additional content or code snippets can go here.',
      'challenge-hints': 'Hint 1: Check the source code\nHint 2: Look for hidden elements'
    };

    Object.entries(sampleData).forEach(([fieldId, value]) => {
      const element = document.getElementById(fieldId);
      if (element) {
        element.value = value;
        // Trigger input event to update styling
        element.dispatchEvent(new Event('input'));
      }
    });

    alert('✅ Sample data filled! You can now test the form submission.');
  }

  // Test score update functionality
  async testScoreUpdate() {
    try {
      console.log('🧪 Testing score update functionality...');

      // Load users if not already loaded
      if (this.users.length === 0) await this.loadUsersList();

      if (this.users.length === 0) {
        alert('❌ No users found to test with.\n\nPlease ensure users are registered first.');
        return;
      }

      // Test with first user
      const testUser = this.users[0];
      const originalScore = testUser.score || 0;
      const testScore = originalScore + 10;

      console.log(`🧪 Testing with user: ${testUser.email}`);
      console.log(`🧪 Original score: ${originalScore}`);
      console.log(`🧪 Test score: ${testScore}`);

      const confirmed = confirm(
        `🧪 Test Score Update\n\n` +
        `User: ${testUser.displayName || testUser.email}\n` +
        `Current score: ${originalScore}\n` +
        `Test score: ${testScore} (+10)\n\n` +
        `This will temporarily add 10 points for testing.\n` +
        `Continue with test?`
      );

      if (!confirmed) return;

      // Test the update
      console.log('🧪 Attempting score update...');
      await this.updateUserScore(testUser.id, testScore, 'test_update');

      // Update local cache
      testUser.score = testScore;

      // Verify the update
      console.log('🧪 Verifying update...');
      await this.loadUsersList();

      const updatedUser = this.users.find(u => u.id === testUser.id);
      const actualScore = updatedUser ? updatedUser.score || 0 : 0;

      let resultMessage = `🧪 Score Update Test Results:\n\n`;
      resultMessage += `User: ${testUser.displayName || testUser.email}\n`;
      resultMessage += `Original: ${originalScore}\n`;
      resultMessage += `Expected: ${testScore}\n`;
      resultMessage += `Actual: ${actualScore}\n\n`;

      if (actualScore === testScore) {
        resultMessage += `✅ SUCCESS: Score update is working correctly!`;
      } else {
        resultMessage += `❌ FAILED: Score was not updated properly.\n`;
        resultMessage += `Expected ${testScore} but got ${actualScore}`;
      }

      alert(resultMessage);

      // Refresh displays
      await this.loadUserScoresTable();
      await this.loadScoreStats();

      // Ask if user wants to revert the test
      if (actualScore === testScore) {
        const revert = confirm(`Test successful! Revert the test score back to ${originalScore}?`);
        if (revert) {
          await this.updateUserScore(testUser.id, originalScore, 'test_revert');
          testUser.score = originalScore;
          await this.loadUsersList();
          await this.loadUserScoresTable();
          await this.loadScoreStats();
          alert(`✅ Test score reverted back to ${originalScore}`);
        }
      }

    } catch (error) {
      console.error('🧪 Test failed:', error);
      alert(`❌ Score update test failed:\n\n${error.message}\n\nCheck console for details.`);
    }
  }

  // Quick and direct score update - bypasses all complexity
  async quickScoreUpdate() {
    try {
      console.log('⚡ QUICK SCORE UPDATE - Direct localStorage method');

      // Load users if not already loaded
      if (this.users.length === 0) await this.loadUsersList();

      if (this.users.length === 0) {
        alert('❌ No users found. Please ensure users are registered first.');
        return;
      }

      // Show user selection
      let userList = 'Select user by number:\n\n';
      this.users.forEach((user, index) => {
        userList += `${index + 1}. ${user.displayName || user.email} (Current: ${user.score || 0} pts)\n`;
      });

      const userSelection = prompt(userList + '\nEnter user number:');
      if (!userSelection) return;

      const userIndex = parseInt(userSelection) - 1;
      if (isNaN(userIndex) || userIndex < 0 || userIndex >= this.users.length) {
        alert('❌ Invalid user selection.');
        return;
      }

      const selectedUser = this.users[userIndex];
      const currentScore = selectedUser.score || 0;

      const newScoreInput = prompt(
        `⚡ QUICK UPDATE\n\n` +
        `User: ${selectedUser.displayName || selectedUser.email}\n` +
        `Current Score: ${currentScore}\n\n` +
        `Enter new score:`
      );

      if (!newScoreInput) return;

      const newScore = parseInt(newScoreInput);
      if (isNaN(newScore) || newScore < 0) {
        alert('❌ Please enter a valid score (0 or higher).');
        return;
      }

      console.log(`⚡ Direct update: ${selectedUser.email} from ${currentScore} to ${newScore}`);

      // DIRECT localStorage update - no Firebase, no complexity
      try {
        // Update localStorage directly
        let users = JSON.parse(localStorage.getItem('ctf_users') || '[]');
        const storageUserIndex = users.findIndex(u => u.id === selectedUser.id);

        if (storageUserIndex !== -1) {
          users[storageUserIndex].score = newScore;
          users[storageUserIndex].lastUpdated = new Date().toISOString();
          localStorage.setItem('ctf_users', JSON.stringify(users));
          console.log('✅ localStorage updated directly');
        }

        // Update memory array
        selectedUser.score = newScore;
        selectedUser.lastUpdated = new Date().toISOString();
        console.log('✅ Memory array updated');

        // Force refresh displays immediately
        this.loadUserScoresTable();
        this.loadScoreStats();

        alert(`✅ QUICK UPDATE SUCCESSFUL!\n\n${selectedUser.displayName || selectedUser.email}: ${currentScore} → ${newScore} points\n\nCheck the scores table to verify the change.`);

      } catch (error) {
        console.error('❌ Quick update failed:', error);
        alert(`❌ Quick update failed: ${error.message}`);
      }

    } catch (error) {
      console.error('❌ Quick score update error:', error);
      alert(`❌ Error: ${error.message}`);
    }
  }

  // Direct score edit - bypasses all Firebase complexity
  async directScoreEdit(userId) {
    try {
      console.log(`⚡ DIRECT SCORE EDIT for user: ${userId}`);

      // Find user in current array
      const user = this.users.find(u => u.id === userId);
      if (!user) {
        alert('❌ User not found. Please refresh and try again.');
        return;
      }

      const currentScore = user.score || 0;
      const newScoreInput = prompt(
        `⚡ DIRECT SCORE EDIT\n\n` +
        `User: ${user.displayName || user.email}\n` +
        `Current Score: ${currentScore}\n\n` +
        `Enter new score (this will update immediately):`
      );

      if (!newScoreInput) return;

      const newScore = parseInt(newScoreInput);
      if (isNaN(newScore) || newScore < 0) {
        alert('❌ Please enter a valid score (0 or higher).');
        return;
      }

      if (newScore === currentScore) {
        alert('ℹ️ Score is already set to this value.');
        return;
      }

      console.log(`⚡ DIRECT UPDATE: ${user.email} from ${currentScore} to ${newScore}`);

      // IMMEDIATE localStorage update
      try {
        // Update localStorage
        let users = JSON.parse(localStorage.getItem('ctf_users') || '[]');
        const storageUserIndex = users.findIndex(u => u.id === userId);

        if (storageUserIndex !== -1) {
          users[storageUserIndex].score = newScore;
          users[storageUserIndex].lastUpdated = new Date().toISOString();
          localStorage.setItem('ctf_users', JSON.stringify(users));
          console.log('✅ localStorage updated immediately');
        }

        // Update memory array immediately
        user.score = newScore;
        user.lastUpdated = new Date().toISOString();
        console.log('✅ Memory updated immediately');

        // Update the table cell immediately without full reload
        const tableRows = document.querySelectorAll('#user-scores-table tbody tr');
        tableRows.forEach((row, index) => {
          const scoreCell = row.children[2]; // Score column
          if (scoreCell && this.users[index] && this.users[index].id === userId) {
            scoreCell.innerHTML = `<strong>${newScore}</strong>`;
            scoreCell.style.backgroundColor = '#dcfce7'; // Green highlight
            setTimeout(() => {
              scoreCell.style.backgroundColor = '';
            }, 2000);
          }
        });

        alert(`✅ DIRECT UPDATE SUCCESSFUL!\n\n${user.displayName || user.email}: ${currentScore} → ${newScore} points\n\nThe score has been updated immediately!`);

        // Refresh stats (but not the full table since we updated it directly)
        this.loadScoreStats();

      } catch (error) {
        console.error('❌ Direct update failed:', error);
        alert(`❌ Direct update failed: ${error.message}`);
      }

    } catch (error) {
      console.error('❌ Direct score edit error:', error);
      alert(`❌ Error: ${error.message}`);
    }
  }
}

// Initialize admin manager
const adminManager = new AdminManager();

// Make it globally available for onclick handlers
window.adminManager = adminManager;

export default adminManager;
