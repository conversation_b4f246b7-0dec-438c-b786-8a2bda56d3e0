// Profile Loading Fix for The Wolf Challenge CTF Platform
import { auth, db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import { 
  doc, 
  getDoc, 
  setDoc, 
  serverTimestamp 
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class ProfileLoadingFixer {
  constructor() {
    this.isFixing = false;
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  // Main fix method for profile loading issues
  async fixProfileLoading() {
    if (this.isFixing) {
      console.log('🐺 Profile fix already in progress...');
      return;
    }

    this.isFixing = true;
    this.retryCount = 0;

    try {
      console.log('🐺 Starting profile loading fix...');
      
      // Show fix progress
      this.showFixProgress();
      
      // Step 1: Check authentication
      const user = await this.checkAuthentication();
      
      // Step 2: Clear any stuck loading states
      this.clearLoadingStates();
      
      // Step 3: Test database connection
      await this.testDatabaseConnection();
      
      // Step 4: Force reload user profile
      await this.forceReloadProfile(user);
      
      // Step 5: Verify profile loaded correctly
      await this.verifyProfileLoaded();
      
      console.log('✅ Profile loading fix completed successfully');
      this.showSuccessMessage();
      
    } catch (error) {
      console.error('❌ Profile loading fix failed:', error);
      this.showErrorMessage(error);
    } finally {
      this.isFixing = false;
      this.hideFixProgress();
    }
  }

  // Check if user is authenticated
  async checkAuthentication() {
    console.log('🔍 Checking authentication status...');
    
    const user = authManager.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated. Please log in first.');
    }
    
    console.log('✅ User authenticated:', user.email);
    return user;
  }

  // Clear any stuck loading states
  clearLoadingStates() {
    console.log('🧹 Clearing stuck loading states...');
    
    // Remove any existing loading indicators
    const loadingIndicators = [
      'profile-loading-indicator',
      'loading-overlay',
      'auth-loading'
    ];
    
    loadingIndicators.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.remove();
        console.log('🧹 Removed stuck loading element:', id);
      }
    });
    
    // Clear any loading intervals
    if (window.loadingStatusInterval) {
      clearInterval(window.loadingStatusInterval);
      window.loadingStatusInterval = null;
    }
  }

  // Test database connection
  async testDatabaseConnection() {
    console.log('🔍 Testing database connection...');
    
    try {
      // Simple test query
      const testRef = doc(db, 'system_config', 'test');
      await getDoc(testRef);
      
      console.log('✅ Database connection working');
      
    } catch (error) {
      if (error.code === 'permission-denied') {
        throw new Error('Database permission denied. Please check Firestore rules.');
      } else if (error.code === 'unavailable') {
        throw new Error('Database service unavailable. Please try again later.');
      } else {
        throw new Error('Database connection failed: ' + error.message);
      }
    }
  }

  // Force reload user profile
  async forceReloadProfile(user) {
    console.log('🔄 Force reloading user profile...');
    
    try {
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
      
      // Try to get user document with timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile loading timeout')), 8000);
      });
      
      const userDoc = await Promise.race([
        getDoc(userRef),
        timeoutPromise
      ]);
      
      if (!userDoc.exists()) {
        console.log('📝 User document not found, creating...');
        await this.createUserProfile(user);
      } else {
        console.log('✅ User profile loaded successfully');
        
        // Update last activity
        await setDoc(userRef, {
          lastActivity: serverTimestamp()
        }, { merge: true });
      }
      
    } catch (error) {
      if (error.message.includes('timeout')) {
        throw new Error('Profile loading timed out. Please check your connection.');
      }
      throw error;
    }
  }

  // Create user profile if it doesn't exist
  async createUserProfile(user) {
    console.log('📝 Creating user profile...');
    
    const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
    
    const userData = {
      email: user.email,
      role: this.determineUserRole(user.email),
      score: 0,
      challengesSolved: 0,
      progress: {
        beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
        intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
        advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
      },
      solvedChallenges: [],
      accountStatus: 'active',
      createdAt: serverTimestamp(),
      lastActivity: serverTimestamp()
    };
    
    await setDoc(userRef, userData);
    console.log('✅ User profile created successfully');
  }

  // Determine user role (simplified version)
  determineUserRole(email) {
    const authorizedAdmins = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    return authorizedAdmins.includes(email) ? 'admin' : 'participant';
  }

  // Verify profile loaded correctly
  async verifyProfileLoaded() {
    console.log('🔍 Verifying profile loaded correctly...');
    
    const user = authManager.getCurrentUser();
    if (!user) {
      throw new Error('User authentication lost during fix');
    }
    
    // Check if auth manager has user data
    const userRole = authManager.getUserRole();
    if (!userRole) {
      throw new Error('User role not loaded');
    }
    
    console.log('✅ Profile verification passed');
  }

  // Show fix progress
  showFixProgress() {
    const progressDiv = document.createElement('div');
    progressDiv.id = 'profile-fix-progress';
    progressDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    progressDiv.innerHTML = `
      <div class="neo-brutalist bg-white p-6 text-center max-w-sm mx-4">
        <div class="animate-spin w-12 h-12 border-4 border-green-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <h3 class="text-lg font-bold mb-2">Fixing Profile Loading...</h3>
        <p class="text-sm text-gray-600 mb-4">Please wait while we resolve the issue</p>
        <div id="fix-status" class="text-xs text-gray-500">
          Starting diagnostic...
        </div>
      </div>
    `;
    
    document.body.appendChild(progressDiv);
  }

  // Hide fix progress
  hideFixProgress() {
    const progressDiv = document.getElementById('profile-fix-progress');
    if (progressDiv) {
      progressDiv.remove();
    }
  }

  // Show success message
  showSuccessMessage() {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-green-500 text-white p-4 max-w-md';
    successDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">✅</span>
        <h3 class="font-bold">Profile Loading Fixed!</h3>
      </div>
      <p class="text-sm mb-3">Your profile has been loaded successfully.</p>
      <button onclick="this.parentElement.remove(); window.location.reload();" 
              class="bg-white text-green-500 px-3 py-1 text-sm font-bold rounded">
        RELOAD PAGE
      </button>
    `;
    
    document.body.appendChild(successDiv);
    
    // Auto-reload after 5 seconds
    setTimeout(() => {
      if (successDiv.parentElement) {
        successDiv.remove();
        window.location.reload();
      }
    }, 5000);
  }

  // Show error message
  showErrorMessage(error) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-red-500 text-white p-4 max-w-md';
    errorDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">❌</span>
        <h3 class="font-bold">Fix Failed</h3>
      </div>
      <p class="text-sm mb-3">${error.message}</p>
      <div class="space-x-2">
        <button onclick="profileLoadingFixer.fixProfileLoading()" 
                class="bg-white text-red-500 px-3 py-1 text-sm font-bold rounded">
          RETRY
        </button>
        <button onclick="this.parentElement.remove()" 
                class="bg-red-700 text-white px-3 py-1 text-sm font-bold rounded">
          CLOSE
        </button>
      </div>
    `;
    
    document.body.appendChild(errorDiv);
  }

  // Quick fix method for console use
  async quickFix() {
    console.log('🐺 Running quick profile loading fix...');
    
    try {
      // Clear loading states
      this.clearLoadingStates();
      
      // Force refresh auth state
      const user = authManager.getCurrentUser();
      if (user) {
        await authManager.handleUserLogin(user);
      }
      
      console.log('✅ Quick fix completed');
      return true;
      
    } catch (error) {
      console.error('❌ Quick fix failed:', error);
      return false;
    }
  }
}

// Initialize profile loading fixer
const profileLoadingFixer = new ProfileLoadingFixer();

// Make it globally available
window.profileLoadingFixer = profileLoadingFixer;

// Auto-detect loading issues and offer fix
document.addEventListener('DOMContentLoaded', () => {
  // Check for stuck loading states after 10 seconds
  setTimeout(() => {
    const loadingIndicator = document.getElementById('profile-loading-indicator');
    if (loadingIndicator) {
      console.log('🐺 Detected stuck profile loading, offering fix...');
      
      const fixButton = document.createElement('button');
      fixButton.className = 'fixed bottom-4 left-4 z-50 neo-brutalist bg-orange-500 text-white px-4 py-2 font-bold hover:bg-orange-600';
      fixButton.innerHTML = '🔧 FIX LOADING';
      fixButton.onclick = () => {
        profileLoadingFixer.fixProfileLoading();
        fixButton.remove();
      };
      
      document.body.appendChild(fixButton);
    }
  }, 10000);
});

export default profileLoadingFixer;
