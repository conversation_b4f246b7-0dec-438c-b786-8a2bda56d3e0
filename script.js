// Main Application Controller for The Wolf Challenge CTF Platform
import authManager from './auth.js';
import challengeManager from './challenges.js';
import dashboardManager from './dashboard.js';
import leaderboardManager from './leaderboard.js';
import adminManager from './admin.js';
import securityManager from './security.js';
import scoreService from './score-service.js';
import notificationService from './notification-service.js';
import pushNotificationService from './push-notification-service.js';
import serviceUnavailableFixer from './fix-service-unavailable.js';
import profileLoadingFixer from './fix-profile-loading.js';
import profileTabFixer from './fix-profile-tab.js';
import adminLoginFixer from './fix-admin-login.js';
import authServiceErrorFixer from './fix-auth-service-error.js';
import adminSetup from './admin-setup.js';
import { CTF_CONFIG } from './firebase-config.js';

class WolfChallengeApp {
  constructor() {
    this.isInitialized = false;
    this.currentUser = null;
    this.managers = {
      auth: authManager,
      challenges: challengeManager,
      dashboard: dashboardManager,
      leaderboard: leaderboardManager,
      admin: adminManager,
      security: securityManager,
      score: scoreService,
      notifications: notificationService,
      pushNotifications: pushNotificationService,
      serviceFixer: serviceUnavailableFixer
    };

    this.initializeApp();
  }

  async initializeApp() {
    try {
      console.log('🐺 Initializing The Wolf Challenge...');

      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }

      // Initialize core systems
      await this.initializeCoreServices();

      // Set up global event handlers
      this.setupGlobalEventHandlers();

      // Set up periodic maintenance
      this.setupMaintenance();

      this.isInitialized = true;
      console.log('🐺 The Wolf Challenge initialized successfully!');

    } catch (error) {
      console.error('🐺 Failed to initialize The Wolf Challenge:', error);
      this.handleInitializationError(error);
    }
  }

  async initializeCoreServices() {
    // Security manager should already be initialized
    if (!securityManager.isInitialized) {
      throw new Error('Security manager failed to initialize');
    }

    // Set up authentication state listener
    this.setupAuthenticationListener();

    // Initialize other managers (they will activate when user logs in)
    console.log('🐺 Core services initialized');
  }

  setupAuthenticationListener() {
    // Listen for authentication state changes
    const originalHandleUserLogin = authManager.handleUserLogin.bind(authManager);
    const originalHandleUserLogout = authManager.handleUserLogout.bind(authManager);

    authManager.handleUserLogin = async (user) => {
      await originalHandleUserLogin(user);
      await this.onUserLogin(user);
    };

    authManager.handleUserLogout = () => {
      this.onUserLogout();
      originalHandleUserLogout();
    };
  }

  async onUserLogin(user) {
    try {
      this.currentUser = user;
      console.log('🐺 User logged in, initializing user-specific services...');

      // Refresh all managers with user context
      await this.refreshUserData();

      // Start real-time features if needed
      this.startRealTimeFeatures();

      console.log('🐺 User services initialized');
    } catch (error) {
      console.error('🐺 Error initializing user services:', error);
    }
  }

  onUserLogout() {
    this.currentUser = null;
    console.log('🐺 User logged out, cleaning up services...');

    // Stop real-time features
    this.stopRealTimeFeatures();

    // Clear sensitive data
    this.clearUserData();
  }

  async refreshUserData() {
    try {
      // Refresh dashboard data
      if (dashboardManager.refresh) {
        await dashboardManager.refresh();
      }

      // Refresh challenges
      if (challengeManager.refreshChallenges) {
        await challengeManager.refreshChallenges();
      }

    } catch (error) {
      console.error('🐺 Error refreshing user data:', error);
    }
  }

  startRealTimeFeatures() {
    // Real-time features are managed by individual managers
    // This is where we could coordinate them if needed
    console.log('🐺 Real-time features started');
  }

  stopRealTimeFeatures() {
    // Stop real-time leaderboard updates
    if (leaderboardManager.disableRealTimeUpdates) {
      leaderboardManager.disableRealTimeUpdates();
    }

    console.log('🐺 Real-time features stopped');
  }

  clearUserData() {
    // Clear any cached user data
    // Individual managers handle their own cleanup
    console.log('🐺 User data cleared');
  }

  setupGlobalEventHandlers() {
    // Global error handling
    window.addEventListener('error', (event) => {
      console.error('🐺 Global error:', event.error);
      this.handleGlobalError(event.error);
    });

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('🐺 Unhandled promise rejection:', event.reason);
      this.handleGlobalError(event.reason);
    });

    // Network status changes
    window.addEventListener('online', () => {
      console.log('🐺 Network connection restored');
      this.handleNetworkRestore();
    });

    window.addEventListener('offline', () => {
      console.log('🐺 Network connection lost');
      this.handleNetworkLoss();
    });

    // Keyboard shortcuts for power users
    document.addEventListener('keydown', (event) => {
      this.handleKeyboardShortcuts(event);
    });
  }

  handleGlobalError(error) {
    // Log error for debugging
    securityManager.logSecurityEvent({
      type: 'APPLICATION_ERROR',
      error: error.message || error.toString(),
      stack: error.stack,
      timestamp: Date.now()
    });

    // Show user-friendly error message for critical errors
    if (this.isCriticalError(error)) {
      this.showErrorModal('A critical error occurred. Please refresh the page.');
    }
  }

  isCriticalError(error) {
    const criticalPatterns = [
      /firebase/i,
      /authentication/i,
      /network/i,
      /security/i
    ];

    const errorMessage = error.message || error.toString();
    return criticalPatterns.some(pattern => pattern.test(errorMessage));
  }

  showErrorModal(message) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-8 max-w-md mx-4 text-center">
        <div class="text-4xl mb-4">⚠️</div>
        <h3 class="text-2xl font-bold mb-4">Error</h3>
        <p class="text-gray-600 mb-6">${message}</p>
        <button onclick="location.reload()"
                class="neo-brutalist bg-red-500 text-white px-6 py-3 font-bold">
          REFRESH PAGE
        </button>
      </div>
    `;

    document.body.appendChild(modal);
  }

  handleNetworkRestore() {
    // Refresh data when network is restored
    if (this.currentUser) {
      this.refreshUserData();
    }
  }

  handleNetworkLoss() {
    // Show offline indicator
    this.showOfflineIndicator();
  }

  showOfflineIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'offline-indicator';
    indicator.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50';
    indicator.innerHTML = `
      <div class="flex items-center space-x-2">
        <i class="fas fa-wifi-slash"></i>
        <span class="font-bold">Offline</span>
      </div>
    `;

    // Remove existing indicator
    const existing = document.getElementById('offline-indicator');
    if (existing) existing.remove();

    document.body.appendChild(indicator);

    // Remove when back online
    const removeOnOnline = () => {
      indicator.remove();
      window.removeEventListener('online', removeOnOnline);
    };
    window.addEventListener('online', removeOnOnline);
  }

  handleKeyboardShortcuts(event) {
    // Only handle shortcuts when user is logged in
    if (!this.currentUser) return;

    // Ctrl/Cmd + K for quick challenge search (future feature)
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault();
      console.log('🐺 Quick search shortcut (future feature)');
    }

    // Ctrl/Cmd + L for leaderboard
    if ((event.ctrlKey || event.metaKey) && event.key === 'l') {
      event.preventDefault();
      const leaderboardTab = document.getElementById('leaderboard-tab');
      if (leaderboardTab) leaderboardTab.click();
    }

    // Ctrl/Cmd + P for profile
    if ((event.ctrlKey || event.metaKey) && event.key === 'p') {
      event.preventDefault();
      const profileTab = document.getElementById('profile-tab');
      if (profileTab) profileTab.click();
    }
  }

  setupMaintenance() {
    // Run maintenance tasks every 10 minutes
    setInterval(() => {
      this.performMaintenance();
    }, 10 * 60 * 1000);

    // Run initial maintenance after 1 minute
    setTimeout(() => {
      this.performMaintenance();
    }, 60 * 1000);
  }

  performMaintenance() {
    try {
      console.log('🐺 Running maintenance tasks...');

      // Security system maintenance
      if (securityManager.performMaintenance) {
        securityManager.performMaintenance();
      }

      // Clear old cached data
      this.clearOldCacheData();

      // Update activity timestamp
      if (this.currentUser) {
        securityManager.updateLastActivity();
      }

      console.log('🐺 Maintenance completed');
    } catch (error) {
      console.error('🐺 Maintenance error:', error);
    }
  }

  clearOldCacheData() {
    try {
      // Clear old localStorage entries
      const keys = Object.keys(localStorage);
      const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days ago

      keys.forEach(key => {
        if (key.startsWith('ctf_cache_')) {
          try {
            const data = JSON.parse(localStorage.getItem(key));
            if (data.timestamp && data.timestamp < cutoffTime) {
              localStorage.removeItem(key);
            }
          } catch (e) {
            // Invalid JSON, remove it
            localStorage.removeItem(key);
          }
        }
      });
    } catch (error) {
      console.error('🐺 Cache cleanup error:', error);
    }
  }

  handleInitializationError(error) {
    console.error('🐺 Critical initialization error:', error);

    // Show emergency error screen
    document.body.innerHTML = `
      <div class="min-h-screen bg-red-900 text-white flex items-center justify-center">
        <div class="text-center max-w-md mx-4">
          <div class="text-6xl mb-4">💥</div>
          <h1 class="text-3xl font-bold mb-4">System Error</h1>
          <p class="text-lg mb-6">The Wolf Challenge failed to initialize properly.</p>
          <p class="text-sm mb-6 text-red-200">Error: ${error.message}</p>
          <button onclick="location.reload()"
                  class="bg-white text-red-900 px-6 py-3 font-bold rounded">
            RETRY
          </button>
        </div>
      </div>
    `;
  }

  // Public API methods
  getStatus() {
    return {
      initialized: this.isInitialized,
      user: this.currentUser ? {
        email: this.currentUser.email,
        uid: this.currentUser.uid
      } : null,
      managers: Object.keys(this.managers).reduce((status, key) => {
        status[key] = this.managers[key] ? 'loaded' : 'not loaded';
        return status;
      }, {})
    };
  }

  // Emergency methods for debugging
  emergencyReset() {
    console.warn('🐺 Emergency reset initiated');

    // Clear all local storage
    localStorage.clear();
    sessionStorage.clear();

    // Reload page
    location.reload();
  }

  emergencyLogout() {
    console.warn('🐺 Emergency logout initiated');

    if (authManager.handleLogout) {
      authManager.handleLogout();
    } else {
      location.reload();
    }
  }
}

// Initialize The Wolf Challenge Application
const wolfChallengeApp = new WolfChallengeApp();

// Make app globally available for debugging and emergency access
window.wolfChallengeApp = wolfChallengeApp;

// Export for use in other modules
export default wolfChallengeApp;

// Console welcome message
console.log(`
🐺 ========================================
   THE WOLF CHALLENGE - CTF PLATFORM
   Developed by S.Tamilselvan

   Ready to hunt for flags?
   Good luck, hunter! 🎯
========================================
`);

// Development helpers (only in development)
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  console.log('🐺 Development mode detected');

  // Add development helpers to window
  window.ctfDebug = {
    app: wolfChallengeApp,
    auth: authManager,
    challenges: challengeManager,
    dashboard: dashboardManager,
    leaderboard: leaderboardManager,
    admin: adminManager,
    security: securityManager,
    score: scoreService,
    notifications: notificationService,

    // Helper functions
    getStatus: () => wolfChallengeApp.getStatus(),
    emergencyReset: () => wolfChallengeApp.emergencyReset(),
    emergencyLogout: () => wolfChallengeApp.emergencyLogout(),

    // Security helpers
    getSecurityEvents: () => securityManager.getSecurityEvents(),
    getRateLimitStatus: (challengeId) => securityManager.getRateLimitStatus(challengeId),

    // Score service helpers
    getUserScore: (userId) => scoreService.getUserScore(userId),
    getScoreHistory: (userId) => scoreService.getScoreHistory(userId),

    // Notification helpers
    sendTestNotification: (type, title, message) => {
      const user = authManager.getCurrentUser();
      if (user) {
        notificationService.sendNotification(user.uid, { type, title, message });
      }
    },

    // Quick actions
    switchToAdmin: () => {
      const adminTab = document.getElementById('admin-tab');
      if (adminTab && !adminTab.classList.contains('hidden')) {
        adminTab.click();
      } else {
        console.log('Admin tab not available - user may not be admin');
      }
    },

    switchToLeaderboard: () => {
      const leaderboardTab = document.getElementById('leaderboard-tab');
      if (leaderboardTab) leaderboardTab.click();
    }
  };

  console.log('🐺 Debug tools available at window.ctfDebug');
}