// Profile Tab Click Fix for The Wolf Challenge CTF Platform
// This script fixes the "Loading profile..." issue when clicking the profile tab

class ProfileTabFixer {
  constructor() {
    this.isFixing = false;
  }

  // Main fix method for profile tab issues
  async fixProfileTab() {
    if (this.isFixing) {
      console.log('🐺 Profile tab fix already in progress...');
      return;
    }

    this.isFixing = true;

    try {
      console.log('🐺 Fixing profile tab loading issue...');
      
      // Step 1: Clear any stuck loading states
      this.clearStuckLoadingStates();
      
      // Step 2: Reset dashboard manager state
      this.resetDashboardState();
      
      // Step 3: Force reload user stats
      await this.forceReloadUserStats();
      
      // Step 4: Manually trigger profile loading
      await this.manuallyLoadProfile();
      
      console.log('✅ Profile tab fix completed successfully');
      this.showSuccessMessage();
      
    } catch (error) {
      console.error('❌ Profile tab fix failed:', error);
      this.showErrorMessage(error);
    } finally {
      this.isFixing = false;
    }
  }

  // Clear stuck loading states
  clearStuckLoadingStates() {
    console.log('🧹 Clearing stuck loading states...');
    
    const profileContent = document.getElementById('profile-content');
    if (profileContent && profileContent.textContent.includes('Loading profile')) {
      console.log('🧹 Found stuck loading state, clearing...');
      profileContent.innerHTML = '<div class="text-center py-8">Preparing profile...</div>';
    }

    // Clear any loading intervals or timeouts
    if (window.profileLoadingTimeout) {
      clearTimeout(window.profileLoadingTimeout);
      window.profileLoadingTimeout = null;
    }
  }

  // Reset dashboard manager state
  resetDashboardState() {
    console.log('🔄 Resetting dashboard state...');
    
    if (window.dashboardManager) {
      // Clear cached user stats
      window.dashboardManager.userStats = null;
      console.log('🔄 Dashboard state reset');
    }
  }

  // Force reload user stats
  async forceReloadUserStats() {
    console.log('📊 Force reloading user stats...');
    
    if (window.dashboardManager) {
      try {
        await window.dashboardManager.loadUserStats();
        console.log('✅ User stats reloaded');
      } catch (error) {
        console.warn('⚠️ Failed to reload user stats:', error.message);
        // Continue with fallback data
      }
    }
  }

  // Manually load profile
  async manuallyLoadProfile() {
    console.log('👤 Manually loading profile...');
    
    const profileContent = document.getElementById('profile-content');
    if (!profileContent) {
      throw new Error('Profile content element not found');
    }

    // Show loading indicator
    profileContent.innerHTML = `
      <div class="text-center py-8">
        <div class="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <div>Loading profile data...</div>
      </div>
    `;

    try {
      if (window.dashboardManager) {
        // Use dashboard manager if available
        await window.dashboardManager.loadProfile();
      } else {
        // Fallback: create basic profile display
        await this.createFallbackProfile();
      }
      
      console.log('✅ Profile loaded successfully');
      
    } catch (error) {
      console.error('❌ Profile loading failed:', error);
      this.createErrorProfile(error.message);
    }
  }

  // Create fallback profile display
  async createFallbackProfile() {
    console.log('🔄 Creating fallback profile display...');
    
    const profileContent = document.getElementById('profile-content');
    if (!profileContent) return;

    const user = window.authManager?.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get cached profile data if available
    const cachedProfile = localStorage.getItem(`ctf_fallback_profile_${user.uid}`);
    let profileData = null;
    
    if (cachedProfile) {
      try {
        profileData = JSON.parse(cachedProfile);
      } catch (error) {
        console.warn('Failed to parse cached profile data');
      }
    }

    // Create basic profile if no cached data
    if (!profileData) {
      profileData = {
        email: user.email,
        role: 'participant',
        score: 0,
        challengesSolved: 0,
        progress: {
          beginner: { solved: 0, total: 10 },
          intermediate: { solved: 0, total: 20 },
          advanced: { solved: 0, total: 40 }
        },
        isFallback: true
      };
    }

    profileContent.innerHTML = `
      <div class="space-y-6">
        <!-- User Info -->
        <div class="neo-brutalist bg-gray-50 p-6">
          <h3 class="text-2xl font-bold mb-4">👤 User Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-bold mb-1">Email</label>
              <div class="p-3 bg-white border-2 border-gray-300">${profileData.email}</div>
            </div>
            <div>
              <label class="block text-sm font-bold mb-1">Role</label>
              <div class="p-3 bg-white border-2 border-gray-300 capitalize">
                ${profileData.role}
                ${profileData.role === 'admin' ? ' 👑' : ''}
              </div>
            </div>
          </div>
        </div>

        <!-- Statistics -->
        <div class="neo-brutalist bg-gray-50 p-6">
          <h3 class="text-2xl font-bold mb-4">📊 Statistics</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-white border-2 border-gray-300">
              <div class="text-3xl font-bold text-green-600">${profileData.score || 0}</div>
              <div class="text-sm">Total Score</div>
            </div>
            <div class="text-center p-4 bg-white border-2 border-gray-300">
              <div class="text-3xl font-bold text-blue-600">${profileData.challengesSolved || 0}</div>
              <div class="text-sm">Challenges Solved</div>
            </div>
            <div class="text-center p-4 bg-white border-2 border-gray-300">
              <div class="text-3xl font-bold text-purple-600">0%</div>
              <div class="text-sm">Completion Rate</div>
            </div>
            <div class="text-center p-4 bg-white border-2 border-gray-300">
              <div class="text-3xl font-bold text-orange-600">#-</div>
              <div class="text-sm">Current Rank</div>
            </div>
          </div>
        </div>

        <!-- Fix Actions -->
        <div class="neo-brutalist bg-yellow-50 p-6 border-l-4 border-yellow-400">
          <h3 class="text-xl font-bold mb-4">⚠️ Profile Loading Issue Detected</h3>
          <p class="text-sm text-gray-700 mb-4">
            Your profile data may not be fully loaded. Try these solutions:
          </p>
          <div class="flex flex-wrap gap-4">
            <button onclick="profileTabFixer.fixProfileTab()" 
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold hover:bg-blue-600">
              🔄 RETRY LOADING
            </button>
            <button onclick="profileLoadingFixer.fixProfileLoading()" 
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold hover:bg-green-600">
              🔧 FIX PROFILE DATA
            </button>
            <button onclick="window.location.reload()" 
                    class="neo-brutalist bg-gray-500 text-white px-4 py-2 font-bold hover:bg-gray-600">
              🔄 REFRESH PAGE
            </button>
          </div>
        </div>
      </div>
    `;
  }

  // Create error profile display
  createErrorProfile(errorMessage) {
    const profileContent = document.getElementById('profile-content');
    if (!profileContent) return;

    profileContent.innerHTML = `
      <div class="text-center py-12">
        <div class="text-4xl mb-4">❌</div>
        <h3 class="text-xl font-bold mb-2 text-red-600">Profile Loading Failed</h3>
        <p class="text-gray-600 mb-6">${errorMessage}</p>
        
        <div class="space-x-4">
          <button onclick="profileTabFixer.fixProfileTab()" 
                  class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold hover:bg-blue-600">
            🔄 RETRY
          </button>
          <button onclick="window.location.reload()" 
                  class="neo-brutalist bg-gray-500 text-white px-4 py-2 font-bold hover:bg-gray-600">
            🔄 REFRESH PAGE
          </button>
        </div>
      </div>
    `;
  }

  // Show success message
  showSuccessMessage() {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-green-500 text-white p-4 max-w-md';
    successDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">✅</span>
        <h3 class="font-bold">Profile Tab Fixed!</h3>
      </div>
      <p class="text-sm">Your profile should now load correctly.</p>
      <button onclick="this.parentElement.remove()" 
              class="mt-2 bg-white text-green-500 px-3 py-1 text-sm font-bold rounded">
        CLOSE
      </button>
    `;
    
    document.body.appendChild(successDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (successDiv.parentElement) {
        successDiv.remove();
      }
    }, 5000);
  }

  // Show error message
  showErrorMessage(error) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-red-500 text-white p-4 max-w-md';
    errorDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">❌</span>
        <h3 class="font-bold">Fix Failed</h3>
      </div>
      <p class="text-sm mb-3">${error.message}</p>
      <div class="space-x-2">
        <button onclick="profileTabFixer.fixProfileTab()" 
                class="bg-white text-red-500 px-3 py-1 text-sm font-bold rounded">
          RETRY
        </button>
        <button onclick="this.parentElement.remove()" 
                class="bg-red-700 text-white px-3 py-1 text-sm font-bold rounded">
          CLOSE
        </button>
      </div>
    `;
    
    document.body.appendChild(errorDiv);
  }

  // Quick fix method for console use
  quickFix() {
    console.log('🐺 Running quick profile tab fix...');
    
    // Clear stuck states
    this.clearStuckLoadingStates();
    
    // Force click profile tab again
    const profileTab = document.getElementById('profile-tab');
    if (profileTab) {
      profileTab.click();
    }
    
    // If still stuck after 3 seconds, run full fix
    setTimeout(() => {
      const profileContent = document.getElementById('profile-content');
      if (profileContent && profileContent.textContent.includes('Loading profile')) {
        console.log('🐺 Still stuck, running full fix...');
        this.fixProfileTab();
      }
    }, 3000);
  }
}

// Initialize profile tab fixer
const profileTabFixer = new ProfileTabFixer();

// Make it globally available
window.profileTabFixer = profileTabFixer;

// Auto-detect stuck profile tab and offer fix
document.addEventListener('DOMContentLoaded', () => {
  // Monitor profile tab clicks
  const profileTab = document.getElementById('profile-tab');
  if (profileTab) {
    profileTab.addEventListener('click', () => {
      // Check if profile gets stuck loading after 8 seconds
      setTimeout(() => {
        const profileContent = document.getElementById('profile-content');
        if (profileContent && profileContent.textContent.includes('Loading profile')) {
          console.log('🐺 Detected stuck profile loading, offering fix...');
          
          const fixButton = document.createElement('button');
          fixButton.className = 'fixed bottom-4 right-4 z-50 neo-brutalist bg-orange-500 text-white px-4 py-2 font-bold hover:bg-orange-600';
          fixButton.innerHTML = '🔧 FIX PROFILE';
          fixButton.onclick = () => {
            profileTabFixer.fixProfileTab();
            fixButton.remove();
          };
          
          document.body.appendChild(fixButton);
          
          // Auto-remove button after 30 seconds
          setTimeout(() => {
            if (fixButton.parentElement) {
              fixButton.remove();
            }
          }, 30000);
        }
      }, 8000);
    });
  }
});

export default profileTabFixer;
