// Analytics Dashboard for The Wolf Challenge CTF Platform
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import scoreService from './score-service.js';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit,
  getDocs,
  startAfter,
  Timestamp
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AnalyticsDashboard {
  constructor() {
    this.analyticsData = {
      users: [],
      submissions: [],
      scoreEvents: [],
      challenges: []
    };
    this.charts = {};
    this.isLoading = false;
  }

  // Initialize analytics dashboard
  async initializeDashboard() {
    if (!authManager.isAdmin()) {
      console.warn('🐺 Analytics dashboard requires admin privileges');
      return;
    }

    console.log('🐺 Initializing Analytics Dashboard...');
    await this.loadAnalyticsData();
  }

  // Load all analytics data
  async loadAnalyticsData() {
    this.isLoading = true;
    
    try {
      await Promise.all([
        this.loadUsersData(),
        this.loadSubmissionsData(),
        this.loadScoreEventsData(),
        this.loadChallengesData()
      ]);
      
      console.log('🐺 Analytics data loaded successfully');
    } catch (error) {
      console.error('🐺 Error loading analytics data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async loadUsersData() {
    const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
    const usersSnap = await getDocs(query(usersRef, orderBy('score', 'desc')));
    this.analyticsData.users = usersSnap.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  async loadSubmissionsData() {
    const submissionsRef = collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS);
    const submissionsSnap = await getDocs(query(submissionsRef, orderBy('timestamp', 'desc'), limit(1000)));
    this.analyticsData.submissions = submissionsSnap.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  async loadScoreEventsData() {
    const eventsRef = collection(db, CTF_CONFIG.COLLECTIONS.SCORE_EVENTS);
    const eventsSnap = await getDocs(query(eventsRef, orderBy('timestamp', 'desc'), limit(500)));
    this.analyticsData.scoreEvents = eventsSnap.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  async loadChallengesData() {
    const challengesRef = collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES);
    const challengesSnap = await getDocs(query(challengesRef, orderBy('category'), orderBy('order')));
    this.analyticsData.challenges = challengesSnap.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  // Render analytics dashboard
  renderDashboard() {
    return `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">📊 Analytics Dashboard</h3>
          <div class="flex space-x-2">
            <button onclick="analyticsDashboard.refreshData()" 
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-refresh mr-2"></i>REFRESH
            </button>
            <button onclick="analyticsDashboard.exportAnalytics()" 
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-download mr-2"></i>EXPORT
            </button>
          </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          ${this.renderKeyMetrics()}
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Score Distribution -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">Score Distribution</h4>
            <div id="score-distribution-chart" class="h-64">
              ${this.renderScoreDistribution()}
            </div>
          </div>

          <!-- Activity Timeline -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">Activity Timeline</h4>
            <div id="activity-timeline-chart" class="h-64">
              ${this.renderActivityTimeline()}
            </div>
          </div>

          <!-- Category Performance -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">Category Performance</h4>
            <div id="category-performance-chart" class="h-64">
              ${this.renderCategoryPerformance()}
            </div>
          </div>

          <!-- User Engagement -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">User Engagement</h4>
            <div id="user-engagement-chart" class="h-64">
              ${this.renderUserEngagement()}
            </div>
          </div>
        </div>

        <!-- Detailed Tables -->
        <div class="space-y-6">
          <!-- Top Performers -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">Top Performers</h4>
            ${this.renderTopPerformers()}
          </div>

          <!-- Challenge Statistics -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">Challenge Statistics</h4>
            ${this.renderChallengeStats()}
          </div>
        </div>
      </div>
    `;
  }

  renderKeyMetrics() {
    const totalUsers = this.analyticsData.users.length;
    const activeUsers = this.analyticsData.users.filter(u => 
      u.lastActivity && (new Date() - u.lastActivity.toDate()) < 86400000
    ).length;
    const totalSubmissions = this.analyticsData.submissions.length;
    const avgScore = totalUsers > 0 ? 
      Math.round(this.analyticsData.users.reduce((sum, u) => sum + (u.score || 0), 0) / totalUsers) : 0;

    return `
      <div class="neo-brutalist bg-blue-50 p-4 text-center">
        <div class="text-3xl font-bold text-blue-600">${totalUsers}</div>
        <div class="text-sm">Total Users</div>
      </div>
      <div class="neo-brutalist bg-green-50 p-4 text-center">
        <div class="text-3xl font-bold text-green-600">${activeUsers}</div>
        <div class="text-sm">Active Users (24h)</div>
      </div>
      <div class="neo-brutalist bg-purple-50 p-4 text-center">
        <div class="text-3xl font-bold text-purple-600">${totalSubmissions}</div>
        <div class="text-sm">Total Submissions</div>
      </div>
      <div class="neo-brutalist bg-yellow-50 p-4 text-center">
        <div class="text-3xl font-bold text-yellow-600">${avgScore}</div>
        <div class="text-sm">Average Score</div>
      </div>
    `;
  }

  renderScoreDistribution() {
    const scoreRanges = {
      '0-50': 0,
      '51-100': 0,
      '101-200': 0,
      '201-300': 0,
      '301-400': 0,
      '401-500': 0,
      '501+': 0
    };

    this.analyticsData.users.forEach(user => {
      const score = user.score || 0;
      if (score <= 50) scoreRanges['0-50']++;
      else if (score <= 100) scoreRanges['51-100']++;
      else if (score <= 200) scoreRanges['101-200']++;
      else if (score <= 300) scoreRanges['201-300']++;
      else if (score <= 400) scoreRanges['301-400']++;
      else if (score <= 500) scoreRanges['401-500']++;
      else scoreRanges['501+']++;
    });

    return `
      <div class="space-y-2">
        ${Object.entries(scoreRanges).map(([range, count]) => `
          <div class="flex justify-between items-center">
            <span class="text-sm font-bold">${range}</span>
            <div class="flex items-center space-x-2">
              <div class="w-32 bg-gray-200 rounded-full h-4">
                <div class="bg-blue-600 h-4 rounded-full" 
                     style="width: ${this.analyticsData.users.length > 0 ? (count / this.analyticsData.users.length) * 100 : 0}%"></div>
              </div>
              <span class="text-sm w-8">${count}</span>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  renderActivityTimeline() {
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      last7Days.push({
        date: date.toLocaleDateString(),
        submissions: 0,
        activeUsers: 0
      });
    }

    // Count submissions per day
    this.analyticsData.submissions.forEach(submission => {
      if (submission.timestamp) {
        const submissionDate = submission.timestamp.toDate().toLocaleDateString();
        const dayData = last7Days.find(d => d.date === submissionDate);
        if (dayData) dayData.submissions++;
      }
    });

    const maxSubmissions = Math.max(...last7Days.map(d => d.submissions), 1);

    return `
      <div class="space-y-2">
        ${last7Days.map(day => `
          <div class="flex justify-between items-center">
            <span class="text-sm font-bold">${day.date}</span>
            <div class="flex items-center space-x-2">
              <div class="w-32 bg-gray-200 rounded-full h-4">
                <div class="bg-green-600 h-4 rounded-full" 
                     style="width: ${(day.submissions / maxSubmissions) * 100}%"></div>
              </div>
              <span class="text-sm w-8">${day.submissions}</span>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  renderCategoryPerformance() {
    const categories = {
      beginner: { solved: 0, total: 0 },
      intermediate: { solved: 0, total: 0 },
      advanced: { solved: 0, total: 0 }
    };

    this.analyticsData.users.forEach(user => {
      const progress = user.progress || {};
      Object.keys(categories).forEach(category => {
        if (progress[category]) {
          categories[category].solved += progress[category].solved || 0;
          categories[category].total += progress[category].total || 0;
        }
      });
    });

    return `
      <div class="space-y-4">
        ${Object.entries(categories).map(([category, data]) => {
          const percentage = data.total > 0 ? Math.round((data.solved / data.total) * 100) : 0;
          return `
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm font-bold capitalize">${category}</span>
                <span class="text-sm">${percentage}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-4">
                <div class="bg-${category === 'beginner' ? 'green' : category === 'intermediate' ? 'yellow' : 'red'}-600 h-4 rounded-full" 
                     style="width: ${percentage}%"></div>
              </div>
              <div class="text-xs text-gray-600 mt-1">${data.solved} / ${data.total} solved</div>
            </div>
          `;
        }).join('')}
      </div>
    `;
  }

  renderUserEngagement() {
    const engagementLevels = {
      'High (>10 challenges)': 0,
      'Medium (5-10 challenges)': 0,
      'Low (1-4 challenges)': 0,
      'None (0 challenges)': 0
    };

    this.analyticsData.users.forEach(user => {
      const solved = user.challengesSolved || 0;
      if (solved > 10) engagementLevels['High (>10 challenges)']++;
      else if (solved >= 5) engagementLevels['Medium (5-10 challenges)']++;
      else if (solved >= 1) engagementLevels['Low (1-4 challenges)']++;
      else engagementLevels['None (0 challenges)']++;
    });

    const total = this.analyticsData.users.length;

    return `
      <div class="space-y-2">
        ${Object.entries(engagementLevels).map(([level, count]) => `
          <div class="flex justify-between items-center">
            <span class="text-sm font-bold">${level}</span>
            <div class="flex items-center space-x-2">
              <div class="w-24 bg-gray-200 rounded-full h-4">
                <div class="bg-purple-600 h-4 rounded-full" 
                     style="width: ${total > 0 ? (count / total) * 100 : 0}%"></div>
              </div>
              <span class="text-sm w-8">${count}</span>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  renderTopPerformers() {
    const topUsers = this.analyticsData.users
      .sort((a, b) => (b.score || 0) - (a.score || 0))
      .slice(0, 10);

    return `
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b-2 border-black bg-gray-100">
              <th class="text-left p-3 font-bold">Rank</th>
              <th class="text-left p-3 font-bold">User</th>
              <th class="text-center p-3 font-bold">Score</th>
              <th class="text-center p-3 font-bold">Challenges</th>
              <th class="text-center p-3 font-bold">Last Active</th>
            </tr>
          </thead>
          <tbody>
            ${topUsers.map((user, index) => `
              <tr class="border-b border-gray-200">
                <td class="p-3 font-bold">#${index + 1}</td>
                <td class="p-3">${user.email || 'Unknown'}</td>
                <td class="p-3 text-center font-bold text-green-600">${user.score || 0}</td>
                <td class="p-3 text-center">${user.challengesSolved || 0}</td>
                <td class="p-3 text-center text-sm">
                  ${user.lastActivity ? new Date(user.lastActivity.toDate()).toLocaleDateString() : 'Never'}
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  renderChallengeStats() {
    const challengeStats = this.analyticsData.challenges.map(challenge => {
      const solveCount = this.analyticsData.submissions.filter(s => 
        s.challengeId === challenge.id && s.isCorrect !== false
      ).length;
      
      return {
        ...challenge,
        solveCount,
        solveRate: this.analyticsData.users.length > 0 ? 
          Math.round((solveCount / this.analyticsData.users.length) * 100) : 0
      };
    }).sort((a, b) => b.solveCount - a.solveCount);

    return `
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b-2 border-black bg-gray-100">
              <th class="text-left p-3 font-bold">Challenge</th>
              <th class="text-center p-3 font-bold">Category</th>
              <th class="text-center p-3 font-bold">Points</th>
              <th class="text-center p-3 font-bold">Solves</th>
              <th class="text-center p-3 font-bold">Solve Rate</th>
            </tr>
          </thead>
          <tbody>
            ${challengeStats.slice(0, 20).map(challenge => `
              <tr class="border-b border-gray-200">
                <td class="p-3 font-bold">${challenge.title || 'Unknown'}</td>
                <td class="p-3 text-center">
                  <span class="px-2 py-1 bg-${challenge.category === 'beginner' ? 'green' : challenge.category === 'intermediate' ? 'yellow' : 'red'}-100 text-xs rounded">
                    ${challenge.category || 'Unknown'}
                  </span>
                </td>
                <td class="p-3 text-center">${challenge.points || 0}</td>
                <td class="p-3 text-center font-bold">${challenge.solveCount}</td>
                <td class="p-3 text-center">${challenge.solveRate}%</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  // Utility methods
  async refreshData() {
    await this.loadAnalyticsData();
    // Re-render if dashboard is currently displayed
    const dashboardElement = document.getElementById('analytics-dashboard');
    if (dashboardElement) {
      dashboardElement.innerHTML = this.renderDashboard();
    }
  }

  async exportAnalytics() {
    const analyticsReport = {
      generatedAt: new Date().toISOString(),
      summary: {
        totalUsers: this.analyticsData.users.length,
        activeUsers: this.analyticsData.users.filter(u => 
          u.lastActivity && (new Date() - u.lastActivity.toDate()) < 86400000
        ).length,
        totalSubmissions: this.analyticsData.submissions.length,
        totalChallenges: this.analyticsData.challenges.length
      },
      users: this.analyticsData.users,
      submissions: this.analyticsData.submissions,
      scoreEvents: this.analyticsData.scoreEvents,
      challenges: this.analyticsData.challenges
    };

    const jsonContent = JSON.stringify(analyticsReport, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `ctf_analytics_${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// Initialize analytics dashboard
const analyticsDashboard = new AnalyticsDashboard();

// Make it globally available
window.analyticsDashboard = analyticsDashboard;

export default analyticsDashboard;
