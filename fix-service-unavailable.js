// Fix Service Unavailable Issue for The Wolf Challenge CTF Platform
// This script helps diagnose and fix common Firestore connection issues

import { db, auth, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import { 
  collection, 
  doc, 
  setDoc, 
  getDoc,
  getDocs,
  query,
  limit,
  serverTimestamp
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class ServiceUnavailableFixer {
  constructor() {
    this.diagnostics = [];
    this.fixes = [];
  }

  // Main diagnostic and fix function
  async diagnoseAndFix() {
    console.log('🐺 Starting Service Unavailable diagnostics...');
    
    try {
      // Step 1: Check authentication
      await this.checkAuthentication();
      
      // Step 2: Test Firestore connection
      await this.testFirestoreConnection();
      
      // Step 3: Check and create user document
      await this.checkUserDocument();
      
      // Step 4: Test leaderboard query
      await this.testLeaderboardQuery();
      
      // Step 5: Initialize required collections
      await this.initializeCollections();
      
      console.log('✅ All diagnostics passed! Service should be available now.');
      this.showSuccessMessage();
      
    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      this.showErrorMessage(error);
    }
  }

  async checkAuthentication() {
    console.log('🔍 Checking authentication...');
    
    const user = authManager.getCurrentUser();
    if (!user) {
      this.diagnostics.push('❌ User not authenticated');
      
      // Try to sign in with demo credentials or prompt for login
      throw new Error('Please log in first to access the leaderboard');
    }
    
    this.diagnostics.push('✅ User authenticated: ' + user.email);
    console.log('✅ Authentication check passed');
  }

  async testFirestoreConnection() {
    console.log('🔍 Testing Firestore connection...');
    
    try {
      // Try a simple read operation
      const testRef = collection(db, 'users');
      const testQuery = query(testRef, limit(1));
      
      const snapshot = await getDocs(testQuery);
      this.diagnostics.push('✅ Firestore connection successful');
      console.log('✅ Firestore connection test passed');
      
    } catch (error) {
      this.diagnostics.push('❌ Firestore connection failed: ' + error.message);
      
      if (error.code === 'permission-denied') {
        throw new Error('Firestore rules not deployed. Run: firebase deploy --only firestore:rules');
      } else if (error.code === 'unavailable') {
        throw new Error('Firestore service unavailable. Check internet connection.');
      } else {
        throw new Error('Firestore connection error: ' + error.message);
      }
    }
  }

  async checkUserDocument() {
    console.log('🔍 Checking user document...');
    
    const user = authManager.getCurrentUser();
    if (!user) return;

    try {
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        console.log('📝 Creating user document...');
        
        await setDoc(userRef, {
          email: user.email,
          role: 'participant',
          score: 0,
          challengesSolved: 0,
          progress: {
            beginner: { solved: 0, total: 10 },
            intermediate: { solved: 0, total: 20 },
            advanced: { solved: 0, total: 40 }
          },
          solvedChallenges: [],
          createdAt: serverTimestamp(),
          lastActivity: serverTimestamp(),
          rank: null
        });
        
        this.fixes.push('✅ Created user document');
        this.diagnostics.push('✅ User document created');
      } else {
        this.diagnostics.push('✅ User document exists');
      }
      
      console.log('✅ User document check passed');
      
    } catch (error) {
      this.diagnostics.push('❌ User document error: ' + error.message);
      throw new Error('Failed to create/check user document: ' + error.message);
    }
  }

  async testLeaderboardQuery() {
    console.log('🔍 Testing leaderboard query...');
    
    try {
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const leaderboardQuery = query(
        usersRef,
        limit(10)
      );
      
      const snapshot = await getDocs(leaderboardQuery);
      
      if (snapshot.empty) {
        this.diagnostics.push('⚠️ No users in leaderboard yet');
        console.log('⚠️ Leaderboard is empty but query works');
      } else {
        this.diagnostics.push(`✅ Leaderboard query successful (${snapshot.size} users)`);
        console.log('✅ Leaderboard query test passed');
      }
      
    } catch (error) {
      this.diagnostics.push('❌ Leaderboard query failed: ' + error.message);
      throw new Error('Leaderboard query failed: ' + error.message);
    }
  }

  async initializeCollections() {
    console.log('🔍 Initializing required collections...');
    
    try {
      // Create system config if it doesn't exist
      const systemConfigRef = doc(db, 'system_config', 'platform');
      const systemConfigDoc = await getDoc(systemConfigRef);
      
      if (!systemConfigDoc.exists()) {
        await setDoc(systemConfigRef, {
          name: 'The Wolf Challenge',
          version: '2.0.0',
          status: 'active',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
        
        this.fixes.push('✅ Created system configuration');
      }
      
      // Create a sample challenge if none exist
      const challengesRef = collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES);
      const challengesSnapshot = await getDocs(query(challengesRef, limit(1)));
      
      if (challengesSnapshot.empty) {
        const sampleChallengeRef = doc(challengesRef, 'welcome');
        await setDoc(sampleChallengeRef, {
          title: 'Welcome to The Wolf Challenge',
          description: 'Find the flag in this welcome message: WOLF{welcome_to_the_hunt}',
          category: 'beginner',
          points: 10,
          flag: 'WOLF{welcome_to_the_hunt}',
          order: 1,
          isActive: true,
          createdAt: serverTimestamp()
        });
        
        this.fixes.push('✅ Created sample challenge');
      }
      
      this.diagnostics.push('✅ Collections initialized');
      console.log('✅ Collection initialization completed');
      
    } catch (error) {
      this.diagnostics.push('❌ Collection initialization failed: ' + error.message);
      console.warn('⚠️ Collection initialization failed:', error.message);
      // Don't throw error here as this is not critical
    }
  }

  showSuccessMessage() {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-green-500 text-white p-4 max-w-md';
    messageDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">✅</span>
        <h3 class="font-bold">Service Fixed!</h3>
      </div>
      <p class="text-sm mb-3">The leaderboard service is now available.</p>
      <button onclick="this.parentElement.remove(); window.location.reload();" 
              class="bg-white text-green-500 px-3 py-1 text-sm font-bold rounded">
        RELOAD PAGE
      </button>
    `;
    
    document.body.appendChild(messageDiv);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (messageDiv.parentElement) {
        messageDiv.remove();
        window.location.reload();
      }
    }, 10000);
  }

  showErrorMessage(error) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-red-500 text-white p-4 max-w-md';
    messageDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">❌</span>
        <h3 class="font-bold">Service Issue</h3>
      </div>
      <p class="text-sm mb-3">${error.message}</p>
      <div class="text-xs mb-3">
        <strong>Diagnostics:</strong><br>
        ${this.diagnostics.join('<br>')}
      </div>
      <div class="space-x-2">
        <button onclick="serviceUnavailableFixer.diagnoseAndFix()" 
                class="bg-white text-red-500 px-3 py-1 text-sm font-bold rounded">
          RETRY
        </button>
        <button onclick="this.parentElement.remove()" 
                class="bg-red-700 text-white px-3 py-1 text-sm font-bold rounded">
          CLOSE
        </button>
      </div>
    `;
    
    document.body.appendChild(messageDiv);
  }

  // Quick fix method that can be called from console
  async quickFix() {
    console.log('🐺 Running quick fix for service unavailable...');
    
    try {
      // Force refresh Firebase connection
      await this.testFirestoreConnection();
      
      // Ensure user document exists
      await this.checkUserDocument();
      
      // Refresh the leaderboard
      if (window.leaderboardManager) {
        await window.leaderboardManager.attemptErrorRecovery();
      }
      
      console.log('✅ Quick fix completed');
      return true;
      
    } catch (error) {
      console.error('❌ Quick fix failed:', error);
      return false;
    }
  }
}

// Initialize the fixer
const serviceUnavailableFixer = new ServiceUnavailableFixer();

// Make it globally available
window.serviceUnavailableFixer = serviceUnavailableFixer;

// Auto-run diagnostics if leaderboard shows service unavailable
document.addEventListener('DOMContentLoaded', () => {
  // Check if we're on the leaderboard tab and it shows service unavailable
  setTimeout(() => {
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (leaderboardContent && leaderboardContent.textContent.includes('Service Unavailable')) {
      console.log('🐺 Detected service unavailable, running auto-fix...');
      serviceUnavailableFixer.diagnoseAndFix();
    }
  }, 2000);
});

export default serviceUnavailableFixer;
