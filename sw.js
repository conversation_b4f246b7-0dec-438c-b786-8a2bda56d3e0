// Service Worker for The Wolf Challenge CTF Platform
// Handles push notifications and offline functionality

const CACHE_NAME = 'wolf-ctf-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/style.css',
  '/script.js',
  '/firebase-config.js',
  '/auth.js',
  '/challenges.js',
  '/leaderboard.js',
  '/admin.js',
  '/score-service.js',
  '/notification-service.js',
  '/push-notification-service.js',
  '/analytics-dashboard.js',
  'https://cdn.tailwindcss.com',
  'https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Mono:wght@400;700&display=swap',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

// Install event - cache resources
self.addEventListener('install', event => {
  console.log('🐺 Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('🐺 Caching app shell');
        return cache.addAll(urlsToCache);
      })
      .catch(error => {
        console.error('🐺 Error caching resources:', error);
      })
  );
  
  // Force the waiting service worker to become the active service worker
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('🐺 Service Worker activating...');
  
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('🐺 Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Ensure the service worker takes control immediately
  self.clients.claim();
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', event => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip Firebase and external API requests
  if (event.request.url.includes('firebaseapp.com') || 
      event.request.url.includes('googleapis.com') ||
      event.request.url.includes('gstatic.com')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached version or fetch from network
        return response || fetch(event.request).catch(() => {
          // If both cache and network fail, return offline page
          if (event.request.destination === 'document') {
            return caches.match('/offline.html');
          }
        });
      })
  );
});

// Push event - handle incoming push notifications
self.addEventListener('push', event => {
  console.log('🐺 Push notification received');

  let notificationData = {
    title: '🐺 The Wolf Challenge',
    body: 'You have a new notification',
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    tag: 'ctf-notification',
    data: {
      url: '/'
    }
  };

  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json();
      notificationData = {
        ...notificationData,
        ...pushData
      };
    } catch (error) {
      console.error('🐺 Error parsing push data:', error);
      notificationData.body = event.data.text() || notificationData.body;
    }
  }

  // Customize notification based on type
  if (notificationData.type) {
    switch (notificationData.type) {
      case 'score_update':
        notificationData.icon = '/trophy-icon.png';
        notificationData.badge = '/trophy-badge.png';
        notificationData.tag = 'score-update';
        break;
      case 'rank_change':
        notificationData.icon = '/rank-icon.png';
        notificationData.badge = '/rank-badge.png';
        notificationData.tag = 'rank-change';
        break;
      case 'achievement':
        notificationData.icon = '/achievement-icon.png';
        notificationData.badge = '/achievement-badge.png';
        notificationData.tag = 'achievement';
        break;
      case 'admin_message':
        notificationData.icon = '/admin-icon.png';
        notificationData.badge = '/admin-badge.png';
        notificationData.tag = 'admin-message';
        notificationData.requireInteraction = true;
        break;
    }
  }

  // Add action buttons
  notificationData.actions = [
    {
      action: 'view',
      title: 'View Platform',
      icon: '/view-icon.png'
    },
    {
      action: 'dismiss',
      title: 'Dismiss',
      icon: '/dismiss-icon.png'
    }
  ];

  // Show notification
  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});

// Notification click event
self.addEventListener('notificationclick', event => {
  console.log('🐺 Notification clicked:', event.notification.tag);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Default action or 'view' action
  const urlToOpen = event.notification.data?.url || '/';

  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then(clientList => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }

      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Notification close event
self.addEventListener('notificationclose', event => {
  console.log('🐺 Notification closed:', event.notification.tag);
  
  // Track notification dismissal
  event.waitUntil(
    fetch('/api/notification-dismissed', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tag: event.notification.tag,
        timestamp: Date.now()
      })
    }).catch(error => {
      console.error('🐺 Error tracking notification dismissal:', error);
    })
  );
});

// Background sync event (for offline actions)
self.addEventListener('sync', event => {
  console.log('🐺 Background sync triggered:', event.tag);

  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Perform background sync operations
      syncOfflineActions()
    );
  }
});

// Sync offline actions when connection is restored
async function syncOfflineActions() {
  try {
    // Get offline actions from IndexedDB or localStorage
    const offlineActions = await getOfflineActions();
    
    for (const action of offlineActions) {
      try {
        await fetch(action.url, {
          method: action.method,
          headers: action.headers,
          body: action.body
        });
        
        // Remove successful action from offline storage
        await removeOfflineAction(action.id);
        
      } catch (error) {
        console.error('🐺 Error syncing offline action:', error);
      }
    }
    
  } catch (error) {
    console.error('🐺 Error during background sync:', error);
  }
}

// Helper functions for offline storage
async function getOfflineActions() {
  // Implementation would depend on your offline storage strategy
  return [];
}

async function removeOfflineAction(actionId) {
  // Implementation would depend on your offline storage strategy
  console.log('🐺 Removing offline action:', actionId);
}

// Message event - handle messages from main thread
self.addEventListener('message', event => {
  console.log('🐺 Service Worker received message:', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }

  if (event.data && event.data.type === 'CACHE_URLS') {
    event.waitUntil(
      caches.open(CACHE_NAME).then(cache => {
        return cache.addAll(event.data.urls);
      })
    );
  }
});

// Error event
self.addEventListener('error', event => {
  console.error('🐺 Service Worker error:', event.error);
});

// Unhandled rejection event
self.addEventListener('unhandledrejection', event => {
  console.error('🐺 Service Worker unhandled rejection:', event.reason);
});

console.log('🐺 Service Worker loaded successfully');
