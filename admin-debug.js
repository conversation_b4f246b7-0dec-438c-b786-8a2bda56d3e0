// Admin Panel Debug and <PERSON><PERSON>ript for The Wolf Challenge CTF Platform

class AdminDebugger {
  constructor() {
    this.debugMode = true;
  }

  // Main debug method to diagnose admin panel issues
  debugAdminPanel() {
    console.log('🐺 Starting admin panel debug...');
    
    const results = {
      authManager: this.checkAuthManager(),
      adminManager: this.checkAdminManager(),
      adminTab: this.checkAdminTab(),
      adminSection: this.checkAdminSection(),
      adminContent: this.checkAdminContent(),
      userRole: this.checkUserRole(),
      recommendations: []
    };

    this.generateRecommendations(results);
    this.displayDebugResults(results);
    
    return results;
  }

  checkAuthManager() {
    const authManager = window.authManager;
    if (!authManager) {
      return { status: 'ERROR', message: 'AuthManager not found' };
    }

    const isAdmin = authManager.isAdmin && authManager.isAdmin();
    const currentUser = authManager.getCurrentUser();
    
    return {
      status: 'OK',
      isAdmin: isAdmin,
      currentUser: currentUser ? currentUser.email : 'No user',
      userRole: authManager.userRole
    };
  }

  checkAdminManager() {
    const adminManager = window.adminManager;
    if (!adminManager) {
      return { status: 'ERROR', message: 'AdminManager not found' };
    }

    return {
      status: 'OK',
      hasRefreshMethod: typeof adminManager.refreshAdminAccess === 'function',
      hasLoadMethod: typeof adminManager.loadAdminPanel === 'function'
    };
  }

  checkAdminTab() {
    const adminTab = document.getElementById('admin-tab');
    if (!adminTab) {
      return { status: 'ERROR', message: 'Admin tab element not found' };
    }

    return {
      status: 'OK',
      isHidden: adminTab.classList.contains('hidden'),
      hasClickListener: adminTab.onclick !== null || adminTab.addEventListener
    };
  }

  checkAdminSection() {
    const adminSection = document.getElementById('admin-section');
    if (!adminSection) {
      return { status: 'ERROR', message: 'Admin section element not found' };
    }

    return {
      status: 'OK',
      isHidden: adminSection.classList.contains('hidden'),
      innerHTML: adminSection.innerHTML.length > 0
    };
  }

  checkAdminContent() {
    const adminContent = document.getElementById('admin-content');
    if (!adminContent) {
      return { status: 'ERROR', message: 'Admin content element not found' };
    }

    return {
      status: 'OK',
      innerHTML: adminContent.innerHTML.substring(0, 100) + '...',
      hasContent: adminContent.innerHTML.length > 0
    };
  }

  checkUserRole() {
    const authManager = window.authManager;
    if (!authManager) {
      return { status: 'ERROR', message: 'Cannot check user role - AuthManager not found' };
    }

    const currentUser = authManager.getCurrentUser();
    if (!currentUser) {
      return { status: 'WARNING', message: 'No user logged in' };
    }

    return {
      status: 'OK',
      email: currentUser.email,
      role: authManager.userRole,
      isAdmin: authManager.isAdmin()
    };
  }

  generateRecommendations(results) {
    const recommendations = [];

    if (results.authManager.status === 'ERROR') {
      recommendations.push('❌ AuthManager not loaded - check script imports');
    }

    if (results.adminManager.status === 'ERROR') {
      recommendations.push('❌ AdminManager not loaded - check admin.js import');
    }

    if (results.userRole.status === 'WARNING') {
      recommendations.push('⚠️ No user logged in - login as admin first');
    }

    if (results.userRole.isAdmin === false) {
      recommendations.push('⚠️ Current user is not admin - use admin credentials');
    }

    if (results.adminTab.isHidden) {
      recommendations.push('🔧 Admin tab is hidden - will show after admin login');
    }

    if (results.adminContent.hasContent === false) {
      recommendations.push('🔧 Admin content is empty - try clicking admin tab');
    }

    results.recommendations = recommendations;
  }

  displayDebugResults(results) {
    console.group('🐺 Admin Panel Debug Results');
    
    Object.keys(results).forEach(key => {
      if (key !== 'recommendations') {
        console.log(`${key}:`, results[key]);
      }
    });

    console.log('Recommendations:', results.recommendations);
    console.groupEnd();

    // Show visual debug info
    this.showDebugModal(results);
  }

  showDebugModal(results) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">🐺 Admin Panel Debug Results</h3>
          <button onclick="this.parentElement.parentElement.parentElement.remove()"
                  class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
            ✕
          </button>
        </div>

        <div class="space-y-4">
          <!-- Status Overview -->
          <div class="grid grid-cols-2 gap-4">
            <div class="neo-brutalist p-3 ${results.authManager.status === 'OK' ? 'bg-green-50' : 'bg-red-50'}">
              <div class="font-bold">Auth Manager</div>
              <div class="text-sm">${results.authManager.status}</div>
            </div>
            <div class="neo-brutalist p-3 ${results.adminManager.status === 'OK' ? 'bg-green-50' : 'bg-red-50'}">
              <div class="font-bold">Admin Manager</div>
              <div class="text-sm">${results.adminManager.status}</div>
            </div>
          </div>

          <!-- User Info -->
          <div class="neo-brutalist p-3 bg-blue-50">
            <div class="font-bold">Current User</div>
            <div class="text-sm">Email: ${results.userRole.email || 'Not logged in'}</div>
            <div class="text-sm">Role: ${results.userRole.role || 'Unknown'}</div>
            <div class="text-sm">Is Admin: ${results.userRole.isAdmin ? '✅ Yes' : '❌ No'}</div>
          </div>

          <!-- Recommendations -->
          <div class="neo-brutalist p-3 bg-yellow-50">
            <div class="font-bold mb-2">Recommendations:</div>
            ${results.recommendations.length > 0 ? 
              results.recommendations.map(rec => `<div class="text-sm">• ${rec}</div>`).join('') :
              '<div class="text-sm text-green-600">✅ Everything looks good!</div>'
            }
          </div>

          <!-- Quick Actions -->
          <div class="flex space-x-2">
            <button onclick="adminDebugger.forceShowAdminPanel()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 text-sm font-bold">
              FORCE SHOW ADMIN
            </button>
            <button onclick="adminDebugger.refreshAdminPanel()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 text-sm font-bold">
              REFRESH ADMIN
            </button>
            <button onclick="showAdminLogin()"
                    class="neo-brutalist bg-purple-500 text-white px-4 py-2 text-sm font-bold">
              ADMIN LOGIN
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  // Force show admin panel regardless of auth state
  forceShowAdminPanel() {
    console.log('🐺 Force showing admin panel...');
    
    // Show admin tab
    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
      adminTab.classList.remove('hidden');
    }

    // Show admin section
    const adminSection = document.getElementById('admin-section');
    if (adminSection) {
      adminSection.classList.remove('hidden');
    }

    // Hide other sections
    ['challenges-section', 'leaderboard-section', 'profile-section'].forEach(id => {
      const section = document.getElementById(id);
      if (section) {
        section.classList.add('hidden');
      }
    });

    // Update tab states
    document.querySelectorAll('[id$="-tab"]').forEach(tab => {
      tab.className = tab.className.replace('bg-yellow-400', 'bg-gray-300').replace('text-black', 'text-black');
    });

    if (adminTab) {
      adminTab.className = adminTab.className.replace('bg-gray-300', 'bg-red-500').replace('text-black', 'text-white');
    }

    // Load admin panel content
    if (window.adminManager) {
      window.adminManager.loadAdminPanel();
    }

    console.log('🐺 Admin panel force-shown');
  }

  // Refresh admin panel
  refreshAdminPanel() {
    console.log('🐺 Refreshing admin panel...');
    
    if (window.adminManager) {
      window.adminManager.refreshAdminAccess();
    }

    console.log('🐺 Admin panel refreshed');
  }

  // Quick admin login
  quickAdminLogin() {
    const email = '<EMAIL>';
    const password = 'tamilselvanadmin';

    // Fill login form
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');

    if (emailInput && passwordInput) {
      emailInput.value = email;
      passwordInput.value = password;
      
      // Submit form
      const loginForm = document.getElementById('login-form');
      if (loginForm) {
        loginForm.dispatchEvent(new Event('submit'));
      }
    }
  }
}

// Create global instance
const adminDebugger = new AdminDebugger();
window.adminDebugger = adminDebugger;

// Add debug button to page
document.addEventListener('DOMContentLoaded', () => {
  const debugButton = document.createElement('button');
  debugButton.className = 'fixed bottom-4 right-4 z-50 neo-brutalist bg-orange-500 text-white px-4 py-2 font-bold hover:bg-orange-600';
  debugButton.innerHTML = '🔧 DEBUG ADMIN';
  debugButton.onclick = () => {
    adminDebugger.debugAdminPanel();
  };
  
  document.body.appendChild(debugButton);
});

console.log('🐺 Admin debugger loaded - use adminDebugger.debugAdminPanel() to diagnose issues');

export default adminDebugger;
