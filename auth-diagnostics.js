// Authentication Diagnostics for The Wolf Challenge
// This module provides comprehensive debugging and troubleshooting for authentication issues

export class AuthDiagnostics {
  constructor() {
    this.diagnosticResults = {};
    this.startTime = Date.now();
  }

  async runFullDiagnostics() {
    console.log('🐺 Starting comprehensive authentication diagnostics...');
    
    const results = {
      timestamp: new Date().toISOString(),
      browser: this.getBrowserInfo(),
      network: await this.checkNetworkConnectivity(),
      firebase: await this.checkFirebaseServices(),
      dom: this.checkDOMElements(),
      security: this.checkSecuritySettings(),
      localStorage: this.checkLocalStorage(),
      summary: {}
    };

    // Generate summary
    results.summary = this.generateSummary(results);
    
    console.log('🐺 Diagnostics completed:', results);
    return results;
  }

  getBrowserInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      platform: navigator.platform,
      vendor: navigator.vendor,
      localStorage: typeof Storage !== 'undefined',
      sessionStorage: typeof sessionStorage !== 'undefined',
      indexedDB: typeof indexedDB !== 'undefined'
    };
  }

  async checkNetworkConnectivity() {
    const results = {
      online: navigator.onLine,
      firebaseReachable: false,
      googleReachable: false,
      dnsResolution: false
    };

    try {
      // Test Firebase connectivity
      const firebaseResponse = await fetch('https://wolf-ctf.firebaseapp.com/', { 
        method: 'HEAD', 
        mode: 'no-cors',
        cache: 'no-cache'
      });
      results.firebaseReachable = true;
    } catch (error) {
      console.warn('🐺 Firebase connectivity test failed:', error);
    }

    try {
      // Test Google connectivity
      const googleResponse = await fetch('https://www.google.com/', { 
        method: 'HEAD', 
        mode: 'no-cors',
        cache: 'no-cache'
      });
      results.googleReachable = true;
    } catch (error) {
      console.warn('🐺 Google connectivity test failed:', error);
    }

    return results;
  }

  async checkFirebaseServices() {
    const results = {
      sdkLoaded: false,
      appInitialized: false,
      authInitialized: false,
      firestoreInitialized: false,
      authDomain: null,
      projectId: null,
      errors: []
    };

    try {
      // Check if Firebase SDK is loaded
      if (typeof window.firebase !== 'undefined' || 
          typeof window.initializeApp !== 'undefined') {
        results.sdkLoaded = true;
      }

      // Import and check Firebase modules
      const { auth, db, app } = await import('./firebase-config.js');
      
      if (app) {
        results.appInitialized = true;
        results.projectId = app.options?.projectId;
      }

      if (auth) {
        results.authInitialized = true;
        results.authDomain = auth.config?.authDomain;
      }

      if (db) {
        results.firestoreInitialized = true;
      }

    } catch (error) {
      results.errors.push({
        type: 'import_error',
        message: error.message,
        stack: error.stack
      });
    }

    return results;
  }

  checkDOMElements() {
    const requiredElements = [
      'login-form',
      'email',
      'password',
      'login-error',
      'login-error-message',
      'loading-screen',
      'login-screen',
      'main-app'
    ];

    const results = {
      allPresent: true,
      missing: [],
      present: []
    };

    requiredElements.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        results.present.push(id);
      } else {
        results.missing.push(id);
        results.allPresent = false;
      }
    });

    return results;
  }

  checkSecuritySettings() {
    return {
      httpsProtocol: location.protocol === 'https:',
      thirdPartyCookies: this.checkThirdPartyCookies(),
      localStorageAccess: this.checkLocalStorageAccess(),
      popupBlocked: this.checkPopupBlocking(),
      corsEnabled: true // Assume true for file:// protocol
    };
  }

  checkThirdPartyCookies() {
    try {
      // Simple test for third-party cookie support
      document.cookie = 'test=1; SameSite=None; Secure';
      const cookieSet = document.cookie.includes('test=1');
      // Clean up
      document.cookie = 'test=; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      return cookieSet;
    } catch (error) {
      return false;
    }
  }

  checkLocalStorageAccess() {
    try {
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      return true;
    } catch (error) {
      return false;
    }
  }

  checkPopupBlocking() {
    try {
      const popup = window.open('', '_blank', 'width=1,height=1');
      if (popup) {
        popup.close();
        return false; // Not blocked
      }
      return true; // Blocked
    } catch (error) {
      return true; // Blocked
    }
  }

  checkLocalStorage() {
    const results = {
      available: false,
      securityLog: null,
      userPreferences: null,
      errors: []
    };

    try {
      if (typeof localStorage !== 'undefined') {
        results.available = true;
        
        // Check for security log
        const securityLog = localStorage.getItem('ctf_security_log');
        if (securityLog) {
          results.securityLog = JSON.parse(securityLog).length;
        }

        // Check for user preferences
        const userPrefs = localStorage.getItem('ctf_user_preferences');
        if (userPrefs) {
          results.userPreferences = JSON.parse(userPrefs);
        }
      }
    } catch (error) {
      results.errors.push(error.message);
    }

    return results;
  }

  generateSummary(results) {
    const issues = [];
    const recommendations = [];

    // Check for critical issues
    if (!results.network.online) {
      issues.push('No internet connection detected');
      recommendations.push('Check your internet connection');
    }

    if (!results.firebase.appInitialized) {
      issues.push('Firebase app not initialized');
      recommendations.push('Check Firebase configuration and network connectivity');
    }

    if (!results.firebase.authInitialized) {
      issues.push('Firebase Auth not initialized');
      recommendations.push('Verify Firebase Auth configuration');
    }

    if (!results.dom.allPresent) {
      issues.push(`Missing DOM elements: ${results.dom.missing.join(', ')}`);
      recommendations.push('Check HTML structure and element IDs');
    }

    if (!results.security.httpsProtocol && location.hostname !== 'localhost') {
      issues.push('Not using HTTPS protocol');
      recommendations.push('Use HTTPS for production deployment');
    }

    if (!results.security.localStorageAccess) {
      issues.push('Local storage access denied');
      recommendations.push('Check browser privacy settings');
    }

    return {
      status: issues.length === 0 ? 'healthy' : 'issues_detected',
      issueCount: issues.length,
      issues,
      recommendations,
      overallHealth: this.calculateHealthScore(results)
    };
  }

  calculateHealthScore(results) {
    let score = 100;
    
    // Deduct points for issues
    if (!results.network.online) score -= 30;
    if (!results.firebase.appInitialized) score -= 25;
    if (!results.firebase.authInitialized) score -= 25;
    if (!results.dom.allPresent) score -= 15;
    if (!results.security.localStorageAccess) score -= 5;

    return Math.max(0, score);
  }

  async generateReport() {
    const results = await this.runFullDiagnostics();
    
    const report = `
🐺 THE WOLF CHALLENGE - AUTHENTICATION DIAGNOSTICS REPORT
Generated: ${results.timestamp}
Duration: ${Date.now() - this.startTime}ms

OVERALL HEALTH: ${results.summary.overallHealth}/100
STATUS: ${results.summary.status.toUpperCase()}

CRITICAL SYSTEMS:
✓ Network Online: ${results.network.online ? 'YES' : 'NO'}
✓ Firebase App: ${results.firebase.appInitialized ? 'YES' : 'NO'}
✓ Firebase Auth: ${results.firebase.authInitialized ? 'YES' : 'NO'}
✓ DOM Elements: ${results.dom.allPresent ? 'YES' : 'NO'}

ISSUES DETECTED (${results.summary.issueCount}):
${results.summary.issues.map(issue => `• ${issue}`).join('\n')}

RECOMMENDATIONS:
${results.summary.recommendations.map(rec => `• ${rec}`).join('\n')}

BROWSER INFO:
• User Agent: ${results.browser.userAgent}
• Online: ${results.browser.onLine}
• Cookies: ${results.browser.cookieEnabled}
• Local Storage: ${results.browser.localStorage}

FIREBASE CONFIG:
• Project ID: ${results.firebase.projectId || 'Not available'}
• Auth Domain: ${results.firebase.authDomain || 'Not available'}
• Errors: ${results.firebase.errors.length}

For detailed technical information, check the browser console.
    `;

    console.log(report);
    return { results, report };
  }
}

// Auto-run diagnostics in development
if (location.hostname === 'localhost' || location.protocol === 'file:') {
  setTimeout(async () => {
    const diagnostics = new AuthDiagnostics();
    await diagnostics.generateReport();
  }, 3000);
}

export default AuthDiagnostics;
