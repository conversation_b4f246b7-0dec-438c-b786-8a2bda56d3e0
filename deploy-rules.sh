#!/bin/bash

# 🐺 The Wolf Challenge CTF - Quick Firebase Rules Deployment

echo "🐺 Deploying Firebase Security Rules..."
echo "📋 Project: wolf-ctf"
echo "🔐 VAPID Key: qEjDhUv4P-YpgaZoSNyUMy4bpdzZNlae050QYYTq07o"
echo ""

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Installing..."
    npm install -g firebase-tools
fi

# Login to Firebase (if not already logged in)
echo "🔑 Checking Firebase authentication..."
firebase login --no-localhost

# Deploy Firestore rules
echo "🚀 Deploying Firestore security rules..."
firebase deploy --only firestore:rules --project wolf-ctf

# Check deployment status
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Firebase rules deployed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure VAPID key in Firebase Console"
    echo "2. Create required Firestore indexes"
    echo "3. Test the application"
    echo ""
    echo "🔗 Firebase Console: https://console.firebase.google.com/project/wolf-ctf"
else
    echo ""
    echo "❌ Deployment failed. Please check your Firebase configuration."
    echo "💡 Make sure you have the correct permissions for the wolf-ctf project."
fi
