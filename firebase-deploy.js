// Firebase Deployment and Setup Script for The Wolf Challenge CTF Platform
// This script helps initialize the Firebase project with proper configuration

import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getAuth } from 'firebase-admin/auth';
import serviceAccount from './wolf-ctf-8e7993c7b98c.json' assert { type: 'json' };

class FirebaseDeployment {
  constructor() {
    this.app = null;
    this.db = null;
    this.auth = null;
    this.initializeAdmin();
  }

  initializeAdmin() {
    try {
      // Initialize Firebase Admin SDK
      if (getApps().length === 0) {
        this.app = initializeApp({
          credential: cert(serviceAccount),
          projectId: 'wolf-ctf'
        });
      } else {
        this.app = getApps()[0];
      }

      this.db = getFirestore(this.app);
      this.auth = getAuth(this.app);
      
      console.log('🐺 Firebase Admin SDK initialized successfully');
    } catch (error) {
      console.error('🐺 Error initializing Firebase Admin:', error);
      throw error;
    }
  }

  // Create initial admin user
  async createAdminUser(email, password = 'TempPassword123!') {
    try {
      console.log('🐺 Creating admin user:', email);

      // Create user in Firebase Auth
      const userRecord = await this.auth.createUser({
        email: email,
        password: password,
        emailVerified: true,
        displayName: 'CTF Administrator'
      });

      // Create user document in Firestore
      await this.db.collection('users').doc(userRecord.uid).set({
        email: email,
        role: 'admin',
        score: 0,
        challengesSolved: 0,
        progress: {
          beginner: { solved: 0, total: 20 },
          intermediate: { solved: 0, total: 25 },
          advanced: { solved: 0, total: 25 }
        },
        solvedChallenges: [],
        createdAt: new Date(),
        lastActivity: new Date(),
        rank: null
      });

      console.log('✅ Admin user created successfully');
      console.log('📧 Email:', email);
      console.log('🔑 Temporary Password:', password);
      console.log('⚠️  Please change the password after first login');

      return userRecord;

    } catch (error) {
      console.error('🐺 Error creating admin user:', error);
      throw error;
    }
  }

  // Initialize system collections with default data
  async initializeCollections() {
    try {
      console.log('🐺 Initializing system collections...');

      // System configuration
      await this.db.collection('system_config').doc('platform').set({
        name: 'The Wolf Challenge',
        version: '2.0.0',
        status: 'active',
        registrationOpen: true,
        competitionActive: true,
        maxUsers: 1000,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Competition settings
      await this.db.collection('competition_settings').doc('scoring').set({
        beginner: {
          challenges: 20,
          pointsPerChallenge: 10,
          category: 'beginner'
        },
        intermediate: {
          challenges: 25,
          pointsPerChallenge: 20,
          category: 'intermediate'
        },
        advanced: {
          challenges: 25,
          pointsPerChallenge: 30,
          category: 'advanced'
        },
        maxScorePerUser: 1750,
        updatedAt: new Date()
      });

      // Sample challenges (you can expand this)
      const sampleChallenges = [
        {
          id: 'welcome',
          title: 'Welcome to The Wolf Challenge',
          description: 'Find the flag in the welcome message',
          category: 'beginner',
          points: 10,
          flag: 'WOLF{welcome_to_the_hunt}',
          order: 1,
          isActive: true,
          createdAt: new Date()
        },
        {
          id: 'basic_crypto',
          title: 'Basic Cryptography',
          description: 'Decode this simple cipher',
          category: 'beginner',
          points: 10,
          flag: 'WOLF{caesar_cipher_solved}',
          order: 2,
          isActive: true,
          createdAt: new Date()
        },
        {
          id: 'web_basics',
          title: 'Web Security Basics',
          description: 'Find the hidden flag in the web page',
          category: 'intermediate',
          points: 20,
          flag: 'WOLF{inspect_element_master}',
          order: 3,
          isActive: true,
          createdAt: new Date()
        }
      ];

      // Add sample challenges
      for (const challenge of sampleChallenges) {
        await this.db.collection('challenges').doc(challenge.id).set(challenge);
      }

      console.log('✅ System collections initialized');

    } catch (error) {
      console.error('🐺 Error initializing collections:', error);
      throw error;
    }
  }

  // Set up Firestore indexes
  async setupIndexes() {
    console.log('🐺 Setting up Firestore indexes...');
    console.log('📋 Required indexes:');
    console.log('   1. users: role (asc), score (desc), lastActivity (desc)');
    console.log('   2. submissions: userId (asc), timestamp (desc)');
    console.log('   3. score_events: userId (asc), timestamp (desc)');
    console.log('   4. notifications: userId (asc), timestamp (desc), read (asc)');
    console.log('   5. audit_logs: timestamp (desc), eventType (asc)');
    console.log('');
    console.log('⚠️  Please create these indexes manually in Firebase Console:');
    console.log('   Go to Firestore > Indexes > Create Index');
  }

  // Validate Firebase configuration
  async validateConfiguration() {
    try {
      console.log('🐺 Validating Firebase configuration...');

      // Test Firestore connection
      const testDoc = await this.db.collection('system_config').doc('platform').get();
      if (!testDoc.exists) {
        throw new Error('System configuration not found');
      }

      // Test Auth connection
      const users = await this.auth.listUsers(1);
      console.log('✅ Firebase Auth connection verified');

      // Check collections
      const collections = ['users', 'challenges', 'system_config'];
      for (const collection of collections) {
        const snapshot = await this.db.collection(collection).limit(1).get();
        console.log(`✅ Collection '${collection}' accessible`);
      }

      console.log('✅ Firebase configuration validated successfully');

    } catch (error) {
      console.error('🐺 Configuration validation failed:', error);
      throw error;
    }
  }

  // Deploy security rules (informational - actual deployment via CLI)
  displaySecurityRulesInfo() {
    console.log('🐺 Security Rules Deployment:');
    console.log('');
    console.log('📋 To deploy the security rules:');
    console.log('   1. Install Firebase CLI: npm install -g firebase-tools');
    console.log('   2. Login: firebase login');
    console.log('   3. Initialize project: firebase init firestore');
    console.log('   4. Copy firestore.rules content to firestore.rules file');
    console.log('   5. Deploy: firebase deploy --only firestore:rules');
    console.log('');
    console.log('🔐 VAPID Key Configuration:');
    console.log('   1. Go to Firebase Console > Project Settings');
    console.log('   2. Navigate to Cloud Messaging tab');
    console.log('   3. Add VAPID key: qEjDhUv4P-YpgaZoSNyUMy4bpdzZNlae050QYYTq07o');
    console.log('');
  }

  // Complete setup process
  async completeSetup(adminEmail = '<EMAIL>') {
    try {
      console.log('🐺 Starting complete Firebase setup...');
      console.log('');

      // Step 1: Initialize collections
      await this.initializeCollections();

      // Step 2: Create admin user
      await this.createAdminUser(adminEmail);

      // Step 3: Setup indexes info
      this.setupIndexes();

      // Step 4: Security rules info
      this.displaySecurityRulesInfo();

      // Step 5: Validate configuration
      await this.validateConfiguration();

      console.log('');
      console.log('🎉 Firebase setup completed successfully!');
      console.log('');
      console.log('📋 Next Steps:');
      console.log('   1. Deploy Firestore security rules');
      console.log('   2. Create required indexes in Firebase Console');
      console.log('   3. Configure VAPID keys for push notifications');
      console.log('   4. Test the application with admin user');
      console.log('   5. Create additional challenges as needed');
      console.log('');
      console.log('🔗 Firebase Console: https://console.firebase.google.com/project/wolf-ctf');

    } catch (error) {
      console.error('🐺 Setup failed:', error);
      throw error;
    }
  }

  // Utility: Reset all data (DANGEROUS - use with caution)
  async resetAllData() {
    console.log('⚠️  WARNING: This will delete ALL data in the database!');
    console.log('This operation cannot be undone.');
    
    // In a real implementation, you'd want additional confirmation
    // and would batch delete all documents in all collections
    
    console.log('🐺 Reset operation not implemented for safety');
    console.log('To reset data, manually delete collections in Firebase Console');
  }

  // Export data for backup
  async exportData() {
    try {
      console.log('🐺 Exporting database data...');

      const collections = ['users', 'challenges', 'submissions', 'score_events', 'notifications'];
      const exportData = {
        exportedAt: new Date().toISOString(),
        collections: {}
      };

      for (const collectionName of collections) {
        const snapshot = await this.db.collection(collectionName).get();
        exportData.collections[collectionName] = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
      }

      const fs = await import('fs');
      const filename = `wolf-ctf-backup-${new Date().toISOString().split('T')[0]}.json`;
      fs.writeFileSync(filename, JSON.stringify(exportData, null, 2));

      console.log(`✅ Data exported to ${filename}`);
      return filename;

    } catch (error) {
      console.error('🐺 Export failed:', error);
      throw error;
    }
  }
}

// Usage examples and main execution
async function main() {
  const deployment = new FirebaseDeployment();

  try {
    // Run complete setup
    await deployment.completeSetup('<EMAIL>');

  } catch (error) {
    console.error('🐺 Deployment failed:', error);
    process.exit(1);
  }
}

// Export for use as module
export default FirebaseDeployment;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
