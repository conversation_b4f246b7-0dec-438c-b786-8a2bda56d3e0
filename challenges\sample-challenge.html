<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Challenge - The Wolf Challenge</title>
    <style>
        body {
            font-family: 'Roboto Mono', monospace;
            background-color: #f5f5f5;
            color: #1a1a1a;
            padding: 20px;
        }
        .challenge-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 4px solid #000;
            box-shadow: 8px 8px 0 #000;
            padding: 20px;
        }
        .flag {
            color: #28a745;
            font-weight: bold;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="challenge-container">
        <h1>🐺 Welcome Hunter!</h1>
        <p>This is a sample challenge for The Wolf Challenge CTF platform.</p>
        
        <h2>Challenge: Find the Hidden Flag</h2>
        <p>Your first task is to find the flag hidden in this page. Look carefully!</p>
        
        <!-- Hidden flag in HTML comment: wolf{welcome_to_the_hunt} -->
        
        <div class="hidden">
            <p class="flag">Secret: wolf{hidden_in_css}</p>
        </div>
        
        <script>
            // Another flag hidden in JavaScript
            const secretFlag = "wolf{javascript_secrets}";
            console.log("Debug info: Check the console for clues!");
        </script>
        
        <p>Hints:</p>
        <ul>
            <li>Try viewing the page source</li>
            <li>Check the browser developer tools</li>
            <li>Look for hidden elements</li>
            <li>Inspect the console</li>
        </ul>
        
        <p><strong>Flag format:</strong> wolf{...}</p>
    </div>
</body>
</html>
