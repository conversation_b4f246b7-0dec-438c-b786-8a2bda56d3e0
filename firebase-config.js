// Firebase Configuration and Initialization
import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-app.js';
import { getAuth } from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js';
import { getFirestore } from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

// Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyBWZWojxhjI3IWNo1km_XVcTXX5vYUV6Cc",
  authDomain: "wolf-ctf.firebaseapp.com",
  databaseURL: "https://wolf-ctf-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "wolf-ctf",
  storageBucket: "wolf-ctf.firebasestorage.app",
  messagingSenderId: "335565229802",
  appId: "1:335565229802:web:acb648a6bc2697ea1c9193",
  measurementId: "G-32N69ELWZN"
};

let app, auth, db;

try {
  // Initialize Firebase
  console.log('🐺 Initializing Firebase...');
  app = initializeApp(firebaseConfig);

  // Initialize Firebase services
  auth = getAuth(app);
  db = getFirestore(app);

  // Configure auth settings for better reliability
  auth.settings = {
    appVerificationDisabledForTesting: false
  };

  console.log('🐺 Firebase initialized successfully');
  console.log('🐺 Auth domain:', firebaseConfig.authDomain);
  console.log('🐺 Project ID:', firebaseConfig.projectId);

} catch (error) {
  console.error('🐺 Critical Firebase initialization error:', error);

  // Show critical error - no fallback for security
  setTimeout(() => {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'fixed inset-0 bg-red-900 text-white flex items-center justify-center z-50';
    errorDiv.innerHTML = `
      <div class="text-center max-w-md mx-4 p-8 bg-red-800 border-4 border-red-600">
        <div class="text-6xl mb-4">🚫</div>
        <h1 class="text-3xl font-bold mb-4">Service Unavailable</h1>
        <p class="text-lg mb-4">Firebase authentication service could not be initialized.</p>
        <p class="text-sm mb-6">Please check your internet connection and try again.</p>
        <button onclick="location.reload()"
                class="bg-white text-red-900 px-6 py-3 font-bold rounded">
          RETRY
        </button>
      </div>
    `;
    document.body.appendChild(errorDiv);
  }, 1000);

  throw error; // Re-throw to prevent further execution
}

// CTF Platform Configuration
export const CTF_CONFIG = {
  PLATFORM_NAME: "The Wolf Challenge",
  DEVELOPER: "S.Tamilselvan",
  SCORING: {
    BEGINNER: { challenges: 10, pointsPerChallenge: 10, totalPoints: 100 },
    INTERMEDIATE: { challenges: 20, pointsPerChallenge: 10, totalPoints: 200 },
    ADVANCED: { challenges: 40, pointsPerChallenge: 10, totalPoints: 400 }
  },
  TOTAL_POSSIBLE_SCORE: 700,
  USER_ROLES: {
    PARTICIPANT: 'participant',
    ADMIN: 'admin'
  },
  COLLECTIONS: {
    USERS: 'users',
    CHALLENGES: 'challenges',
    SUBMISSIONS: 'submissions',
    LEADERBOARD: 'leaderboard',
    SCORE_EVENTS: 'score_events',
    NOTIFICATIONS: 'notifications',
    AUDIT_LOGS: 'audit_logs'
  },
  SCORE_EVENTS: {
    CHALLENGE_SOLVED: 'challenge_solved',
    BONUS_AWARDED: 'bonus_awarded',
    PENALTY_APPLIED: 'penalty_applied',
    MANUAL_ADJUSTMENT: 'manual_adjustment'
  },
  NOTIFICATION_TYPES: {
    SCORE_UPDATE: 'score_update',
    RANK_CHANGE: 'rank_change',
    ACHIEVEMENT: 'achievement',
    ADMIN_MESSAGE: 'admin_message'
  }
};

// Export Firebase services
export { auth, db, app };

// Firestore security rules helper
export const SECURITY_RULES = {
  // Users can only read/write their own data
  userCanAccessOwnData: (userId) => `request.auth != null && request.auth.uid == '${userId}'`,
  
  // Only admins can manage challenges
  adminOnly: () => `request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'`,
  
  // Participants can read challenges but not modify
  participantCanRead: () => `request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['participant', 'admin']`
};

console.log('🐺 Firebase initialized for The Wolf Challenge');
