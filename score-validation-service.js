// Score Validation and Security Service for The Wolf Challenge CTF Platform
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import { 
  collection, 
  doc, 
  getDoc,
  setDoc, 
  updateDoc,
  query, 
  where, 
  orderBy, 
  limit,
  getDocs,
  serverTimestamp,
  writeBatch
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class ScoreValidationService {
  constructor() {
    this.validationRules = {
      maxScorePerChallenge: 1000,
      maxChallengesPerHour: 10,
      maxScoreIncreasePerHour: 5000,
      suspiciousPatterns: {
        rapidSubmissions: 5, // submissions within 1 minute
        identicalTimings: 3, // submissions with identical timing patterns
        impossibleSolveTimes: 10 // seconds (too fast to be human)
      }
    };
    this.auditLog = [];
    this.suspiciousActivities = new Map();
    this.rateLimits = new Map();
  }

  // Main validation method called before awarding points
  async validateScoreUpdate(userId, challengeId, points, metadata = {}) {
    console.log('🐺 Validating score update:', { userId, challengeId, points });

    try {
      // Run all validation checks
      const validationResults = await Promise.all([
        this.validateUser(userId),
        this.validateChallenge(challengeId, points),
        this.validateSubmissionTiming(userId, challengeId),
        this.validateRateLimit(userId),
        this.validateScoreProgression(userId, points),
        this.detectSuspiciousPatterns(userId, challengeId, metadata)
      ]);

      // Check if any validation failed
      const failures = validationResults.filter(result => !result.valid);
      
      if (failures.length > 0) {
        await this.logSecurityEvent(userId, 'VALIDATION_FAILED', {
          challengeId,
          points,
          failures: failures.map(f => f.reason),
          metadata
        });
        
        return {
          valid: false,
          reasons: failures.map(f => f.reason),
          securityLevel: this.calculateSecurityLevel(failures)
        };
      }

      // All validations passed
      await this.logSecurityEvent(userId, 'SCORE_VALIDATED', {
        challengeId,
        points,
        metadata
      });

      return { valid: true, securityLevel: 'LOW' };

    } catch (error) {
      console.error('🐺 Error during score validation:', error);
      
      await this.logSecurityEvent(userId, 'VALIDATION_ERROR', {
        challengeId,
        points,
        error: error.message,
        metadata
      });

      return {
        valid: false,
        reasons: ['Validation system error'],
        securityLevel: 'HIGH'
      };
    }
  }

  // Validate user exists and is not banned
  async validateUser(userId) {
    try {
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        return { valid: false, reason: 'User does not exist' };
      }

      const userData = userDoc.data();

      if (userData.banned) {
        return { valid: false, reason: 'User is banned' };
      }

      if (userData.role !== CTF_CONFIG.USER_ROLES.PARTICIPANT) {
        return { valid: false, reason: 'Invalid user role for scoring' };
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, reason: 'User validation error: ' + error.message };
    }
  }

  // Validate challenge exists and points are correct
  async validateChallenge(challengeId, points) {
    try {
      const challengeRef = doc(db, CTF_CONFIG.COLLECTIONS.CHALLENGES, challengeId);
      const challengeDoc = await getDoc(challengeRef);

      if (!challengeDoc.exists()) {
        return { valid: false, reason: 'Challenge does not exist' };
      }

      const challengeData = challengeDoc.data();

      if (challengeData.points !== points) {
        return { 
          valid: false, 
          reason: `Points mismatch: expected ${challengeData.points}, got ${points}` 
        };
      }

      if (points > this.validationRules.maxScorePerChallenge) {
        return { 
          valid: false, 
          reason: `Points exceed maximum allowed: ${points} > ${this.validationRules.maxScorePerChallenge}` 
        };
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, reason: 'Challenge validation error: ' + error.message };
    }
  }

  // Validate submission timing to detect automation
  async validateSubmissionTiming(userId, challengeId) {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60000);

      // Check recent submissions
      const submissionsRef = collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS);
      const recentQuery = query(
        submissionsRef,
        where('userId', '==', userId),
        where('timestamp', '>=', oneMinuteAgo),
        orderBy('timestamp', 'desc')
      );

      const recentSubmissions = await getDocs(recentQuery);

      if (recentSubmissions.size >= this.validationRules.suspiciousPatterns.rapidSubmissions) {
        return { 
          valid: false, 
          reason: `Too many rapid submissions: ${recentSubmissions.size} in 1 minute` 
        };
      }

      // Check for identical timing patterns
      const timings = recentSubmissions.docs.map(doc => {
        const timestamp = doc.data().timestamp;
        return timestamp ? timestamp.toDate().getTime() : 0;
      });

      const intervals = [];
      for (let i = 1; i < timings.length; i++) {
        intervals.push(timings[i-1] - timings[i]);
      }

      // Check for suspiciously regular intervals
      if (intervals.length >= 2) {
        const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
        const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
        
        if (variance < 1000 && intervals.length >= this.validationRules.suspiciousPatterns.identicalTimings) {
          return { 
            valid: false, 
            reason: 'Suspiciously regular submission timing detected' 
          };
        }
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, reason: 'Timing validation error: ' + error.message };
    }
  }

  // Validate rate limits
  async validateRateLimit(userId) {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 3600000);

      // Check submissions in the last hour
      const submissionsRef = collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS);
      const hourlyQuery = query(
        submissionsRef,
        where('userId', '==', userId),
        where('timestamp', '>=', oneHourAgo),
        orderBy('timestamp', 'desc')
      );

      const hourlySubmissions = await getDocs(hourlyQuery);

      if (hourlySubmissions.size >= this.validationRules.maxChallengesPerHour) {
        return { 
          valid: false, 
          reason: `Rate limit exceeded: ${hourlySubmissions.size} challenges in 1 hour` 
        };
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, reason: 'Rate limit validation error: ' + error.message };
    }
  }

  // Validate score progression patterns
  async validateScoreProgression(userId, newPoints) {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 3600000);

      // Get recent score events
      const eventsRef = collection(db, CTF_CONFIG.COLLECTIONS.SCORE_EVENTS);
      const recentQuery = query(
        eventsRef,
        where('userId', '==', userId),
        where('timestamp', '>=', oneHourAgo),
        orderBy('timestamp', 'desc')
      );

      const recentEvents = await getDocs(recentQuery);
      const totalRecentPoints = recentEvents.docs.reduce((sum, doc) => {
        return sum + (doc.data().pointsAwarded || 0);
      }, 0);

      if (totalRecentPoints + newPoints > this.validationRules.maxScoreIncreasePerHour) {
        return { 
          valid: false, 
          reason: `Score increase too rapid: ${totalRecentPoints + newPoints} points in 1 hour` 
        };
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, reason: 'Score progression validation error: ' + error.message };
    }
  }

  // Detect suspicious patterns
  async detectSuspiciousPatterns(userId, challengeId, metadata) {
    try {
      const suspiciousIndicators = [];

      // Check solve time if provided
      if (metadata.solveTime && metadata.solveTime < this.validationRules.suspiciousPatterns.impossibleSolveTimes) {
        suspiciousIndicators.push(`Impossibly fast solve time: ${metadata.solveTime}s`);
      }

      // Check for repeated identical submissions
      if (metadata.flag) {
        const submissionsRef = collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS);
        const flagQuery = query(
          submissionsRef,
          where('userId', '==', userId),
          where('flag', '==', metadata.flag),
          limit(5)
        );

        const flagSubmissions = await getDocs(flagQuery);
        if (flagSubmissions.size > 1) {
          suspiciousIndicators.push('Multiple submissions with identical flag');
        }
      }

      // Check user agent consistency (if provided)
      if (metadata.userAgent) {
        const userKey = `userAgent_${userId}`;
        const storedUserAgent = this.suspiciousActivities.get(userKey);
        
        if (storedUserAgent && storedUserAgent !== metadata.userAgent) {
          suspiciousIndicators.push('User agent changed during session');
        } else {
          this.suspiciousActivities.set(userKey, metadata.userAgent);
        }
      }

      if (suspiciousIndicators.length > 0) {
        return { 
          valid: false, 
          reason: 'Suspicious patterns detected: ' + suspiciousIndicators.join(', ') 
        };
      }

      return { valid: true };

    } catch (error) {
      return { valid: false, reason: 'Pattern detection error: ' + error.message };
    }
  }

  // Calculate security threat level
  calculateSecurityLevel(failures) {
    const highRiskKeywords = ['rapid', 'automation', 'impossible', 'banned'];
    const mediumRiskKeywords = ['rate limit', 'progression', 'timing'];

    let riskScore = 0;
    
    failures.forEach(failure => {
      const reason = failure.reason.toLowerCase();
      
      if (highRiskKeywords.some(keyword => reason.includes(keyword))) {
        riskScore += 3;
      } else if (mediumRiskKeywords.some(keyword => reason.includes(keyword))) {
        riskScore += 2;
      } else {
        riskScore += 1;
      }
    });

    if (riskScore >= 6) return 'CRITICAL';
    if (riskScore >= 4) return 'HIGH';
    if (riskScore >= 2) return 'MEDIUM';
    return 'LOW';
  }

  // Log security events
  async logSecurityEvent(userId, eventType, details) {
    try {
      const auditRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.AUDIT_LOGS));
      await setDoc(auditRef, {
        userId: userId,
        eventType: eventType,
        details: details,
        timestamp: serverTimestamp(),
        ipAddress: this.getClientIP(),
        userAgent: navigator.userAgent
      });

      // Also log locally for immediate access
      this.auditLog.push({
        userId,
        eventType,
        details,
        timestamp: new Date(),
        ipAddress: this.getClientIP()
      });

      console.log('🐺 Security event logged:', eventType, details);

    } catch (error) {
      console.error('🐺 Error logging security event:', error);
    }
  }

  // Get client IP (simplified - in production use proper IP detection)
  getClientIP() {
    // This is a simplified implementation
    // In production, you'd get this from server-side or use a service
    return 'client-ip-not-available';
  }

  // Admin function to get security reports
  async getSecurityReport(timeframe = '24h') {
    if (!authManager.isAdmin()) {
      throw new Error('Only admins can access security reports');
    }

    try {
      const now = new Date();
      let startTime;

      switch (timeframe) {
        case '1h':
          startTime = new Date(now.getTime() - 3600000);
          break;
        case '24h':
          startTime = new Date(now.getTime() - 86400000);
          break;
        case '7d':
          startTime = new Date(now.getTime() - 604800000);
          break;
        default:
          startTime = new Date(now.getTime() - 86400000);
      }

      const auditRef = collection(db, CTF_CONFIG.COLLECTIONS.AUDIT_LOGS);
      const reportQuery = query(
        auditRef,
        where('timestamp', '>=', startTime),
        orderBy('timestamp', 'desc'),
        limit(1000)
      );

      const auditDocs = await getDocs(reportQuery);
      const events = auditDocs.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Analyze events
      const analysis = this.analyzeSecurityEvents(events);

      return {
        timeframe,
        totalEvents: events.length,
        events: events,
        analysis: analysis,
        generatedAt: new Date()
      };

    } catch (error) {
      console.error('🐺 Error generating security report:', error);
      throw error;
    }
  }

  // Analyze security events for patterns
  analyzeSecurityEvents(events) {
    const analysis = {
      eventTypes: {},
      suspiciousUsers: {},
      riskLevels: { LOW: 0, MEDIUM: 0, HIGH: 0, CRITICAL: 0 },
      commonFailures: {},
      recommendations: []
    };

    events.forEach(event => {
      // Count event types
      analysis.eventTypes[event.eventType] = (analysis.eventTypes[event.eventType] || 0) + 1;

      // Track suspicious users
      if (event.eventType === 'VALIDATION_FAILED') {
        analysis.suspiciousUsers[event.userId] = (analysis.suspiciousUsers[event.userId] || 0) + 1;
        
        // Count failure reasons
        if (event.details.failures) {
          event.details.failures.forEach(failure => {
            analysis.commonFailures[failure] = (analysis.commonFailures[failure] || 0) + 1;
          });
        }
      }
    });

    // Generate recommendations
    if (analysis.eventTypes.VALIDATION_FAILED > 10) {
      analysis.recommendations.push('High number of validation failures detected - consider reviewing validation rules');
    }

    const suspiciousUserCount = Object.keys(analysis.suspiciousUsers).length;
    if (suspiciousUserCount > 5) {
      analysis.recommendations.push(`${suspiciousUserCount} users showing suspicious behavior - manual review recommended`);
    }

    return analysis;
  }

  // Get validation statistics
  getValidationStats() {
    return {
      rules: this.validationRules,
      auditLogSize: this.auditLog.length,
      suspiciousActivitiesTracked: this.suspiciousActivities.size,
      rateLimitsActive: this.rateLimits.size
    };
  }
}

// Initialize score validation service
const scoreValidationService = new ScoreValidationService();

// Make it globally available
window.scoreValidationService = scoreValidationService;

export default scoreValidationService;
