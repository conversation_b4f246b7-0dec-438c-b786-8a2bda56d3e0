<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>The Wolf Challenge - CTF Platform</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Mono:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Firebase SDK v9 -->
  <script type="module" src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app.js"></script>
  <script type="module" src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js"></script>
  <script type="module" src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js"></script>

  <link rel="stylesheet" href="./style.css">
</head>
<body class="bg-gray-100 min-h-screen">
  <!-- Loading Screen -->
  <div id="loading-screen" class="fixed inset-0 bg-black text-white flex items-center justify-center z-50">
    <div class="text-center">
      <div class="text-6xl mb-4">🐺</div>
      <h1 class="text-4xl font-bold mb-2">THE WOLF CHALLENGE</h1>
      <p id="loading-message" class="text-lg">Loading CTF Platform...</p>
      <div class="mt-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
      </div>
      <div class="mt-4">
        <button id="skip-loading" class="hidden bg-yellow-400 text-black px-4 py-2 font-bold rounded hover:bg-yellow-500">
          SKIP TO LOGIN
        </button>
      </div>
    </div>
  </div>

  <!-- Login Screen -->
  <div id="login-screen" class="hidden fixed inset-0 bg-gray-900 text-white flex items-center justify-center z-40">
    <div class="neo-brutalist bg-white text-black p-8 max-w-md w-full mx-4">
      <div class="text-center mb-6">
        <div class="text-5xl mb-4">🐺</div>
        <h1 class="text-4xl font-bold mb-2">THE WOLF CHALLENGE</h1>
        <p class="text-lg text-gray-600">Capture The Flag Platform</p>
      </div>

      <form id="login-form" class="space-y-4" data-no-csrf="true">
        <div>
          <label class="block text-lg font-bold mb-2">Email</label>
          <input type="email" id="email" required
                 class="neo-brutalist w-full p-3 text-lg bg-white border-4 border-black focus:outline-none"
                 placeholder="Enter your authorized email">
        </div>
        <div>
          <label class="block text-lg font-bold mb-2">Password</label>
          <input type="password" id="password" required
                 class="neo-brutalist w-full p-3 text-lg bg-white border-4 border-black focus:outline-none"
                 placeholder="Enter your secure password">
        </div>
        <button type="submit"
                class="neo-brutalist w-full bg-red-500 text-white text-lg font-bold py-3 px-6 hover:bg-red-600">
          <i class="fas fa-sign-in-alt mr-2"></i>LOGIN TO HUNT
        </button>

        <!-- Security Notice -->
        <div class="mt-4 p-3 bg-red-100 border-4 border-red-500 text-red-800">
          <i class="fas fa-shield-alt mr-2"></i>
          <strong>Secure Access Only:</strong> Only authorized participant accounts can access this platform.
        </div>

        
      </form>

      <div id="login-error" class="hidden mt-4 p-3 bg-red-100 border-4 border-red-500 text-red-700">
        <i class="fas fa-exclamation-triangle mr-2"></i>
        <span id="login-error-message"></span>
      </div>


    </div>
  </div>

  <!-- Main Application -->
  <div id="main-app" class="hidden">
    <!-- Notification Panel (Left Side) -->
    <div id="notification-panel" class="fixed left-0 top-0 h-full w-80 bg-white shadow-2xl transform -translate-x-full transition-transform duration-300 ease-in-out z-50 border-r-4 border-black">
      <div class="h-full flex flex-col">
        <!-- Notification Header -->
        <div class="neo-brutalist bg-black text-white p-4 flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <i class="fas fa-bell text-xl"></i>
            <h2 class="text-lg font-bold">NOTIFICATIONS</h2>
          </div>
          <button onclick="toggleNotificationPanel()" class="text-white hover:text-red-400 text-xl">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Notification Filters -->
        <div class="p-4 border-b-2 border-gray-200">
          <div class="flex space-x-2">
            <button onclick="filterNotifications('all')" id="filter-all"
                    class="notification-filter-btn neo-brutalist bg-blue-500 text-white px-3 py-1 text-sm font-bold">
              ALL
            </button>
            <button onclick="filterNotifications('unread')" id="filter-unread"
                    class="notification-filter-btn neo-brutalist bg-gray-300 text-black px-3 py-1 text-sm font-bold">
              UNREAD
            </button>
            <button onclick="filterNotifications('important')" id="filter-important"
                    class="notification-filter-btn neo-brutalist bg-gray-300 text-black px-3 py-1 text-sm font-bold">
              IMPORTANT
            </button>
          </div>
        </div>

        <!-- Notification List -->
        <div class="flex-1 overflow-y-auto">
          <div id="notification-list" class="p-4">
            <div class="text-center py-8 text-gray-500">
              <i class="fas fa-bell-slash text-4xl mb-4"></i>
              <div class="text-lg font-bold">No notifications yet</div>
              <div class="text-sm">You'll see notifications from admins here</div>
            </div>
          </div>
        </div>

        <!-- Notification Actions -->
        <div class="p-4 border-t-2 border-gray-200">
          <div class="flex space-x-2">
            <button onclick="markAllAsRead()" class="flex-1 neo-brutalist bg-green-500 text-white py-2 text-sm font-bold">
              <i class="fas fa-check-double mr-1"></i>MARK ALL READ
            </button>
            <button onclick="clearAllNotifications()" class="flex-1 neo-brutalist bg-red-500 text-white py-2 text-sm font-bold">
              <i class="fas fa-trash mr-1"></i>CLEAR ALL
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Notification Overlay -->
    <div id="notification-overlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-40" onclick="toggleNotificationPanel()"></div>

    <!-- Navigation Header -->
    <nav class="neo-brutalist bg-black text-white p-4 mb-6">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <div class="logo-container">
            <img src="https://cyberwolf-career-guidance.web.app/logo/WhatsApp%20Image%202025-03-22%20at%2011,19,00%20AM-photoaidcom-cropped.jpeg" alt="Wolf Logo" class="logo-round" />
          </div>
          
          <h1 class="text-2xl font-bold text-white">THE WOLF CHALLENGE</h1>
        </div>

        <div class="flex items-center space-x-4">
          <!-- Notification Icon -->
          <div class="relative">
            <button id="notification-bell" onclick="toggleNotificationPanel()"
                    class="relative p-2 text-yellow-400 hover:text-yellow-300 transition-all duration-200 hover:scale-110">
              <i class="fas fa-bell text-xl drop-shadow-lg"></i>
              <span id="notification-badge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold hidden animate-pulse">
                0
              </span>
            </button>
          </div>

          <div id="user-info" class="flex items-center space-x-2">
            <i class="fas fa-user"></i>
            <span id="user-email"></span>
            <span id="user-role" class="px-2 py-1 bg-yellow-400 text-black text-sm font-bold"></span>
          </div>
          <button id="logout-btn" class="neo-brutalist bg-red-500 text-white px-4 py-2 text-sm font-bold">
            <i class="fas fa-sign-out-alt mr-1"></i>LOGOUT
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content Area -->
    <div class="container mx-auto px-4">
      <!-- Dashboard Navigation -->
      <div class="flex flex-wrap gap-4 mb-6">
        <button id="challenges-tab" class="neo-brutalist bg-yellow-400 text-black px-6 py-3 text-lg font-bold">
          <i class="fas fa-flag mr-2"></i>CHALLENGES
        </button>
        <button id="leaderboard-tab" class="neo-brutalist bg-gray-300 text-black px-6 py-3 text-lg font-bold">
          <i class="fas fa-trophy mr-2"></i>LEADERBOARD
        </button>
        <button id="profile-tab" class="neo-brutalist bg-gray-300 text-black px-6 py-3 text-lg font-bold">
          <i class="fas fa-user mr-2"></i>PROFILE
        </button>
        <button id="admin-tab" class="hidden neo-brutalist bg-red-500 text-white px-6 py-3 text-lg font-bold">
          <i class="fas fa-cog mr-2"></i>ADMIN
        </button>
      </div>

      <!-- Content Sections -->
      <div id="content-area">
        <!-- Challenges Section -->
        <div id="challenges-section" class="space-y-6">
          <div class="neo-brutalist bg-white p-6">
            <h2 class="text-3xl font-bold mb-4">🎯 Your Progress</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-4xl font-bold text-green-600" id="total-score">0</div>
                <div class="text-lg">Total Score</div>
                <div class="text-sm text-gray-500">/ 700 points</div>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-blue-600" id="challenges-solved">0</div>
                <div class="text-lg">Challenges Solved</div>
                <div class="text-sm text-gray-500">/ 70 challenges</div>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-purple-600" id="current-rank">#-</div>
                <div class="text-lg">Current Rank</div>
                <div class="text-sm text-gray-500">Global</div>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-yellow-600" id="completion-rate">0%</div>
                <div class="text-lg">Completion</div>
                <div class="text-sm text-gray-500">Overall</div>
              </div>
            </div>

            <!-- Detailed Scoring Breakdown -->
            <div class="mt-6 neo-brutalist bg-gray-50 p-4">
              <h3 class="text-lg font-bold mb-3">📊 Scoring Breakdown</h3>
              <div id="scoring-breakdown">
                <!-- Scoring breakdown will be populated here -->
              </div>
            </div>
          </div>

          <!-- Challenge Categories -->
          <div class="space-y-6" id="challenge-categories">
            <!-- Categories will be loaded here -->
          </div>
        </div>

        <!-- Leaderboard Section -->
        <div id="leaderboard-section" class="hidden">
          <!-- Service Fix Panel (hidden by default) -->
          <div id="service-fix-panel" class="hidden mb-4 p-4 bg-yellow-50 border-l-4 border-yellow-400">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-bold text-yellow-800">Service Issue Detected</h4>
                <p class="text-sm text-yellow-700">Click to diagnose and fix connection issues</p>
              </div>
              <button onclick="serviceUnavailableFixer.diagnoseAndFix()"
                      class="neo-brutalist bg-yellow-500 text-white px-4 py-2 font-bold hover:bg-yellow-600">
                🔧 FIX SERVICE
              </button>
            </div>
          </div>

          <div class="neo-brutalist bg-white p-6">
            <h2 class="text-3xl font-bold mb-6">🏆 LEADERBOARD</h2>
            <div id="leaderboard-content">
              <!-- Leaderboard will be loaded here -->
            </div>
          </div>
        </div>

        <!-- Profile Section -->
        <div id="profile-section" class="hidden">
          <div class="neo-brutalist bg-white p-6">
            <h2 class="text-3xl font-bold mb-6">👤 PROFILE</h2>
            <div id="profile-content">
              <!-- Profile will be loaded here -->
            </div>
          </div>
        </div>

        <!-- Admin Section -->
        <div id="admin-section" class="hidden">
          <div class="neo-brutalist bg-white p-6">
            <h2 class="text-3xl font-bold mb-6">⚙️ ADMIN PANEL</h2>
            <div id="admin-content">
              <!-- Admin panel will be loaded here -->
              <div class="text-center py-8">
                <div class="text-blue-500 text-xl font-bold mb-4">🔄 Loading Admin Panel...</div>
                <p class="text-gray-600">Please wait while we initialize the admin interface.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Challenge Modal -->
  <div id="challenge-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30">
    <div class="neo-brutalist bg-white max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 id="challenge-title" class="text-3xl font-bold"></h3>
          <button id="close-challenge" class="text-2xl font-bold hover:text-red-500">×</button>
        </div>
        <div id="challenge-content">
          <!-- Challenge content will be loaded here -->
        </div>
      </div>
    </div>
  </div>

  

  <!-- Developer Credit -->
  <div class="fixed bottom-4 right-4 neo-brutalist bg-yellow-400 text-black px-4 py-2 text-sm font-bold">
    Developed by S.Tamilselvan
  </div>

  <!-- Advanced Security Protection -->
  <script>
    // Advanced Security Features
    (function() {
      'use strict';

      // Disable right-click context menu
      document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        showSecurityAlert('Right-click disabled for security purposes');
        return false;
      });

      // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S
      document.addEventListener('keydown', function(e) {
        // F12 - Developer Tools
        if (e.keyCode === 123) {
          e.preventDefault();
          showSecurityAlert('Developer tools access denied');
          return false;
        }

        // Ctrl+Shift+I - Developer Tools
        if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
          e.preventDefault();
          showSecurityAlert('Developer tools access denied');
          return false;
        }

        // Ctrl+U - View Source
        if (e.ctrlKey && e.keyCode === 85) {
          e.preventDefault();
          showSecurityAlert('Source code viewing disabled');
          return false;
        }

        // Ctrl+S - Save Page
        if (e.ctrlKey && e.keyCode === 83) {
          e.preventDefault();
          showSecurityAlert('Page saving disabled');
          return false;
        }

        // Ctrl+A - Select All
        if (e.ctrlKey && e.keyCode === 65) {
          e.preventDefault();
          showSecurityAlert('Text selection disabled');
          return false;
        }

        // Ctrl+P - Print
        if (e.ctrlKey && e.keyCode === 80) {
          e.preventDefault();
          showSecurityAlert('Printing disabled');
          return false;
        }

        // Ctrl+Shift+C - Inspect Element
        if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
          e.preventDefault();
          showSecurityAlert('Element inspection disabled');
          return false;
        }

        // Ctrl+Shift+J - Console
        if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
          e.preventDefault();
          showSecurityAlert('Console access denied');
          return false;
        }
      });

      // Disable text selection
      document.addEventListener('selectstart', function(e) {
        e.preventDefault();
        return false;
      });

      // Disable drag and drop
      document.addEventListener('dragstart', function(e) {
        e.preventDefault();
        return false;
      });

      // Disable image saving
      document.addEventListener('dragstart', function(e) {
        if (e.target.tagName === 'IMG') {
          e.preventDefault();
          return false;
        }
      });

      // Clear console periodically
      setInterval(function() {
        console.clear();
        console.log('%c🐺 THE WOLF CHALLENGE - SECURE ACCESS ONLY', 'color: red; font-size: 20px; font-weight: bold;');
        console.log('%cUnauthorized access attempts are logged and monitored.', 'color: orange; font-size: 14px;');
      }, 1000);

      // Detect developer tools
      let devtools = {open: false, orientation: null};
      setInterval(function() {
        if (window.outerHeight - window.innerHeight > 200 ||
            window.outerWidth - window.innerWidth > 200) {
          if (!devtools.open) {
            devtools.open = true;
            showSecurityAlert('Developer tools detected! Access denied.');
            // Redirect to 404 page
            setTimeout(() => {
              window.location.href = 'about:blank';
            }, 2000);
          }
        } else {
          devtools.open = false;
        }
      }, 500);

      // Disable common bypass methods
      window.addEventListener('resize', function() {
        if (window.outerHeight - window.innerHeight > 200) {
          showSecurityAlert('Unauthorized window manipulation detected');
        }
      });

      // Security alert function
      function showSecurityAlert(message) {
        const alert = document.createElement('div');
        alert.style.cssText = `
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: #dc3545;
          color: white;
          padding: 20px;
          border: 4px solid #000;
          box-shadow: 8px 8px 0 #000;
          z-index: 10000;
          font-family: 'Roboto Mono', monospace;
          font-weight: bold;
          text-align: center;
          max-width: 400px;
        `;
        alert.innerHTML = `
          <div style="font-size: 24px; margin-bottom: 10px;">🚫</div>
          <div style="font-size: 16px; margin-bottom: 10px;">${message}</div>
          <div style="font-size: 12px;">Security violation logged</div>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
          if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
          }
        }, 3000);
      }

      // Make security functions inaccessible
      window.showSecurityAlert = undefined;
      delete window.showSecurityAlert;

    })();
  </script>

  <!-- Loading Management Script -->
  <script>
    // Loading timeout and fallback system
    let loadingTimeout;
    let modulesLoaded = false;

    function updateLoadingMessage(message) {
      const loadingMessage = document.getElementById('loading-message');
      if (loadingMessage) {
        loadingMessage.textContent = message;
      }
    }

    function showSkipButton() {
      const skipButton = document.getElementById('skip-loading');
      if (skipButton) {
        skipButton.classList.remove('hidden');
        skipButton.addEventListener('click', skipToLogin);
      }
    }

    function skipToLogin() {
      console.log('🐺 Proceeding to secure login screen');
      document.getElementById('loading-screen').classList.add('hidden');
      document.getElementById('login-screen').classList.remove('hidden');
    }

    function handleLoadingTimeout() {
      if (!modulesLoaded) {
        console.warn('🐺 Loading timeout - modules may have failed to load');
        updateLoadingMessage('Loading taking longer than expected...');
        showSkipButton();

        // Auto-skip after additional 5 seconds
        setTimeout(() => {
          if (!modulesLoaded) {
            console.log('🐺 Auto-skipping to login after timeout');
            skipToLogin();
          }
        }, 5000);
      }
    }

    // Set loading timeout (10 seconds)
    loadingTimeout = setTimeout(handleLoadingTimeout, 10000);

    // Mark modules as loaded when they finish
    window.markModulesLoaded = function() {
      modulesLoaded = true;
      clearTimeout(loadingTimeout);
    };

    // Fallback: Show login after 15 seconds regardless
    setTimeout(() => {
      if (document.getElementById('loading-screen').style.display !== 'none' &&
          !document.getElementById('loading-screen').classList.contains('hidden')) {
        console.log('🐺 Fallback: Forcing login screen after 15 seconds');
        skipToLogin();
      }
    }, 15000);
  </script>

  <!-- Scripts -->
  <script type="module" src="./firebase-config.js"></script>
  <script type="module" src="./auth.js"></script>
  <script type="module" src="./challenges.js"></script>
  <script type="module" src="./dashboard.js"></script>
  <script type="module" src="./leaderboard.js"></script>
  <script type="module" src="./admin.js"></script>
  <script type="module" src="./security.js"></script>
  <script type="module" src="./script.js"></script>

  <!-- Notification System Script -->
  <script>
    // Notification System for Participants
    let notificationPanel = null;
    let notificationOverlay = null;
    let notificationList = null;
    let notificationBadge = null;
    let currentFilter = 'all';
    let userNotifications = [];

    // Initialize notification system
    function initializeNotificationSystem() {
      notificationPanel = document.getElementById('notification-panel');
      notificationOverlay = document.getElementById('notification-overlay');
      notificationList = document.getElementById('notification-list');
      notificationBadge = document.getElementById('notification-badge');

      // Load existing notifications
      loadUserNotifications();

      // Check for new notifications every 30 seconds
      setInterval(checkForNewNotifications, 30000);

      console.log('🔔 Notification system initialized');
    }

    // Toggle notification panel
    function toggleNotificationPanel() {
      if (!notificationPanel || !notificationOverlay) return;

      const isOpen = !notificationPanel.classList.contains('-translate-x-full');

      if (isOpen) {
        // Close panel
        notificationPanel.classList.add('-translate-x-full');
        notificationOverlay.classList.add('hidden');
      } else {
        // Open panel
        notificationPanel.classList.remove('-translate-x-full');
        notificationOverlay.classList.remove('hidden');
        // Mark notifications as read when panel is opened
        setTimeout(markVisibleAsRead, 500);
      }
    }

    // Load user notifications from localStorage and admin notifications
    function loadUserNotifications() {
      // Get global notifications (sent by admin to all users)
      const globalNotifications = JSON.parse(localStorage.getItem('ctf_global_notifications') || '[]');

      // Get user-specific notifications
      const userEmail = authManager?.getCurrentUser()?.email || 'unknown';
      const userNotificationKey = `ctf_user_notifications_${userEmail}`;
      const userSpecificNotifications = JSON.parse(localStorage.getItem(userNotificationKey) || '[]');

      // Get user's read status for global notifications
      const readStatusKey = `ctf_notification_read_${userEmail}`;
      const readStatus = JSON.parse(localStorage.getItem(readStatusKey) || '{}');

      // Combine and sort notifications
      userNotifications = [
        ...globalNotifications.map(n => ({
          ...n,
          id: n.id || Date.now() + Math.random(),
          isRead: readStatus[n.id] || false,
          isImportant: n.type === 'error' || n.type === 'warning',
          source: 'admin'
        })),
        ...userSpecificNotifications
      ].sort((a, b) => new Date(b.sentAt) - new Date(a.sentAt));

      displayNotifications();
      updateNotificationBadge();
    }

    // Display notifications in the panel
    function displayNotifications() {
      if (!notificationList) return;

      let filteredNotifications = userNotifications;

      // Apply filters
      switch (currentFilter) {
        case 'unread':
          filteredNotifications = userNotifications.filter(n => !n.isRead);
          break;
        case 'important':
          filteredNotifications = userNotifications.filter(n => n.isImportant);
          break;
        default:
          filteredNotifications = userNotifications;
      }

      if (filteredNotifications.length === 0) {
        notificationList.innerHTML = `
          <div class="text-center py-8 text-gray-500">
            <i class="fas fa-bell-slash text-4xl mb-4"></i>
            <div class="text-lg font-bold">No ${currentFilter === 'all' ? '' : currentFilter} notifications</div>
            <div class="text-sm">You'll see notifications from admins here</div>
          </div>
        `;
        return;
      }

      notificationList.innerHTML = filteredNotifications.map(notification => `
        <div class="notification-item neo-brutalist bg-${notification.isRead ? 'gray-50' : 'blue-50'} p-4 mb-3 ${!notification.isRead ? 'border-l-4 border-blue-500' : ''}">
          <div class="flex justify-between items-start mb-2">
            <div class="flex items-center space-x-2">
              <i class="fas ${getNotificationIcon(notification.type)} ${getNotificationColor(notification.type)}"></i>
              <span class="font-bold text-sm">${notification.title}</span>
              ${!notification.isRead ? '<span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">NEW</span>' : ''}
              ${notification.isImportant ? '<span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">!</span>' : ''}
            </div>
            <button onclick="removeNotification('${notification.id}')" class="text-gray-400 hover:text-red-500">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <p class="text-sm text-gray-700 mb-2">${notification.message}</p>
          <div class="flex justify-between items-center text-xs text-gray-500">
            <span>${getTimeAgo(notification.sentAt)}</span>
            <span class="px-2 py-1 bg-gray-200 rounded">${notification.source === 'admin' ? 'ADMIN' : 'SYSTEM'}</span>
          </div>
        </div>
      `).join('');
    }

    // Get notification icon based on type
    function getNotificationIcon(type) {
      const icons = {
        info: 'fa-info-circle',
        success: 'fa-check-circle',
        warning: 'fa-exclamation-triangle',
        error: 'fa-times-circle',
        announcement: 'fa-bullhorn',
        challenge: 'fa-flag'
      };
      return icons[type] || 'fa-bell';
    }

    // Get notification color based on type
    function getNotificationColor(type) {
      const colors = {
        info: 'text-blue-500',
        success: 'text-green-500',
        warning: 'text-yellow-500',
        error: 'text-red-500',
        announcement: 'text-purple-500',
        challenge: 'text-indigo-500'
      };
      return colors[type] || 'text-gray-500';
    }

    // Update notification badge count
    function updateNotificationBadge() {
      if (!notificationBadge) return;

      const unreadCount = userNotifications.filter(n => !n.isRead).length;
      const notificationBell = document.getElementById('notification-bell');

      if (unreadCount > 0) {
        notificationBadge.textContent = unreadCount > 99 ? '99+' : unreadCount;
        notificationBadge.classList.remove('hidden');

        // Add enhanced animations for new notifications
        notificationBadge.classList.add('animate-pulse', 'notification-badge-new');

        // Add bell ring and glow animation
        if (notificationBell) {
          notificationBell.classList.add('bell-ring', 'bell-glow');

          // Remove animations after they complete
          setTimeout(() => {
            notificationBell.classList.remove('bell-ring');
          }, 1200);

          setTimeout(() => {
            notificationBell.classList.remove('bell-glow');
            notificationBadge.classList.remove('animate-pulse', 'notification-badge-new');
          }, 4000);
        }
      } else {
        notificationBadge.classList.add('hidden');
        if (notificationBell) {
          notificationBell.classList.remove('bell-ring', 'bell-glow');
        }
      }
    }

    // Filter notifications
    function filterNotifications(filter) {
      currentFilter = filter;

      // Update filter button styles
      document.querySelectorAll('.notification-filter-btn').forEach(btn => {
        btn.className = 'notification-filter-btn neo-brutalist bg-gray-300 text-black px-3 py-1 text-sm font-bold';
      });

      const activeBtn = document.getElementById(`filter-${filter}`);
      if (activeBtn) {
        activeBtn.className = 'notification-filter-btn neo-brutalist bg-blue-500 text-white px-3 py-1 text-sm font-bold';
      }

      displayNotifications();
    }

    // Mark all notifications as read
    function markAllAsRead() {
      userNotifications.forEach(n => n.isRead = true);
      saveUserNotifications();
      displayNotifications();
      updateNotificationBadge();
    }

    // Mark visible notifications as read
    function markVisibleAsRead() {
      userNotifications.forEach(n => {
        if (!n.isRead) n.isRead = true;
      });
      saveUserNotifications();
      updateNotificationBadge();
    }

    // Clear all notifications
    function clearAllNotifications() {
      if (confirm('Are you sure you want to clear all notifications?')) {
        userNotifications = [];
        saveUserNotifications();
        displayNotifications();
        updateNotificationBadge();
      }
    }

    // Remove specific notification
    function removeNotification(notificationId) {
      userNotifications = userNotifications.filter(n => n.id !== notificationId);
      saveUserNotifications();
      displayNotifications();
      updateNotificationBadge();
    }

    // Save user notifications to localStorage
    function saveUserNotifications() {
      const userEmail = authManager?.getCurrentUser()?.email || 'unknown';
      const userNotificationKey = `ctf_user_notifications_${userEmail}`;
      const readStatusKey = `ctf_notification_read_${userEmail}`;

      // Save user-specific notifications
      const userSpecificNotifications = userNotifications.filter(n => n.source !== 'admin');
      localStorage.setItem(userNotificationKey, JSON.stringify(userSpecificNotifications));

      // Save read status for global notifications
      const readStatus = {};
      userNotifications.filter(n => n.source === 'admin').forEach(n => {
        readStatus[n.id] = n.isRead;
      });
      localStorage.setItem(readStatusKey, JSON.stringify(readStatus));
    }

    // Check for new notifications (called periodically)
    function checkForNewNotifications() {
      const previousCount = userNotifications.length;
      loadUserNotifications();

      if (userNotifications.length > previousCount) {
        // Show notification toast
        showNotificationToast('New notification received!');
      }
    }

    // Show notification toast
    function showNotificationToast(message) {
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black px-4 py-3 rounded-lg shadow-2xl z-50 transform translate-x-full transition-all duration-300 neo-brutalist';
      toast.innerHTML = `
        <div class="flex items-center space-x-3">
          <i class="fas fa-bell text-lg animate-pulse"></i>
          <span class="font-bold">${message}</span>
          <i class="fas fa-times cursor-pointer hover:text-red-600 ml-2" onclick="this.parentElement.parentElement.remove()"></i>
        </div>
      `;

      document.body.appendChild(toast);

      // Slide in with bounce effect
      setTimeout(() => {
        toast.classList.remove('translate-x-full');
        toast.classList.add('animate-bounce');

        // Remove bounce after animation
        setTimeout(() => {
          toast.classList.remove('animate-bounce');
        }, 1000);
      }, 100);

      // Auto slide out and remove
      setTimeout(() => {
        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
          if (document.body.contains(toast)) {
            document.body.removeChild(toast);
          }
        }, 300);
      }, 4000);
    }

    // Get time ago string
    function getTimeAgo(dateString) {
      const now = new Date();
      const date = new Date(dateString);
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return 'Just now';
      if (diffMins < 60) return `${diffMins}m ago`;
      if (diffHours < 24) return `${diffHours}h ago`;
      return `${diffDays}d ago`;
    }

    // Listen for localStorage changes (for real-time notifications)
    window.addEventListener('storage', (e) => {
      if (e.key === 'ctf_global_notifications' || e.key === 'ctf_notification_trigger') {
        console.log('🔔 New notification detected from admin');
        loadUserNotifications();
        showNotificationToast('New notification from admin!');
      }
    });

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initializeNotificationSystem, 1000);
    });

    // Add notification when admin sends one (called by admin panel)
    function addNotificationForUsers(notification) {
      // This function can be called by the admin panel to add notifications
      userNotifications.unshift({
        ...notification,
        id: Date.now() + Math.random(),
        isRead: false,
        isImportant: notification.type === 'error' || notification.type === 'warning',
        source: 'admin'
      });

      displayNotifications();
      updateNotificationBadge();
      showNotificationToast(notification.title);
    }

    // Make functions globally available
    window.toggleNotificationPanel = toggleNotificationPanel;
    window.filterNotifications = filterNotifications;
    window.markAllAsRead = markAllAsRead;
    window.clearAllNotifications = clearAllNotifications;
    window.removeNotification = removeNotification;
    window.addNotificationForUsers = addNotificationForUsers;
  </script>

  <!-- Admin Debug Script -->
  <script>
    // Debug admin manager loading
    setTimeout(() => {
      console.log('🐺 Admin Manager Debug Check:');
      console.log('window.adminManager:', window.adminManager);
      console.log('Admin content element:', document.getElementById('admin-content'));
      console.log('Admin section element:', document.getElementById('admin-section'));
      console.log('Admin tab element:', document.getElementById('admin-tab'));

      if (window.adminManager) {
        console.log('✅ Admin manager is loaded');
      } else {
        console.error('❌ Admin manager is NOT loaded');
      }
    }, 2000);
  </script>

  <!-- Admin Login Functions -->
  <script>

    // Show admin login modal
    function showAdminLogin() {
      // Remove any existing modal
      const existingModal = document.getElementById('admin-login-modal');
      if (existingModal) {
        existingModal.remove();
      }

      // Admin credentials
      const adminCredentials = [
        {
          email: '<EMAIL>',
          password: 'tamilselvanadmin',
          displayName: 'Tamil Selvan Admin'
        }
      ];

      // Create modal
      const modal = document.createElement('div');
      modal.id = 'admin-login-modal';
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      modal.innerHTML = `
        <div class="neo-brutalist bg-white p-8 max-w-md mx-4">
          <h2 class="text-2xl font-bold mb-6 text-center">👑 Admin Login</h2>

          <div class="space-y-4 mb-6">
            <div>
              <label class="block text-sm font-bold mb-2">Admin Email:</label>
              <input type="email" id="admin-email-select"
                     class="w-full p-3 border-2 border-gray-300"
                     value="<EMAIL>" readonly>
            </div>

            <div>
              <label class="block text-sm font-bold mb-2">Password:</label>
              <input type="password" id="admin-password-input"
                     class="w-full p-3 border-2 border-gray-300"
                     value="tamilselvanadmin" readonly>
            </div>
          </div>

          <div class="space-y-4">
            <button onclick="performAdminLogin()"
                    class="w-full neo-brutalist bg-green-500 text-white py-3 font-bold hover:bg-green-600">
              🔑 LOGIN AS ADMIN
            </button>

            <div class="flex space-x-2">
              <button onclick="copyAdminCredentials()"
                      class="flex-1 neo-brutalist bg-blue-500 text-white py-2 text-sm font-bold hover:bg-blue-600">
                📋 COPY CREDENTIALS
              </button>
              <button onclick="closeAdminLogin()"
                      class="flex-1 neo-brutalist bg-gray-500 text-white py-2 text-sm font-bold hover:bg-gray-600">
                ❌ CLOSE
              </button>
            </div>
          </div>

          <div class="mt-4 p-3 bg-yellow-100 border-l-4 border-yellow-500 text-sm">
            <strong>💡 Quick Login:</strong><br>
            Select an admin email above, password is auto-filled. Click "LOGIN AS ADMIN".
          </div>
        </div>
      `;

      document.body.appendChild(modal);
    }

    // Perform admin login
    async function performAdminLogin() {
      const emailSelect = document.getElementById('admin-email-select');
      const passwordInput = document.getElementById('admin-password-input');

      const email = emailSelect.value;
      const password = passwordInput.value;

      // Fill the main login form
      document.getElementById('email').value = email;
      document.getElementById('password').value = password;

      // Close admin modal
      closeAdminLogin();

      // Show success message
      showTempMessage('Admin credentials filled! Click "LOGIN TO HUNT" to proceed.', 'green');

      // Highlight the main login button
      const loginButton = document.querySelector('button[type="submit"]');
      if (loginButton) {
        loginButton.classList.add('animate-pulse');
        loginButton.style.backgroundColor = '#10B981'; // green
        setTimeout(() => {
          loginButton.classList.remove('animate-pulse');
          loginButton.style.backgroundColor = ''; // reset
        }, 3000);
      }
    }

    // Copy admin credentials
    function copyAdminCredentials() {
      const emailSelect = document.getElementById('admin-email-select');
      const passwordInput = document.getElementById('admin-password-input');

      const credentials = `Email: ${emailSelect.value}\\nPassword: ${passwordInput.value}`;

      navigator.clipboard.writeText(credentials).then(() => {
        showTempMessage('Credentials copied to clipboard!', 'blue');
      }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = credentials;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showTempMessage('Credentials copied to clipboard!', 'blue');
      });
    }

    // Close admin login modal
    function closeAdminLogin() {
      const modal = document.getElementById('admin-login-modal');
      if (modal) {
        modal.remove();
      }
    }

    // Show temporary message
    function showTempMessage(message, color = 'blue') {
      const tempDiv = document.createElement('div');
      tempDiv.className = `fixed top-4 right-4 z-50 neo-brutalist bg-${color}-500 text-white p-3 text-sm border-4 border-black`;
      tempDiv.textContent = message;

      document.body.appendChild(tempDiv);

      setTimeout(() => {
        if (tempDiv.parentElement) {
          tempDiv.remove();
        }
      }, 3000);
    }

    // Close modal when clicking outside
    document.addEventListener('click', (e) => {
      const modal = document.getElementById('admin-login-modal');
      if (modal && e.target === modal) {
        closeAdminLogin();
      }
    });
  </script>
</body>
</html>
