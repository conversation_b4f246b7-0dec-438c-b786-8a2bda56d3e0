// Authentication System for The Wolf Challenge
import { auth, db, CTF_CONFIG } from './firebase-config.js';
import AuthDiagnostics from './auth-diagnostics.js';
import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  sendEmailVerification
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js';
import { 
  doc, 
  getDoc, 
  setDoc, 
  serverTimestamp 
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AuthManager {
  constructor() {
    this.currentUser = null;
    this.userRole = null;
    this.diagnostics = new AuthDiagnostics();
    this.initializeAuth();
  }

  initializeAuth() {
    console.log('🐺 Initializing authentication system...');

    try {
      // Validate Firebase availability
      if (!auth) {
        throw new Error('Firebase auth not initialized');
      }

      if (typeof onAuthStateChanged === 'undefined') {
        throw new Error('Firebase auth functions not available');
      }

      console.log('🐺 Setting up authentication state listener...');

      // Listen for authentication state changes with error handling
      onAuthStateChanged(auth, async (user) => {
        try {
          console.log('🐺 Auth state changed:', user ? `User: ${user.email}` : 'No user');

          if (user) {
            console.log('🐺 User authenticated, processing login...');
            await this.handleUserLogin(user);
          } else {
            console.log('🐺 No user authenticated, showing login screen...');
            this.handleUserLogout();
          }
        } catch (error) {
          console.error('🐺 Error in auth state change handler:', error);
          this.showLoginError('Authentication error occurred. Please try logging in again.',
                              document.getElementById('login-error'),
                              document.getElementById('login-error-message'));
        }
      }, (error) => {
        console.error('🐺 Auth state listener error:', error);
        this.showLoginError('Authentication service error. Please refresh the page.',
                            document.getElementById('login-error'),
                            document.getElementById('login-error-message'));
      });

      console.log('🐺 Authentication state listener established');

    } catch (error) {
      console.error('🐺 Critical auth initialization error:', error);

      // Show critical error to user
      setTimeout(() => {
        const errorDiv = document.getElementById('login-error');
        const errorMessage = document.getElementById('login-error-message');
        this.showLoginError('Authentication service unavailable. Please refresh the page and try again.',
                            errorDiv, errorMessage);
      }, 1000);
    }

    // Set up login form
    this.setupLoginForm();

    // Set up logout button
    this.setupLogoutButton();

    console.log('🐺 Authentication initialization completed');
  }

  setupLoginForm() {
    console.log('🐺 Setting up login form...');

    const setupForm = () => {
      const loginForm = document.getElementById('login-form');
      const emailInput = document.getElementById('email');
      const passwordInput = document.getElementById('password');

      console.log('🐺 Form elements found:', {
        loginForm: !!loginForm,
        emailInput: !!emailInput,
        passwordInput: !!passwordInput
      });

      if (loginForm && emailInput && passwordInput) {
        console.log('🐺 Attaching login form event listeners...');

        // Form submission handler
        loginForm.addEventListener('submit', async (e) => {
          e.preventDefault();
          console.log('🐺 Form submitted, calling handleLogin...');
          await this.handleLogin();
        });

        // Enter key handlers for inputs
        emailInput.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            passwordInput.focus();
          }
        });

        passwordInput.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            loginForm.dispatchEvent(new Event('submit'));
          }
        });

        // Input validation feedback
        emailInput.addEventListener('blur', () => {
          const email = emailInput.value.trim();
          if (email && (!email.includes('@') || !email.includes('.'))) {
            emailInput.classList.add('border-red-500');
          } else {
            emailInput.classList.remove('border-red-500');
          }
        });

        passwordInput.addEventListener('input', () => {
          const password = passwordInput.value;
          if (password.length > 0 && password.length < 6) {
            passwordInput.classList.add('border-red-500');
          } else {
            passwordInput.classList.remove('border-red-500');
          }
        });

        console.log('🐺 Login form setup completed successfully');
      } else {
        console.warn('🐺 Login form elements not ready, retrying in 100ms...');
        setTimeout(setupForm, 100);
      }
    };

    if (document.readyState === 'loading') {
      console.log('🐺 DOM still loading, waiting for DOMContentLoaded...');
      document.addEventListener('DOMContentLoaded', setupForm);
    } else {
      console.log('🐺 DOM ready, setting up form immediately...');
      setupForm();
    }
  }

  // Demo buttons removed for security - Firebase authentication only

  setupLogoutButton() {
    // Wait for DOM to be ready
    const setupButton = () => {
      const logoutBtn = document.getElementById('logout-btn');
      if (logoutBtn) {
        console.log('🐺 Setting up logout button');
        logoutBtn.addEventListener('click', async () => {
          await this.handleLogout();
        });
      }
    };

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', setupButton);
    } else {
      // Delay setup since logout button is in main app
      setTimeout(setupButton, 100);
    }
  }

  async handleLogin() {
    console.log('🐺 Login attempt initiated');

    // Get form elements with comprehensive validation
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const errorDiv = document.getElementById('login-error');
    const errorMessage = document.getElementById('login-error-message');
    const submitButton = document.querySelector('#login-form button[type="submit"]');

    // Debug: Log element availability
    console.log('🐺 Form elements check:', {
      emailInput: !!emailInput,
      passwordInput: !!passwordInput,
      errorDiv: !!errorDiv,
      errorMessage: !!errorMessage,
      submitButton: !!submitButton
    });

    // Validate DOM elements exist
    if (!emailInput || !passwordInput) {
      console.error('🐺 Critical: Login form elements not found');
      this.showLoginError('Login form not ready. Please refresh the page.', errorDiv, errorMessage);
      return;
    }

    const email = emailInput.value.trim();
    const password = passwordInput.value.trim();

    // Debug: Log input values (without password)
    console.log('🐺 Login inputs:', {
      email: email,
      passwordLength: password.length,
      emailValid: email.includes('@') && email.includes('.')
    });

    // Enhanced validation with specific error messages
    if (!email || !password) {
      this.showLoginError('Please enter both email and password.', errorDiv, errorMessage);
      return;
    }

    if (!email.includes('@') || !email.includes('.')) {
      this.showLoginError('Please enter a valid email address (must contain @ and domain).', errorDiv, errorMessage);
      return;
    }

    if (password.length < 6) {
      this.showLoginError('Password must be at least 6 characters long.', errorDiv, errorMessage);
      return;
    }

    try {
      // Update UI to show loading state
      if (submitButton) {
        submitButton.disabled = true;
        submitButton.textContent = 'AUTHENTICATING...';
        submitButton.classList.add('opacity-75');
      }

      // Hide any previous errors
      if (errorDiv) errorDiv.classList.add('hidden');

      // Debug: Check Firebase availability
      console.log('🐺 Firebase availability check:', {
        auth: !!auth,
        signInFunction: typeof signInWithEmailAndPassword,
        authCurrentUser: auth?.currentUser,
        authConfig: auth?.config
      });

      // SECURITY: Only Firebase authentication allowed
      if (!auth) {
        throw new Error('Firebase authentication service not initialized. Please refresh the page.');
      }

      if (typeof signInWithEmailAndPassword === 'undefined') {
        throw new Error('Firebase authentication functions not available. Please check your connection.');
      }

      // Attempt Firebase authentication
      console.log('🐺 Initiating Firebase authentication for:', email);
      console.log('🐺 Auth domain:', auth.config?.authDomain);

      const userCredential = await signInWithEmailAndPassword(auth, email, password);

      console.log('🐺 Authentication successful!', {
        uid: userCredential.user.uid,
        email: userCredential.user.email,
        emailVerified: userCredential.user.emailVerified,
        creationTime: userCredential.user.metadata.creationTime,
        lastSignInTime: userCredential.user.metadata.lastSignInTime
      });

      // Check email verification status (warning only, not blocking)
      if (!userCredential.user.emailVerified) {
        console.warn('🐺 Email not verified for user:', email);
        // Show verification reminder but allow login
        this.showEmailVerificationReminder(userCredential.user);
      }

      // Clear form for security
      emailInput.value = '';
      passwordInput.value = '';

      console.log('🐺 Login process completed successfully');

    } catch (error) {
      console.error('🐺 Authentication error details:', {
        code: error.code,
        message: error.message,
        stack: error.stack
      });

      // Comprehensive error handling with user-friendly messages
      let message = 'Authentication failed. Please try again.';
      let isRetryable = true;

      switch (error.code) {
        case 'auth/user-not-found':
          // Check if this is an admin email that needs account creation
          if (this.isAuthorizedAdmin(email)) {
            console.log('🐺 Admin email detected, attempting to create account...');
            try {
              await this.createAdminAccount(email, password);
              message = 'Admin account created successfully! Please try logging in again.';
              isRetryable = true;
            } catch (createError) {
              console.error('🐺 Failed to create admin account:', createError);
              message = 'Failed to create admin account. Please contact system administrator.';
              isRetryable = false;
            }
          } else {
            message = 'No account found with this email address. Please verify your email or contact administrator for access.';
            isRetryable = false;
          }
          break;

        case 'auth/wrong-password':
          message = 'Incorrect password. Please check your password and try again.';
          break;

        case 'auth/invalid-email':
          message = 'Invalid email address format. Please enter a valid email.';
          break;

        case 'auth/too-many-requests':
          message = 'Too many failed login attempts. Please wait a few minutes before trying again.';
          isRetryable = false;
          break;

        case 'auth/user-disabled':
          message = 'This account has been disabled. Please contact administrator for assistance.';
          isRetryable = false;
          break;

        case 'auth/network-request-failed':
          message = 'Network connection error. Please check your internet connection and try again.';
          break;

        case 'auth/timeout':
          message = 'Authentication request timed out. Please try again.';
          break;

        case 'auth/invalid-credential':
          message = 'Invalid login credentials. Please check your email and password.';
          break;

        default:
          // Handle generic errors
          if (error.message.includes('network')) {
            message = 'Network error occurred. Please check your connection and try again.';
          } else if (error.message.includes('Firebase')) {
            message = 'Authentication service error. Please try again in a moment.';
          } else {
            message = `Authentication failed: ${error.message}`;
          }
      }

      // Show error with retry guidance
      if (isRetryable) {
        message += ' If the problem persists, please contact support.';
      }

      this.showLoginError(message, errorDiv, errorMessage);

      // Log security event for monitoring
      this.logSecurityEvent({
        type: 'FAILED_LOGIN_ATTEMPT',
        email: email,
        errorCode: error.code,
        errorMessage: error.message,
        timestamp: Date.now(),
        userAgent: navigator.userAgent.substring(0, 200),
        retryable: isRetryable
      });

    } finally {
      // Always restore button state
      if (submitButton) {
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-sign-in-alt mr-2"></i>LOGIN TO HUNT';
        submitButton.classList.remove('opacity-75');
      }

      console.log('🐺 Login attempt completed');
    }
  }

  showLoginError(message, errorDiv, errorMessage) {
    console.warn('🐺 Authentication error:', message);

    // Enhanced error display with troubleshooting tips
    if (errorMessage) {
      errorMessage.innerHTML = `
        <div class="font-bold mb-2">${message}</div>
        <div class="text-sm">
          <strong>Troubleshooting Tips:</strong><br>
          • Ensure you have a stable internet connection<br>
          • Check that your email and password are correct<br>
          • Try refreshing the page if the error persists<br>
          • Contact administrator if you need account access
        </div>
      `;
    }

    if (errorDiv) {
      errorDiv.classList.remove('hidden');

      // Auto-hide error after 10 seconds
      setTimeout(() => {
        if (errorDiv) {
          errorDiv.classList.add('hidden');
        }
      }, 10000);
    }

    // Also show a temporary toast notification
    this.showToastNotification(message, 'error');
  }

  showToastNotification(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 p-4 rounded shadow-lg z-50 max-w-sm ${
      type === 'error' ? 'bg-red-500 text-white' :
      type === 'success' ? 'bg-green-500 text-white' :
      type === 'warning' ? 'bg-yellow-500 text-black' :
      'bg-blue-500 text-white'
    }`;

    toast.innerHTML = `
      <div class="flex items-center space-x-2">
        <span class="text-lg">
          ${type === 'error' ? '❌' :
            type === 'success' ? '✅' :
            type === 'warning' ? '⚠️' : 'ℹ️'}
        </span>
        <span class="font-bold">${message}</span>
      </div>
    `;

    document.body.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (toast.parentElement) {
        toast.parentElement.removeChild(toast);
      }
    }, 5000);
  }

  showEmailVerificationReminder(user) {
    // Show a non-blocking reminder about email verification
    const reminder = document.createElement('div');
    reminder.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-yellow-500 text-white p-4 rounded shadow-lg z-50 max-w-md';
    reminder.innerHTML = `
      <div class="flex items-center space-x-2">
        <i class="fas fa-envelope text-white"></i>
        <span class="font-bold text-white">Email Verification</span>
      </div>
      <p class="text-sm mt-1 text-white">Please verify your email for enhanced security.</p>
      <div class="mt-2 space-x-2">
        <button onclick="this.parentElement.parentElement.remove()"
                class="bg-black text-white px-3 py-1 text-sm font-bold rounded">
          DISMISS
        </button>
        <button onclick="authManager.sendVerificationEmail('${user.email}')"
                class="bg-black text-white px-3 py-1 text-sm font-bold rounded">
          SEND VERIFICATION
        </button>
      </div>
    `;

    document.body.appendChild(reminder);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (reminder.parentElement) {
        reminder.parentElement.removeChild(reminder);
      }
    }, 10000);
  }

  async sendVerificationEmail(email) {
    try {
      const user = auth.currentUser;
      if (user && !user.emailVerified) {
        await sendEmailVerification(user);

        // Show success message
        const success = document.createElement('div');
        success.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded shadow-lg z-50';
        success.innerHTML = `
          <div class="flex items-center space-x-2">
            <i class="fas fa-check"></i>
            <span class="font-bold">Verification Email Sent</span>
          </div>
          <p class="text-sm mt-1">Check your email for verification link.</p>
        `;

        document.body.appendChild(success);

        setTimeout(() => {
          if (success.parentElement) {
            success.parentElement.removeChild(success);
          }
        }, 5000);

        console.log('🐺 Verification email sent to:', email);
      }
    } catch (error) {
      console.error('🐺 Error sending verification email:', error);

      // Show error message
      const errorMsg = document.createElement('div');
      errorMsg.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded shadow-lg z-50';
      errorMsg.innerHTML = `
        <div class="flex items-center space-x-2">
          <i class="fas fa-exclamation-triangle"></i>
          <span class="font-bold">Verification Error</span>
        </div>
        <p class="text-sm mt-1">Unable to send verification email. Please try again later.</p>
      `;

      document.body.appendChild(errorMsg);

      setTimeout(() => {
        if (errorMsg.parentElement) {
          errorMsg.parentElement.removeChild(errorMsg);
        }
      }, 5000);
    }
  }

  updateEmailVerificationStatus(user) {
    // Update email verification status in the user profile
    setTimeout(() => {
      const userEmailElement = document.getElementById('user-email');
      if (userEmailElement) {
        const verificationStatus = user.emailVerified ?
          '<span class="text-green-600 ml-2"><i class="fas fa-check-circle"></i> Verified</span>' :
          '<span class="text-yellow-600 ml-2"><i class="fas fa-exclamation-triangle"></i> Unverified</span>';

        userEmailElement.innerHTML = `${user.email} ${verificationStatus}`;

        // Add verification button if not verified
        if (!user.emailVerified) {
          const verifyButton = document.createElement('button');
          verifyButton.className = 'ml-2 text-xs bg-yellow-500 text-black px-2 py-1 rounded font-bold';
          verifyButton.textContent = 'VERIFY';
          verifyButton.onclick = () => this.sendVerificationEmail(user.email);
          userEmailElement.appendChild(verifyButton);
        }
      }
    }, 500); // Small delay to ensure UI is ready
  }

  logSecurityEvent(event) {
    // Log security events for monitoring
    console.warn('🐺 Security Event:', event);

    // Store in localStorage for admin review
    const securityLog = JSON.parse(localStorage.getItem('ctf_security_log') || '[]');
    securityLog.push(event);

    // Keep only last 100 events
    if (securityLog.length > 100) {
      securityLog.splice(0, securityLog.length - 100);
    }

    localStorage.setItem('ctf_security_log', JSON.stringify(securityLog));
  }

  async handleUserLogin(user) {
    console.log('🐺 Processing user login for:', user.email);

    this.currentUser = user;

    try {
      // Show loading indicator
      this.showProfileLoadingIndicator();

      console.log('🐺 Loading user profile...');

      // Get or create user document with comprehensive error handling
      const userDoc = await this.getOrCreateUserDocument(user);
      this.userRole = userDoc.role;

      // Hide loading indicator
      this.hideProfileLoadingIndicator();

      console.log('🐺 User document loaded:', {
        email: userDoc.email,
        role: userDoc.role,
        score: userDoc.score,
        challengesSolved: userDoc.challengesSolved
      });

      // Update UI components
      console.log('🐺 Updating user interface...');
      this.updateUserInterface(userDoc);

      // Show main application
      console.log('🐺 Showing main application...');
      this.showMainApplication();

      // Update email verification status in UI
      console.log('🐺 Updating email verification status...');
      this.updateEmailVerificationStatus(user);

      // Log successful login
      this.logSecurityEvent({
        type: 'SUCCESSFUL_LOGIN',
        email: user.email,
        role: this.userRole,
        timestamp: Date.now(),
        emailVerified: user.emailVerified
      });

      console.log('🐺 User login completed successfully:', {
        email: user.email,
        role: this.userRole,
        emailVerified: user.emailVerified
      });

    } catch (error) {
      console.error('🐺 Critical error during user login:', error);

      // Hide loading indicator on error
      this.hideProfileLoadingIndicator();

      // Enhanced error message based on error type
      let errorMessage = 'Failed to load user profile. Please try logging in again.';

      if (error.message.includes('timeout')) {
        errorMessage = 'Profile loading timed out. Please check your connection and try again.';
      } else if (error.message.includes('unavailable')) {
        errorMessage = 'Service temporarily unavailable. Please try again in a moment.';
      } else if (error.message.includes('permission')) {
        errorMessage = 'Access denied. Please contact administrator if this persists.';
      }

      // Show error to user
      this.showLoginError(
        errorMessage,
        document.getElementById('login-error'),
        document.getElementById('login-error-message')
      );

      // Log the error
      this.logSecurityEvent({
        type: 'LOGIN_PROCESSING_ERROR',
        email: user.email,
        error: error.message,
        timestamp: Date.now()
      });

      // Sign out and return to login
      await this.handleLogout();
    }
  }

  async getOrCreateUserDocument(user) {
    console.log('🐺 Loading user profile for:', user.email);

    try {
      // SECURITY: Only Firebase database operations allowed
      if (!db || typeof doc === 'undefined') {
        console.error('🐺 Database service unavailable');
        return this.createFallbackUserProfile(user);
      }

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile loading timeout')), 10000);
      });

      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);

      // Race between database operation and timeout
      const userSnap = await Promise.race([
        getDoc(userRef),
        timeoutPromise
      ]);

      if (userSnap.exists()) {
        console.log('🐺 Existing user profile loaded');

        // Update last login (non-blocking)
        this.updateLastLogin(userRef).catch(error => {
          console.warn('🐺 Failed to update last login:', error.message);
        });

        const userData = userSnap.data();

        // Validate user data integrity
        const validatedData = this.validateUserData(userData, user);

        console.log('🐺 User profile validated:', user.email);
        return validatedData;

      } else {
        console.log('🐺 Creating new user profile...');
        return await this.createNewUserProfile(user);
      }

    } catch (error) {
      console.error('🐺 Profile loading error:', error.message);

      // Provide fallback profile to prevent login failure
      if (error.message.includes('timeout') ||
          error.message.includes('unavailable') ||
          error.message.includes('permission-denied')) {

        console.log('🐺 Using fallback profile due to:', error.message);
        return this.createFallbackUserProfile(user);
      }

      throw new Error('Unable to load user profile. Please try again.');
    }
  }

  // Create fallback profile when database is unavailable
  createFallbackUserProfile(user) {
    console.log('🐺 Creating fallback profile for:', user.email);

    const fallbackProfile = {
      email: user.email,
      role: this.determineUserRole(user.email),
      score: 0,
      challengesSolved: 0,
      progress: {
        beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
        intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
        advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
      },
      solvedChallenges: [],
      accountStatus: 'active',
      isFallback: true, // Mark as fallback profile
      createdAt: new Date(),
      lastLogin: new Date()
    };

    // Cache fallback profile locally
    localStorage.setItem(`ctf_fallback_profile_${user.uid}`, JSON.stringify(fallbackProfile));

    return fallbackProfile;
  }

  // Create new user profile with better error handling
  async createNewUserProfile(user) {
    try {
      const userData = {
        email: user.email,
        role: this.determineUserRole(user.email),
        createdAt: serverTimestamp(),
        lastLogin: serverTimestamp(),
        score: 0,
        challengesSolved: 0,
        progress: {
          beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
          intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
          advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
        },
        solvedChallenges: [],
        securityEvents: [],
        accountStatus: 'active'
      };

      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
      await setDoc(userRef, userData);

      console.log('🐺 New user profile created:', user.email);

      // Log account creation
      this.logSecurityEvent({
        type: 'ACCOUNT_CREATED',
        email: user.email,
        role: userData.role,
        timestamp: Date.now()
      });

      return userData;

    } catch (error) {
      console.error('🐺 Failed to create user profile:', error);
      // Return fallback profile if creation fails
      return this.createFallbackUserProfile(user);
    }
  }

  // Validate and fix user data integrity
  validateUserData(userData, user) {
    const validated = { ...userData };

    // Ensure required fields exist
    if (!validated.email) validated.email = user.email;
    if (!validated.role) validated.role = this.determineUserRole(user.email);
    if (typeof validated.score !== 'number') validated.score = 0;
    if (typeof validated.challengesSolved !== 'number') validated.challengesSolved = 0;
    if (!validated.solvedChallenges) validated.solvedChallenges = [];
    if (!validated.accountStatus) validated.accountStatus = 'active';

    // Ensure progress object exists
    if (!validated.progress) {
      validated.progress = {
        beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
        intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
        advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
      };
    }

    return validated;
  }

  // Non-blocking last login update
  async updateLastLogin(userRef) {
    try {
      await setDoc(userRef, {
        lastLogin: serverTimestamp()
      }, { merge: true });
    } catch (error) {
      console.warn('🐺 Last login update failed:', error.message);
    }
  }

  // Show profile loading indicator
  showProfileLoadingIndicator() {
    // Remove any existing loading indicator
    this.hideProfileLoadingIndicator();

    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'profile-loading-indicator';
    loadingDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loadingDiv.innerHTML = `
      <div class="neo-brutalist bg-white p-6 text-center max-w-sm mx-4">
        <div class="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <h3 class="text-lg font-bold mb-2">Loading Profile...</h3>
        <p class="text-sm text-gray-600">Please wait while we load your account</p>
        <div class="mt-4 text-xs text-gray-500">
          <div id="loading-status">Connecting to database...</div>
        </div>
      </div>
    `;

    document.body.appendChild(loadingDiv);

    // Update loading status periodically
    let statusIndex = 0;
    const statusMessages = [
      'Connecting to database...',
      'Loading user profile...',
      'Validating account data...',
      'Setting up interface...'
    ];

    this.loadingStatusInterval = setInterval(() => {
      const statusElement = document.getElementById('loading-status');
      if (statusElement && statusIndex < statusMessages.length - 1) {
        statusIndex++;
        statusElement.textContent = statusMessages[statusIndex];
      }
    }, 2000);
  }

  // Hide profile loading indicator
  hideProfileLoadingIndicator() {
    const loadingDiv = document.getElementById('profile-loading-indicator');
    if (loadingDiv) {
      loadingDiv.remove();
    }

    if (this.loadingStatusInterval) {
      clearInterval(this.loadingStatusInterval);
      this.loadingStatusInterval = null;
    }
  }

  determineUserRole(email) {
    // Secure role assignment based on authorized admin emails
    const authorizedAdmins = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    return authorizedAdmins.includes(email.toLowerCase()) ?
           CTF_CONFIG.USER_ROLES.ADMIN :
           CTF_CONFIG.USER_ROLES.PARTICIPANT;
  }

  // Check if email is authorized admin
  isAuthorizedAdmin(email) {
    const authorizedAdmins = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    return authorizedAdmins.includes(email.toLowerCase());
  }

  // Create admin account
  async createAdminAccount(email, password) {
    try {
      console.log('🐺 Creating admin account for:', email);

      // Import createUserWithEmailAndPassword dynamically
      const { createUserWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js');

      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Create admin document in Firestore
      const adminRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
      await setDoc(adminRef, {
        email: user.email,
        role: 'admin',
        displayName: this.getAdminDisplayName(email),
        score: 0,
        challengesSolved: 0,
        progress: {
          beginner: { solved: 0, total: 10 },
          intermediate: { solved: 0, total: 20 },
          advanced: { solved: 0, total: 40 }
        },
        solvedChallenges: [],
        permissions: [
          'user_management',
          'challenge_management',
          'score_management',
          'system_administration',
          'push_notifications',
          'analytics_access'
        ],
        createdAt: serverTimestamp(),
        lastActivity: serverTimestamp(),
        isActive: true,
        accountStatus: 'active'
      });

      console.log('✅ Admin account created successfully');

      // Sign out after creation so user can log in normally
      await signOut(auth);

    } catch (error) {
      console.error('❌ Failed to create admin account:', error);
      throw error;
    }
  }

  // Get admin display name
  getAdminDisplayName(email) {
    const adminNames = {
      '<EMAIL>': 'Main Administrator',
      '<EMAIL>': 'S. Tamilselvan',
      '<EMAIL>': 'System Administrator',
      '<EMAIL>': 'Tamil Selvan Admin'
    };
    return adminNames[email] || 'Administrator';
  }

  updateUserInterface(userDoc) {
    console.log('🐺 Updating user interface elements...');

    try {
      // Update user info in header
      const userEmail = document.getElementById('user-email');
      const userRole = document.getElementById('user-role');
      const userScore = document.getElementById('user-score');

      console.log('🐺 UI elements found:', {
        userEmail: !!userEmail,
        userRole: !!userRole,
        userScore: !!userScore
      });

      if (userEmail) {
        userEmail.textContent = this.currentUser.email;
        console.log('🐺 Updated user email display');
      }

      if (userRole) {
        userRole.textContent = this.userRole.toUpperCase();
        userRole.className = `px-2 py-1 text-sm font-bold rounded ${
          this.userRole === CTF_CONFIG.USER_ROLES.ADMIN
            ? 'bg-red-500 text-white'
            : 'bg-yellow-400 text-black'
        }`;
        console.log('🐺 Updated user role display:', this.userRole);
      }

      if (userScore && userDoc.score !== undefined) {
        userScore.textContent = userDoc.score;
        console.log('🐺 Updated user score display:', userDoc.score);
      }

      // Show/hide admin tab based on role
      const adminTab = document.getElementById('admin-tab');
      if (adminTab) {
        if (this.userRole === CTF_CONFIG.USER_ROLES.ADMIN) {
          adminTab.classList.remove('hidden');
          console.log('🐺 Admin tab shown');
        } else {
          adminTab.classList.add('hidden');
          console.log('🐺 Admin tab hidden');
        }
      }

      // Refresh admin panel access if admin manager is available
      if (window.adminManager && typeof window.adminManager.refreshAdminAccess === 'function') {
        window.adminManager.refreshAdminAccess();
      }

      // Update user stats if available
      const challengesSolvedElement = document.getElementById('challenges-solved');
      if (challengesSolvedElement && userDoc.challengesSolved !== undefined) {
        challengesSolvedElement.textContent = userDoc.challengesSolved;
        console.log('🐺 Updated challenges solved display:', userDoc.challengesSolved);
      }

      console.log('🐺 User interface update completed');

    } catch (error) {
      console.error('🐺 Error updating user interface:', error);
      // Don't throw - this is not critical for login
    }
  }

  showMainApplication() {
    console.log('🐺 Showing main application');

    // Hide loading and login screens
    const loadingScreen = document.getElementById('loading-screen');
    const loginScreen = document.getElementById('login-screen');
    const mainApp = document.getElementById('main-app');

    if (loadingScreen) loadingScreen.classList.add('hidden');
    if (loginScreen) loginScreen.classList.add('hidden');
    if (mainApp) {
      mainApp.classList.remove('hidden');
      console.log('🐺 Main application is now visible');
    } else {
      console.error('🐺 Main app element not found!');
    }

    // Security: Firebase-only authentication enforced

    // Setup logout button now that main app is visible
    setTimeout(() => {
      this.setupLogoutButton();
    }, 100);
  }

  // Demo mode removed - Firebase authentication only

  async handleLogout() {
    try {
      await signOut(auth);
      console.log('🐺 User logged out');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  handleUserLogout() {
    console.log('🐺 Handling user logout');

    this.currentUser = null;
    this.userRole = null;

    // Hide main application
    const mainApp = document.getElementById('main-app');
    const loadingScreen = document.getElementById('loading-screen');
    const loginScreen = document.getElementById('login-screen');

    if (mainApp) mainApp.classList.add('hidden');
    if (loadingScreen) loadingScreen.classList.add('hidden');
    if (loginScreen) {
      loginScreen.classList.remove('hidden');
      console.log('🐺 Login screen is now visible');
    }

    // Clear form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
      loginForm.reset();
      console.log('🐺 Login form cleared');
    }

    // Hide error messages
    const errorDiv = document.getElementById('login-error');
    if (errorDiv) errorDiv.classList.add('hidden');

    // Re-enable submit button if it was disabled
    const submitButton = document.querySelector('#login-form button[type="submit"]');
    if (submitButton) {
      submitButton.disabled = false;
      submitButton.innerHTML = '<i class="fas fa-sign-in-alt mr-2"></i>LOGIN TO HUNT';
    }
  }

  // Utility methods
  getCurrentUser() {
    return this.currentUser;
  }

  getUserRole() {
    return this.userRole;
  }

  isAdmin() {
    return this.userRole === CTF_CONFIG.USER_ROLES.ADMIN;
  }

  isParticipant() {
    return this.userRole === CTF_CONFIG.USER_ROLES.PARTICIPANT;
  }

  // Diagnostic methods for troubleshooting
  async runDiagnostics() {
    console.log('🐺 Running authentication diagnostics...');
    return await this.diagnostics.generateReport();
  }

  getAuthStatus() {
    return {
      currentUser: this.currentUser ? {
        email: this.currentUser.email,
        uid: this.currentUser.uid,
        emailVerified: this.currentUser.emailVerified
      } : null,
      userRole: this.userRole,
      authInitialized: !!auth,
      dbInitialized: !!db
    };
  }
}

// Initialize authentication manager
console.log('🐺 Creating AuthManager instance...');
const authManager = new AuthManager();

// Admin Panel Debug and Fix Functions
window.debugAdmin = {
  async checkAdminStatus() {
    console.log('🐺 === ADMIN STATUS DEBUG ===');
    const user = authManager.getCurrentUser();
    const role = authManager.getUserRole();

    console.log('Current User:', user?.email);
    console.log('Current Role:', role);
    console.log('Is Admin:', authManager.isAdmin());

    if (user) {
      try {
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        if (userDoc.exists()) {
          console.log('Firestore User Doc:', userDoc.data());
        } else {
          console.log('❌ User document does not exist in Firestore');
        }
      } catch (error) {
        console.error('❌ Error fetching user document:', error);
      }
    }

    const adminTab = document.getElementById('admin-tab');
    const adminSection = document.getElementById('admin-section');
    console.log('Admin Tab Element:', adminTab);
    console.log('Admin Tab Hidden:', adminTab?.classList.contains('hidden'));
    console.log('Admin Section Element:', adminSection);
    console.log('Admin Section Hidden:', adminSection?.classList.contains('hidden'));
  },

  async forceAdminRole() {
    console.log('🐺 === FORCING ADMIN ROLE ===');
    const user = authManager.getCurrentUser();

    if (!user) {
      console.error('❌ No user logged in');
      return;
    }

    const authorizedAdmins = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    if (!authorizedAdmins.includes(user.email.toLowerCase())) {
      console.error('❌ Not authorized admin email:', user.email);
      return;
    }

    try {
      // Update Firestore document
      const userRef = doc(db, 'users', user.uid);
      await setDoc(userRef, {
        email: user.email,
        role: 'admin',
        displayName: 'Tamil Selvan (Admin)',
        score: 0,
        challengesSolved: 0,
        progress: {
          beginner: { solved: 0, total: 10 },
          intermediate: { solved: 0, total: 20 },
          advanced: { solved: 0, total: 40 }
        },
        createdAt: serverTimestamp(),
        lastActivity: serverTimestamp()
      }, { merge: true });

      console.log('✅ Admin role set in Firestore');

      // Update local role
      authManager.userRole = 'admin';

      // Show admin tab
      const adminTab = document.getElementById('admin-tab');
      if (adminTab) {
        adminTab.classList.remove('hidden');
        console.log('✅ Admin tab shown');
      }

      // Update UI
      authManager.updateUserInterface();

      console.log('✅ Admin role forced successfully');
      alert('Admin role activated! Click the ADMIN tab to access the admin panel.');

    } catch (error) {
      console.error('❌ Error setting admin role:', error);
      alert('Error setting admin role: ' + error.message);
    }
  },

  showAdminPanel() {
    console.log('🐺 === SHOWING ADMIN PANEL ===');

    // Show admin tab
    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
      adminTab.classList.remove('hidden');
      adminTab.click(); // Trigger click to show admin section
      console.log('✅ Admin tab shown and clicked');
    }

    // Show admin section directly
    const adminSection = document.getElementById('admin-section');
    if (adminSection) {
      adminSection.classList.remove('hidden');
      console.log('✅ Admin section shown');
    }

    // Hide other sections
    const sections = ['challenges-section', 'leaderboard-section', 'profile-section'];
    sections.forEach(sectionId => {
      const section = document.getElementById(sectionId);
      if (section) {
        section.classList.add('hidden');
      }
    });

    // Load admin manager if available
    if (window.adminManager) {
      window.adminManager.loadOverview();
      console.log('✅ Admin manager loaded');
    }
  }
};

// Make authManager globally available for debugging
window.authManager = authManager;
window.debugAuth = {
  getCurrentUser: () => authManager.getCurrentUser(),
  getUserRole: () => authManager.getUserRole(),
  getAuth: () => auth,
  getDb: () => db,
  getAuthStatus: () => authManager.getAuthStatus(),
  runDiagnostics: () => authManager.runDiagnostics(),
  testLogin: async (email, password) => {
    console.log('🐺 Debug login test:', email);
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      console.log('🐺 Debug login success:', result);
      return result;
    } catch (error) {
      console.error('🐺 Debug login error:', error);
      throw error;
    }
  },
  showLoginForm: () => {
    document.getElementById('loading-screen')?.classList.add('hidden');
    document.getElementById('main-app')?.classList.add('hidden');
    document.getElementById('login-screen')?.classList.remove('hidden');
  },
  showMainApp: () => {
    document.getElementById('loading-screen')?.classList.add('hidden');
    document.getElementById('login-screen')?.classList.add('hidden');
    document.getElementById('main-app')?.classList.remove('hidden');
  }
};

console.log('🐺 AuthManager created and debug tools available');

// Export for use in other modules
export default authManager;

// Initialize loading management
document.addEventListener('DOMContentLoaded', () => {
  console.log('🐺 DOM loaded, initializing authentication...');

  // Mark modules as loaded
  if (window.markModulesLoaded) {
    window.markModulesLoaded();
  }

  // Show loading screen initially
  document.getElementById('loading-screen').classList.remove('hidden');

  // Hide loading screen after a short delay to show branding
  setTimeout(() => {
    if (!authManager.getCurrentUser()) {
      console.log('🐺 Showing login screen');
      document.getElementById('loading-screen').classList.add('hidden');
      document.getElementById('login-screen').classList.remove('hidden');
    }
  }, 2000);
});

// Fallback initialization if DOMContentLoaded already fired
if (document.readyState === 'loading') {
  // DOM is still loading
} else {
  // DOM is already loaded
  console.log('🐺 DOM already loaded, initializing immediately...');

  if (window.markModulesLoaded) {
    window.markModulesLoaded();
  }

  setTimeout(() => {
    if (!authManager.getCurrentUser()) {
      console.log('🐺 Showing login screen (immediate)');
      document.getElementById('loading-screen').classList.add('hidden');
      document.getElementById('login-screen').classList.remove('hidden');
    }
  }, 1000);
}

console.log('🐺 Authentication system initialized');
