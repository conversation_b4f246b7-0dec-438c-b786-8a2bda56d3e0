# 🐺 The Wolf Challenge - Authentication Troubleshooting Guide

## Quick Fixes for Common Login Issues

### 1. **"Firebase authentication service unavailable"**
**Cause:** Firebase SDK not loaded or network connectivity issues
**Solutions:**
- Check your internet connection
- Refresh the page (Ctrl+F5 or Cmd+Shift+R)
- Disable browser extensions temporarily
- Try a different browser
- Clear browser cache and cookies

### 2. **"No account found with this email address"**
**Cause:** Email not registered in the system
**Solutions:**
- Verify you're using the correct email address
- Contact administrator to create your account
- Check for typos in your email

### 3. **"Incorrect password"**
**Cause:** Wrong password entered
**Solutions:**
- Double-check your password (case-sensitive)
- Use password reset if available
- Contact administrator for password reset

### 4. **"Too many failed attempts"**
**Cause:** Firebase rate limiting after multiple failed logins
**Solutions:**
- Wait 15-30 minutes before trying again
- Clear browser data and try again
- Use a different browser or incognito mode

### 5. **"Network error"**
**Cause:** Connectivity or firewall issues
**Solutions:**
- Check internet connection
- Disable VPN temporarily
- Try different network (mobile hotspot)
- Check firewall settings

## Advanced Troubleshooting

### Browser Console Debugging
1. Open browser developer tools (F12)
2. Go to Console tab
3. Type: `debugAuth.runDiagnostics()`
4. Review the diagnostic report

### Manual Authentication Test
1. Open browser console
2. Type: `debugAuth.testLogin('<EMAIL>', 'your-password')`
3. Check the response for detailed error information

### Check Authentication Status
1. Open browser console
2. Type: `debugAuth.getAuthStatus()`
3. Verify all services are initialized

## System Requirements

### Supported Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Required Browser Features
- JavaScript enabled
- Cookies enabled
- Local Storage enabled
- Third-party cookies allowed for Firebase

### Network Requirements
- Stable internet connection
- Access to Firebase domains:
  - `*.firebaseapp.com`
  - `*.googleapis.com`
  - `*.gstatic.com`

## Security Settings

### Browser Privacy Settings
- Allow cookies for this site
- Enable JavaScript
- Allow local storage
- Disable strict tracking protection for this site

### Firewall/Antivirus
- Whitelist Firebase domains
- Allow HTTPS connections
- Disable web protection temporarily for testing

## Developer Debug Tools

### Available Debug Commands (Browser Console)
```javascript
// Check current authentication status
debugAuth.getAuthStatus()

// Run comprehensive diagnostics
debugAuth.runDiagnostics()

// Test login with credentials
debugAuth.testLogin('<EMAIL>', 'password')

// Show login form manually
debugAuth.showLoginForm()

// Show main app manually
debugAuth.showMainApp()

// Get current user info
debugAuth.getCurrentUser()

// Get user role
debugAuth.getUserRole()
```

### Firebase Service Status
```javascript
// Check Firebase Auth
debugAuth.getAuth()

// Check Firestore Database
debugAuth.getDb()
```

## Common Error Codes

| Error Code | Meaning | Solution |
|------------|---------|----------|
| `auth/user-not-found` | Email not registered | Contact admin for account |
| `auth/wrong-password` | Incorrect password | Check password or reset |
| `auth/invalid-email` | Malformed email | Check email format |
| `auth/user-disabled` | Account disabled | Contact administrator |
| `auth/too-many-requests` | Rate limited | Wait and try again |
| `auth/network-request-failed` | Network issue | Check connection |

## Contact Information

If you continue experiencing issues:

1. **Run diagnostics first:** `debugAuth.runDiagnostics()`
2. **Copy the diagnostic report**
3. **Contact administrator with:**
   - Your email address
   - Error message
   - Diagnostic report
   - Browser and OS information

## Emergency Access

If all else fails:
1. Try incognito/private browsing mode
2. Use a different device/browser
3. Contact administrator for manual account verification

---

**Developer:** S.Tamilselvan  
**Platform:** The Wolf Challenge CTF  
**Last Updated:** 2024
