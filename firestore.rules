rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Enhanced helper functions for authentication and authorization
    function isAuth() { return request.auth != null; }
    function isAdmin() {
      return isAuth() &&
      exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    function isOwner(uid) { return isAuth() && request.auth.uid == uid; }
    function isResourceOwner() { return isAuth() && resource.data.userId == request.auth.uid; }
    function isAdminOrOwner(uid) { return isAdmin() || isOwner(uid); }

    // Enable full admin access for specific admin email
    function isAuthorizedAdmin() {
      return isAuth() && request.auth.token.email == '<EMAIL>';
    }

    // Users collection - comprehensive access control with enhanced admin permissions
    match /users/{userId} {
      allow read: if isAuth();
      allow create: if isAuth();
      allow update: if isAdminOrOwner(userId) || isAuthorizedAdmin() || isAdmin();
      allow delete: if isAdmin() || isAuthorizedAdmin();
      // Special rule for score updates by admin
      allow write: if isAuthorizedAdmin() && request.auth.token.email == '<EMAIL>';
    }

    // Challenges collection - read for all authenticated, full control for admins
    match /challenges/{challengeId} {
      allow read: if isAuth();
      allow create, update, delete: if isAdmin() || isAuthorizedAdmin();
    }

    // Submissions collection - flexible access for users and admins
    match /submissions/{submissionId} {
      allow read: if isAuth();
      allow create: if isAuth();
      allow update: if isResourceOwner() || isAdmin() || isAuthorizedAdmin();
      allow delete: if isAdmin() || isAuthorizedAdmin();
    }

    // Score events - admin-only for score management
    match /score_events/{eventId} {
      allow read, write: if isAdmin() || isAuthorizedAdmin();
    }

    // Leaderboard - read for all, write for admins
    match /leaderboard/{document} {
      allow read: if isAuth();
      allow write: if isAdmin() || isAuthorizedAdmin();
    }

    // System statistics - admin-only for analytics
    match /system_stats/{document} {
      allow read, write: if isAdmin() || isAuthorizedAdmin();
    }

    // Admin logs - admin-only for audit trails
    match /admin_logs/{logId} {
      allow read, write: if isAdmin() || isAuthorizedAdmin();
    }

    // Notifications - users read their own, admins manage all
    match /notifications/{notificationId} {
      allow read: if isAuth() && (isResourceOwner() || isAdmin() || isAuthorizedAdmin());
      allow create, update, delete: if isAdmin() || isAuthorizedAdmin();
    }

    // Push subscriptions - users manage their own, admins access all
    match /push_subscriptions/{subscriptionId} {
      allow read: if isAuth() && (isResourceOwner() || isAdmin() || isAuthorizedAdmin());
      allow create: if isAuth();
      allow update, delete: if isAuth() && (isResourceOwner() || isAdmin() || isAuthorizedAdmin());
    }

    // Competition settings - admin-only configuration
    match /settings/{settingId} {
      allow read: if isAuth();
      allow write: if isAdmin() || isAuthorizedAdmin();
    }

    // Backup collections - admin-only for data management
    match /backups/{backupId} {
      allow read, write: if isAdmin() || isAuthorizedAdmin();
    }

    // Reports - admin-only for analytics and reporting
    match /reports/{reportId} {
      allow read, write: if isAdmin() || isAuthorizedAdmin();
    }

    // Admin fallback - ensures all admin functions work for any collection
    match /{document=**} {
      allow read, write: if isAdmin() || isAuthorizedAdmin();
    }
  }
}
