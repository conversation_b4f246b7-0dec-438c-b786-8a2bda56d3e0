// Dashboard Management System for The Wolf Challenge
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import { 
  doc, 
  getDoc, 
  collection, 
  getDocs, 
  query, 
  orderBy, 
  limit 
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class DashboardManager {
  constructor() {
    this.currentTab = 'challenges';
    this.userStats = null;
    this.initializeDashboard();
  }

  initializeDashboard() {
    this.setupTabNavigation();
    this.loadUserStats();
  }

  setupTabNavigation() {
    const tabs = ['challenges', 'leaderboard', 'profile', 'admin'];
    
    tabs.forEach(tabName => {
      const tabButton = document.getElementById(`${tabName}-tab`);
      if (tabButton) {
        tabButton.addEventListener('click', () => this.switchTab(tabName));
      }
    });
  }

  switchTab(tabName) {
    // Update tab buttons
    const tabs = ['challenges', 'leaderboard', 'profile', 'admin'];
    tabs.forEach(tab => {
      const button = document.getElementById(`${tab}-tab`);
      const section = document.getElementById(`${tab}-section`);
      
      if (button && section) {
        if (tab === tabName) {
          // Special styling for admin tab
          if (tab === 'admin') {
            button.className = 'neo-brutalist bg-red-500 text-white px-6 py-3 text-lg font-bold';
          } else {
            button.className = 'neo-brutalist bg-yellow-400 text-black px-6 py-3 text-lg font-bold';
          }
          section.classList.remove('hidden');
        } else {
          // Default inactive styling
          if (tab === 'admin') {
            button.className = 'neo-brutalist bg-gray-300 text-black px-6 py-3 text-lg font-bold';
          } else {
            button.className = 'neo-brutalist bg-gray-300 text-black px-6 py-3 text-lg font-bold';
          }
          section.classList.add('hidden');
        }
      }
    });
    
    this.currentTab = tabName;
    
    // Load content for the selected tab
    switch (tabName) {
      case 'leaderboard':
        this.loadLeaderboard();
        break;
      case 'profile':
        this.loadProfile();
        break;
      case 'admin':
        console.log('🐺 Admin tab clicked, loading admin panel...');
        this.loadAdminPanel();
        break;
    }
  }

  async loadUserStats() {
    const user = authManager.getCurrentUser();
    if (!user) return;

    try {
      // Check if Firebase is available
      if (!db || typeof doc === 'undefined') {
        console.log('🐺 Firebase not available, using demo user stats');
        this.userStats = this.getDemoUserStats(user);
        this.updateDashboardStats();
        return;
      }

      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        this.userStats = userSnap.data();
        this.updateDashboardStats();
      } else {
        // Create demo stats for new user
        this.userStats = this.getDemoUserStats(user);
        this.updateDashboardStats();
      }
    } catch (error) {
      console.error('🐺 Error loading user stats, using demo data:', error);
      this.userStats = this.getDemoUserStats(user);
      this.updateDashboardStats();
    }
  }

  getDemoUserStats(user) {
    const randomScore = Math.floor(Math.random() * 150) + 50; // 50-200 points
    const challengesSolved = Math.floor(randomScore / 10);

    return {
      email: user.email,
      role: user.email.includes('admin') ? 'admin' : 'participant',
      score: randomScore,
      challengesSolved: challengesSolved,
      createdAt: new Date(),
      lastActivity: new Date(),
      progress: {
        beginner: {
          solved: Math.min(10, Math.floor(challengesSolved * 0.4)),
          total: 10
        },
        intermediate: {
          solved: Math.min(20, Math.floor(challengesSolved * 0.4)),
          total: 20
        },
        advanced: {
          solved: Math.min(40, Math.floor(challengesSolved * 0.2)),
          total: 40
        }
      },
      solvedChallenges: []
    };
  }

  updateDashboardStats() {
    if (!this.userStats) return;

    // Update main stats
    document.getElementById('total-score').textContent = this.userStats.score || 0;
    document.getElementById('challenges-solved').textContent = this.userStats.challengesSolved || 0;
    
    // Calculate and update rank
    this.updateUserRank();
  }

  async updateUserRank() {
    try {
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const usersQuery = query(usersRef, orderBy('score', 'desc'));
      const usersSnap = await getDocs(usersQuery);
      
      let rank = 1;
      const currentUserId = authManager.getCurrentUser()?.uid;
      
      usersSnap.docs.forEach((doc, index) => {
        if (doc.id === currentUserId) {
          rank = index + 1;
        }
      });
      
      document.getElementById('current-rank').textContent = `#${rank}`;
    } catch (error) {
      console.error('Error calculating rank:', error);
      document.getElementById('current-rank').textContent = '#-';
    }
  }

  async loadLeaderboard() {
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (!leaderboardContent) return;

    try {
      leaderboardContent.innerHTML = '<div class="text-center py-8">Loading leaderboard...</div>';
      
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const leaderboardQuery = query(usersRef, orderBy('score', 'desc'), limit(50));
      const leaderboardSnap = await getDocs(leaderboardQuery);
      
      const leaderboardData = leaderboardSnap.docs.map((doc, index) => ({
        rank: index + 1,
        ...doc.data(),
        id: doc.id
      }));
      
      this.renderLeaderboard(leaderboardData);
    } catch (error) {
      console.error('Error loading leaderboard:', error);
      leaderboardContent.innerHTML = '<div class="text-center py-8 text-red-500">Error loading leaderboard</div>';
    }
  }

  renderLeaderboard(data) {
    const leaderboardContent = document.getElementById('leaderboard-content');
    if (!leaderboardContent) return;

    if (data.length === 0) {
      leaderboardContent.innerHTML = '<div class="text-center py-8">No participants yet</div>';
      return;
    }

    const currentUserId = authManager.getCurrentUser()?.uid;
    
    leaderboardContent.innerHTML = `
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b-4 border-black">
              <th class="text-left p-3 font-bold">Rank</th>
              <th class="text-left p-3 font-bold">Participant</th>
              <th class="text-center p-3 font-bold">Score</th>
              <th class="text-center p-3 font-bold">Solved</th>
              <th class="text-center p-3 font-bold">Progress</th>
            </tr>
          </thead>
          <tbody>
            ${data.map(user => `
              <tr class="border-b-2 border-gray-300 ${user.id === currentUserId ? 'bg-yellow-100' : ''}">
                <td class="p-3">
                  <div class="flex items-center">
                    ${this.getRankIcon(user.rank)}
                    <span class="ml-2 font-bold">#${user.rank}</span>
                  </div>
                </td>
                <td class="p-3">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                      ${user.email.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div class="font-bold">${user.email.split('@')[0]}</div>
                      <div class="text-sm text-gray-600">${user.role}</div>
                    </div>
                  </div>
                </td>
                <td class="p-3 text-center">
                  <div class="text-2xl font-bold text-green-600">${user.score || 0}</div>
                </td>
                <td class="p-3 text-center">
                  <div class="text-lg font-bold">${user.challengesSolved || 0}</div>
                  <div class="text-sm text-gray-600">/ 70</div>
                </td>
                <td class="p-3 text-center">
                  <div class="space-y-1">
                    ${this.renderProgressBars(user.progress)}
                  </div>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  getRankIcon(rank) {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return '🏅';
    }
  }

  renderProgressBars(progress) {
    if (!progress) return '<div class="text-sm text-gray-400">No progress</div>';
    
    const categories = ['beginner', 'intermediate', 'advanced'];
    const colors = ['green', 'yellow', 'red'];
    
    return categories.map((category, index) => {
      const categoryProgress = progress[category];
      if (!categoryProgress) return '';
      
      const percentage = (categoryProgress.solved / categoryProgress.total) * 100;
      return `
        <div class="flex items-center text-xs">
          <div class="w-12 h-2 bg-gray-200 mr-1">
            <div class="h-full bg-${colors[index]}-500" style="width: ${percentage}%"></div>
          </div>
          <span>${categoryProgress.solved}/${categoryProgress.total}</span>
        </div>
      `;
    }).join('');
  }

  async loadProfile() {
    const profileContent = document.getElementById('profile-content');
    if (!profileContent) return;

    console.log('🐺 Loading profile tab...');

    // Show loading indicator
    profileContent.innerHTML = `
      <div class="text-center py-8">
        <div class="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <div>Loading profile...</div>
      </div>
    `;

    const user = authManager.getCurrentUser();
    if (!user) {
      profileContent.innerHTML = `
        <div class="text-center py-8">
          <div class="text-red-500 mb-4">❌ Not logged in</div>
          <button onclick="window.location.reload()" class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
            REFRESH PAGE
          </button>
        </div>
      `;
      return;
    }

    try {
      // Ensure user stats are loaded
      if (!this.userStats) {
        console.log('🐺 User stats not loaded, loading now...');
        await this.loadUserStats();
      }

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile loading timeout')), 8000);
      });

      await Promise.race([
        this.renderProfile(),
        timeoutPromise
      ]);

    } catch (error) {
      console.error('🐺 Profile loading error:', error);
      this.showProfileError(error.message);
    }
  }

  async renderProfile() {
    const profileContent = document.getElementById('profile-content');
    if (!profileContent) return;

    const user = authManager.getCurrentUser();
    if (!user || !this.userStats) {
      throw new Error('User data not available');
    }

    // Safely format dates
    const formatDate = (timestamp) => {
      try {
        if (!timestamp) return 'Unknown';
        if (timestamp.toDate) return new Date(timestamp.toDate()).toLocaleDateString();
        if (timestamp instanceof Date) return timestamp.toLocaleDateString();
        return new Date(timestamp).toLocaleDateString();
      } catch (error) {
        return 'Unknown';
      }
    };

    profileContent.innerHTML = `
      <div class="space-y-6">
        <!-- User Info -->
        <div class="neo-brutalist bg-gray-50 p-6">
          <h3 class="text-2xl font-bold mb-4">👤 User Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-bold mb-1">Email</label>
              <div class="p-3 bg-white border-2 border-gray-300">${user.email}</div>
            </div>
            <div>
              <label class="block text-sm font-bold mb-1">Role</label>
              <div class="p-3 bg-white border-2 border-gray-300 capitalize">
                ${this.userStats.role || 'participant'}
                ${this.userStats.role === 'admin' ? ' 👑' : ''}
              </div>
            </div>
            <div>
              <label class="block text-sm font-bold mb-1">Member Since</label>
              <div class="p-3 bg-white border-2 border-gray-300">
                ${formatDate(this.userStats.createdAt)}
              </div>
            </div>
            <div>
              <label class="block text-sm font-bold mb-1">Last Activity</label>
              <div class="p-3 bg-white border-2 border-gray-300">
                ${formatDate(this.userStats.lastActivity || this.userStats.lastLogin)}
              </div>
            </div>
          </div>
        </div>

        <!-- Statistics -->
        <div class="neo-brutalist bg-gray-50 p-6">
          <h3 class="text-2xl font-bold mb-4">📊 Statistics</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-white border-2 border-gray-300">
              <div class="text-3xl font-bold text-green-600">${this.userStats.score || 0}</div>
              <div class="text-sm">Total Score</div>
            </div>
            <div class="text-center p-4 bg-white border-2 border-gray-300">
              <div class="text-3xl font-bold text-blue-600">${this.userStats.challengesSolved || 0}</div>
              <div class="text-sm">Challenges Solved</div>
            </div>
            <div class="text-center p-4 bg-white border-2 border-gray-300">
              <div class="text-3xl font-bold text-purple-600">${this.calculateCompletionRate()}%</div>
              <div class="text-sm">Completion Rate</div>
            </div>
            <div class="text-center p-4 bg-white border-2 border-gray-300">
              <div class="text-3xl font-bold text-orange-600" id="profile-rank">#-</div>
              <div class="text-sm">Current Rank</div>
            </div>
          </div>
        </div>

        <!-- Progress by Category -->
        <div class="neo-brutalist bg-gray-50 p-6">
          <h3 class="text-2xl font-bold mb-4">🎯 Progress by Category</h3>
          <div class="space-y-4">
            ${this.renderCategoryProgress()}
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="neo-brutalist bg-gray-50 p-6">
          <h3 class="text-2xl font-bold mb-4">🕒 Recent Activity</h3>
          <div id="recent-activity">
            <div class="text-center py-4 text-gray-500">No recent activity</div>
          </div>
        </div>

        <!-- Profile Actions -->
        <div class="neo-brutalist bg-gray-50 p-6">
          <h3 class="text-2xl font-bold mb-4">⚙️ Profile Actions</h3>
          <div class="flex flex-wrap gap-4">
            <button onclick="dashboardManager.refreshProfile()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold hover:bg-blue-600">
              🔄 REFRESH PROFILE
            </button>
            <button onclick="dashboardManager.exportProgress()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold hover:bg-green-600">
              📊 EXPORT PROGRESS
            </button>
            ${this.userStats.isFallback ? `
              <button onclick="profileLoadingFixer.fixProfileLoading()"
                      class="neo-brutalist bg-orange-500 text-white px-4 py-2 font-bold hover:bg-orange-600">
                🔧 FIX PROFILE DATA
              </button>
            ` : ''}
          </div>
          ${this.userStats.isFallback ? `
            <div class="mt-4 p-3 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700">
              <strong>⚠️ Fallback Mode:</strong> Using cached profile data. Some features may be limited.
            </div>
          ` : ''}
        </div>
      </div>
    `;

    console.log('✅ Profile rendered successfully');

    // Load additional data (non-blocking)
    this.updateProfileRank().catch(error => {
      console.warn('🐺 Failed to update profile rank:', error.message);
    });

    this.loadRecentActivity().catch(error => {
      console.warn('🐺 Failed to load recent activity:', error.message);
    });
  }

  // Show profile error with recovery options
  showProfileError(errorMessage) {
    const profileContent = document.getElementById('profile-content');
    if (!profileContent) return;

    profileContent.innerHTML = `
      <div class="text-center py-12">
        <div class="text-4xl mb-4">❌</div>
        <h3 class="text-xl font-bold mb-2 text-red-600">Profile Loading Failed</h3>
        <p class="text-gray-600 mb-6">${errorMessage}</p>

        <div class="space-y-4">
          <div class="space-x-4">
            <button onclick="dashboardManager.loadProfile()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold hover:bg-blue-600">
              🔄 RETRY
            </button>
            <button onclick="profileLoadingFixer.fixProfileLoading()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold hover:bg-green-600">
              🔧 AUTO-FIX
            </button>
          </div>

          <div class="text-xs text-gray-500 max-w-md mx-auto">
            <p><strong>Common solutions:</strong></p>
            <ul class="text-left mt-2 space-y-1">
              <li>• Check your internet connection</li>
              <li>• Refresh the page (Ctrl+R)</li>
              <li>• Clear browser cache</li>
              <li>• Try the auto-fix button above</li>
            </ul>
          </div>
        </div>
      </div>
    `;
  }

  // Refresh profile method
  async refreshProfile() {
    console.log('🐺 Refreshing profile...');

    try {
      // Clear cached data
      this.userStats = null;

      // Reload user stats
      await this.loadUserStats();

      // Reload profile display
      await this.loadProfile();

      console.log('✅ Profile refreshed successfully');

    } catch (error) {
      console.error('❌ Profile refresh failed:', error);
      this.showProfileError('Failed to refresh profile: ' + error.message);
    }
  }

  // Export progress method
  exportProgress() {
    const user = authManager.getCurrentUser();
    if (!user || !this.userStats) {
      alert('No profile data to export');
      return;
    }

    const exportData = {
      email: user.email,
      role: this.userStats.role,
      score: this.userStats.score,
      challengesSolved: this.userStats.challengesSolved,
      progress: this.userStats.progress,
      solvedChallenges: this.userStats.solvedChallenges || [],
      exportedAt: new Date().toISOString()
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `wolf-ctf-progress-${user.email}-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    console.log('📊 Progress exported successfully');
  }

  calculateCompletionRate() {
    if (!this.userStats || !this.userStats.progress) return 0;
    
    const totalChallenges = 70; // 10 + 20 + 40
    const solvedChallenges = this.userStats.challengesSolved || 0;
    
    return Math.round((solvedChallenges / totalChallenges) * 100);
  }

  renderCategoryProgress() {
    if (!this.userStats || !this.userStats.progress) return '<div>No progress data</div>';
    
    const categories = [
      { key: 'beginner', name: 'Beginner', icon: '🟢', color: 'green' },
      { key: 'intermediate', name: 'Intermediate', icon: '🟡', color: 'yellow' },
      { key: 'advanced', name: 'Advanced', icon: '🔴', color: 'red' }
    ];
    
    return categories.map(category => {
      const progress = this.userStats.progress[category.key];
      if (!progress) return '';
      
      const percentage = (progress.solved / progress.total) * 100;
      
      return `
        <div class="flex items-center justify-between p-4 bg-white border-2 border-gray-300">
          <div class="flex items-center">
            <span class="text-2xl mr-3">${category.icon}</span>
            <div>
              <div class="font-bold">${category.name}</div>
              <div class="text-sm text-gray-600">${progress.solved}/${progress.total} challenges</div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="w-32 h-4 bg-gray-200 border-2 border-gray-400">
              <div class="h-full bg-${category.color}-500" style="width: ${percentage}%"></div>
            </div>
            <div class="text-lg font-bold">${Math.round(percentage)}%</div>
          </div>
        </div>
      `;
    }).join('');
  }

  async updateProfileRank() {
    try {
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const usersQuery = query(usersRef, orderBy('score', 'desc'));
      const usersSnap = await getDocs(usersQuery);
      
      let rank = 1;
      const currentUserId = authManager.getCurrentUser()?.uid;
      
      usersSnap.docs.forEach((doc, index) => {
        if (doc.id === currentUserId) {
          rank = index + 1;
        }
      });
      
      const profileRank = document.getElementById('profile-rank');
      if (profileRank) {
        profileRank.textContent = `#${rank}`;
      }
    } catch (error) {
      console.error('Error calculating profile rank:', error);
    }
  }

  async loadRecentActivity() {
    // This would load recent submissions/activities
    const recentActivity = document.getElementById('recent-activity');
    if (recentActivity) {
      recentActivity.innerHTML = '<div class="text-center py-4 text-gray-500">No recent activity</div>';
    }
  }

  async loadAdminPanel() {
    console.log('🐺 Dashboard loading admin panel...');

    // Check if admin content element exists
    const adminContent = document.getElementById('admin-content');
    if (!adminContent) {
      console.error('🐺 Admin content element not found in DOM');
      return;
    }

    // Call the admin manager to load the admin panel
    if (window.adminManager && typeof window.adminManager.loadAdminPanel === 'function') {
      console.log('🐺 Admin manager found, calling loadAdminPanel...');
      await window.adminManager.loadAdminPanel();
    } else {
      console.error('🐺 Admin manager not available');
      console.log('🐺 window.adminManager:', window.adminManager);

      // Show error message in admin content
      adminContent.innerHTML = `
        <div class="text-center py-8">
          <div class="text-red-500 text-xl font-bold mb-4">❌ Admin Manager Not Available</div>
          <p class="text-gray-600 mb-4">The admin manager is not loaded or initialized.</p>
          <button onclick="location.reload()" class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
            🔄 RELOAD PAGE
          </button>
          <div class="mt-4 text-sm text-gray-500">
            Debug: Admin manager = ${window.adminManager ? 'exists' : 'null'}
          </div>
        </div>
      `;
    }
  }

  // Refresh dashboard data
  async refresh() {
    await this.loadUserStats();
    
    if (this.currentTab === 'leaderboard') {
      await this.loadLeaderboard();
    } else if (this.currentTab === 'profile') {
      await this.loadProfile();
    }
  }
}

// Initialize dashboard manager
const dashboardManager = new DashboardManager();

export default dashboardManager;
