// Admin Login Fix for The Wolf Challenge CTF Platform
import { auth, db, CTF_CONFIG } from './firebase-config.js';
import { 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js';
import { 
  doc, 
  setDoc, 
  getDoc,
  serverTimestamp 
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AdminLoginFixer {
  constructor() {
    this.adminCredentials = [
      {
        email: '<EMAIL>',
        password: 'WolfChallenge2024!',
        displayName: 'Main Administrator'
      },
      {
        email: '<EMAIL>',
        password: 'TamilSelvan2024!',
        displayName: 'S. Tamilselvan'
      },
      {
        email: '<EMAIL>',
        password: 'Administrator2024!',
        displayName: 'System Administrator'
      },
      {
        email: '<EMAIL>',
        password: 'tamilselvanadmin',
        displayName: 'Tamil Selvan Admin'
      }
    ];
  }

  // Main fix method for admin login issues
  async fixAdminLogin() {
    try {
      console.log('🐺 Starting admin login fix...');
      
      // Show progress
      this.showFixProgress();
      
      // Step 1: Test current authentication
      await this.testCurrentAuth();
      
      // Step 2: Try to create admin accounts
      await this.createAdminAccounts();
      
      // Step 3: Test admin login
      await this.testAdminLogin();
      
      // Step 4: Show login form with credentials
      this.showAdminLoginForm();
      
      console.log('✅ Admin login fix completed');
      
    } catch (error) {
      console.error('❌ Admin login fix failed:', error);
      this.showError(error.message);
    } finally {
      this.hideFixProgress();
    }
  }

  // Test current authentication
  async testCurrentAuth() {
    console.log('🔍 Testing current authentication...');
    
    const currentUser = auth.currentUser;
    if (currentUser) {
      console.log('ℹ️ User already logged in:', currentUser.email);
      
      // Check if current user is admin
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, currentUser.uid);
      const userDoc = await getDoc(userRef);
      
      if (userDoc.exists() && userDoc.data().role === 'admin') {
        console.log('✅ Current user is already admin');
        return;
      }
    }
  }

  // Create admin accounts
  async createAdminAccounts() {
    console.log('📝 Creating admin accounts...');
    
    for (const admin of this.adminCredentials) {
      try {
        console.log(`Creating admin: ${admin.email}`);
        
        // Try to create the account
        const userCredential = await createUserWithEmailAndPassword(
          auth, 
          admin.email, 
          admin.password
        );

        // Create admin document
        await this.createAdminDocument(userCredential.user, admin);
        
        console.log(`✅ Admin created: ${admin.email}`);
        
        // Sign out after creating
        await signOut(auth);
        
      } catch (error) {
        if (error.code === 'auth/email-already-in-use') {
          console.log(`ℹ️ Admin already exists: ${admin.email}`);
          // Try to update existing user to admin
          await this.updateExistingUserToAdmin(admin);
        } else {
          console.warn(`⚠️ Failed to create ${admin.email}:`, error.message);
        }
      }
    }
  }

  // Create admin document
  async createAdminDocument(user, adminInfo) {
    const adminRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
    
    await setDoc(adminRef, {
      email: user.email,
      role: 'admin',
      displayName: adminInfo.displayName,
      score: 0,
      challengesSolved: 0,
      progress: {
        beginner: { solved: 0, total: 10 },
        intermediate: { solved: 0, total: 20 },
        advanced: { solved: 0, total: 40 }
      },
      solvedChallenges: [],
      permissions: [
        'user_management',
        'challenge_management',
        'score_management',
        'system_administration',
        'push_notifications',
        'analytics_access'
      ],
      createdAt: serverTimestamp(),
      lastActivity: serverTimestamp(),
      isActive: true,
      accountStatus: 'active'
    });
  }

  // Update existing user to admin
  async updateExistingUserToAdmin(adminInfo) {
    try {
      // Sign in to get user ID
      const userCredential = await signInWithEmailAndPassword(
        auth, 
        adminInfo.email, 
        adminInfo.password
      );

      // Update user document to admin
      await this.createAdminDocument(userCredential.user, adminInfo);
      
      console.log(`✅ Updated existing user to admin: ${adminInfo.email}`);
      
      // Sign out
      await signOut(auth);
      
    } catch (error) {
      console.warn(`⚠️ Failed to update ${adminInfo.email}:`, error.message);
    }
  }

  // Test admin login
  async testAdminLogin() {
    console.log('🔍 Testing admin login...');
    
    const testAdmin = this.adminCredentials[0]; // Test with first admin
    
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth, 
        testAdmin.email, 
        testAdmin.password
      );

      console.log('✅ Admin login test successful:', userCredential.user.email);
      
      // Sign out after test
      await signOut(auth);
      
    } catch (error) {
      console.error('❌ Admin login test failed:', error.message);
      throw new Error('Admin login not working: ' + error.message);
    }
  }

  // Show admin login form
  showAdminLoginForm() {
    // Remove any existing login form
    const existingForm = document.getElementById('admin-login-form');
    if (existingForm) {
      existingForm.remove();
    }

    const loginForm = document.createElement('div');
    loginForm.id = 'admin-login-form';
    loginForm.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loginForm.innerHTML = `
      <div class="neo-brutalist bg-white p-8 max-w-md mx-4">
        <h2 class="text-2xl font-bold mb-6 text-center">👑 Admin Login</h2>
        
        <div class="space-y-4 mb-6">
          <div>
            <label class="block text-sm font-bold mb-2">Email:</label>
            <select id="admin-email-select" class="w-full p-3 border-2 border-gray-300">
              ${this.adminCredentials.map(admin => 
                `<option value="${admin.email}">${admin.email}</option>`
              ).join('')}
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-bold mb-2">Password:</label>
            <input type="password" id="admin-password-input" 
                   class="w-full p-3 border-2 border-gray-300" 
                   value="${this.adminCredentials[0].password}">
          </div>
        </div>

        <div class="space-y-4">
          <button onclick="adminLoginFixer.performAdminLogin()" 
                  class="w-full neo-brutalist bg-green-500 text-white py-3 font-bold hover:bg-green-600">
            🔑 LOGIN AS ADMIN
          </button>
          
          <div class="flex space-x-2">
            <button onclick="adminLoginFixer.copyCredentials()" 
                    class="flex-1 neo-brutalist bg-blue-500 text-white py-2 text-sm font-bold hover:bg-blue-600">
              📋 COPY CREDENTIALS
            </button>
            <button onclick="document.getElementById('admin-login-form').remove()" 
                    class="flex-1 neo-brutalist bg-gray-500 text-white py-2 text-sm font-bold hover:bg-gray-600">
              ❌ CLOSE
            </button>
          </div>
        </div>

        <div class="mt-4 p-3 bg-yellow-100 border-l-4 border-yellow-500 text-sm">
          <strong>💡 Quick Login:</strong><br>
          Select an admin email above, password is auto-filled. Click "LOGIN AS ADMIN".
        </div>
      </div>
    `;

    document.body.appendChild(loginForm);

    // Update password when email changes
    const emailSelect = document.getElementById('admin-email-select');
    const passwordInput = document.getElementById('admin-password-input');
    
    emailSelect.addEventListener('change', () => {
      const selectedAdmin = this.adminCredentials.find(admin => admin.email === emailSelect.value);
      if (selectedAdmin) {
        passwordInput.value = selectedAdmin.password;
      }
    });
  }

  // Perform admin login
  async performAdminLogin() {
    const emailSelect = document.getElementById('admin-email-select');
    const passwordInput = document.getElementById('admin-password-input');
    
    const email = emailSelect.value;
    const password = passwordInput.value;

    try {
      console.log('🔑 Attempting admin login...');
      
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      
      console.log('✅ Admin login successful:', userCredential.user.email);
      
      // Close login form
      document.getElementById('admin-login-form').remove();
      
      // Show success message
      this.showSuccess('Admin login successful! Refreshing page...');
      
      // Refresh page to load admin interface
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      
    } catch (error) {
      console.error('❌ Admin login failed:', error);
      this.showError('Login failed: ' + error.message);
    }
  }

  // Copy credentials to clipboard
  copyCredentials() {
    const emailSelect = document.getElementById('admin-email-select');
    const passwordInput = document.getElementById('admin-password-input');
    
    const credentials = `Email: ${emailSelect.value}\nPassword: ${passwordInput.value}`;
    
    navigator.clipboard.writeText(credentials).then(() => {
      this.showSuccess('Credentials copied to clipboard!');
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = credentials;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      this.showSuccess('Credentials copied to clipboard!');
    });
  }

  // Show fix progress
  showFixProgress() {
    const progressDiv = document.createElement('div');
    progressDiv.id = 'admin-fix-progress';
    progressDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    progressDiv.innerHTML = `
      <div class="neo-brutalist bg-white p-6 text-center max-w-sm mx-4">
        <div class="animate-spin w-12 h-12 border-4 border-green-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <h3 class="text-lg font-bold mb-2">Fixing Admin Login...</h3>
        <p class="text-sm text-gray-600">Setting up admin accounts</p>
      </div>
    `;
    
    document.body.appendChild(progressDiv);
  }

  // Hide fix progress
  hideFixProgress() {
    const progressDiv = document.getElementById('admin-fix-progress');
    if (progressDiv) {
      progressDiv.remove();
    }
  }

  // Show success message
  showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-green-500 text-white p-4 max-w-md';
    successDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">✅</span>
        <h3 class="font-bold">Success!</h3>
      </div>
      <p class="text-sm">${message}</p>
    `;
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
      if (successDiv.parentElement) {
        successDiv.remove();
      }
    }, 3000);
  }

  // Show error message
  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-red-500 text-white p-4 max-w-md';
    errorDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">❌</span>
        <h3 class="font-bold">Error</h3>
      </div>
      <p class="text-sm mb-3">${message}</p>
      <button onclick="this.parentElement.remove()" 
              class="bg-white text-red-500 px-3 py-1 text-sm font-bold rounded">
        CLOSE
      </button>
    `;
    
    document.body.appendChild(errorDiv);
  }

  // Quick admin login (for console use)
  async quickAdminLogin() {
    const admin = this.adminCredentials[0]; // Use first admin
    
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth, 
        admin.email, 
        admin.password
      );
      
      console.log('✅ Quick admin login successful:', userCredential.user.email);
      window.location.reload();
      
    } catch (error) {
      console.error('❌ Quick admin login failed:', error);
      // Try to create admin first
      await this.fixAdminLogin();
    }
  }
}

// Initialize admin login fixer
const adminLoginFixer = new AdminLoginFixer();

// Make it globally available
window.adminLoginFixer = adminLoginFixer;

// Add admin login button to page
document.addEventListener('DOMContentLoaded', () => {
  const adminButton = document.createElement('button');
  adminButton.className = 'fixed bottom-4 left-4 z-50 neo-brutalist bg-red-500 text-white px-4 py-2 font-bold hover:bg-red-600';
  adminButton.innerHTML = '👑 ADMIN LOGIN';
  adminButton.onclick = () => {
    adminLoginFixer.fixAdminLogin();
  };
  
  document.body.appendChild(adminButton);
});

export default adminLoginFixer;
