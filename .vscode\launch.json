{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/AppData/Local/Temp/29e23b0e-c930-450d-84cb-878ad7bd4319_format_me.zip.319", "program": "c:/Users/<USER>/AppData/Local/Temp/29e23b0e-c930-450d-84cb-878ad7bd4319_format_me.zip.319/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}