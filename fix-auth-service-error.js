// Authentication Service Error Fix for The Wolf Challenge CTF Platform
import { auth, db, CTF_CONFIG } from './firebase-config.js';
import { 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js';
import { 
  doc, 
  setDoc, 
  serverTimestamp 
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AuthServiceErrorFixer {
  constructor() {
    this.adminCredentials = [
      {
        email: '<EMAIL>',
        password: 'WolfChallenge2024!',
        displayName: 'Main Administrator'
      },
      {
        email: '<EMAIL>',
        password: 'TamilSelvan2024!',
        displayName: 'S. Tamilselvan'
      },
      {
        email: '<EMAIL>',
        password: 'Administrator2024!',
        displayName: 'System Administrator'
      },
      {
        email: '<EMAIL>',
        password: 'tamilselvanadmin',
        displayName: 'Tamil Selvan Admin'
      }
    ];
  }

  // Main fix method for authentication service errors
  async fixAuthServiceError() {
    try {
      console.log('🐺 Fixing authentication service error...');
      
      // Show progress
      this.showFixProgress();
      
      // Step 1: Test Firebase connection
      await this.testFirebaseConnection();
      
      // Step 2: Create admin accounts
      await this.createAllAdminAccounts();
      
      // Step 3: Test admin login
      await this.testAdminLogin();
      
      // Step 4: Show success and login form
      this.showSuccessWithLogin();
      
      console.log('✅ Authentication service error fixed');
      
    } catch (error) {
      console.error('❌ Auth service fix failed:', error);
      this.showError(error.message);
    } finally {
      this.hideFixProgress();
    }
  }

  // Test Firebase connection
  async testFirebaseConnection() {
    console.log('🔍 Testing Firebase connection...');
    
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }
    
    if (!db) {
      throw new Error('Firestore not initialized');
    }
    
    console.log('✅ Firebase connection OK');
  }

  // Create all admin accounts
  async createAllAdminAccounts() {
    console.log('📝 Creating admin accounts...');
    
    for (const admin of this.adminCredentials) {
      try {
        console.log(`Creating admin: ${admin.email}`);
        
        // Try to create the account
        const userCredential = await createUserWithEmailAndPassword(
          auth, 
          admin.email, 
          admin.password
        );

        // Create admin document
        await this.createAdminDocument(userCredential.user, admin);
        
        console.log(`✅ Admin created: ${admin.email}`);
        
        // Sign out after creating
        await signOut(auth);
        
      } catch (error) {
        if (error.code === 'auth/email-already-in-use') {
          console.log(`ℹ️ Admin already exists: ${admin.email}`);
        } else {
          console.warn(`⚠️ Failed to create ${admin.email}:`, error.message);
        }
      }
    }
  }

  // Create admin document
  async createAdminDocument(user, adminInfo) {
    const adminRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
    
    await setDoc(adminRef, {
      email: user.email,
      role: 'admin',
      displayName: adminInfo.displayName,
      score: 0,
      challengesSolved: 0,
      progress: {
        beginner: { solved: 0, total: 10 },
        intermediate: { solved: 0, total: 20 },
        advanced: { solved: 0, total: 40 }
      },
      solvedChallenges: [],
      permissions: [
        'user_management',
        'challenge_management',
        'score_management',
        'system_administration',
        'push_notifications',
        'analytics_access'
      ],
      createdAt: serverTimestamp(),
      lastActivity: serverTimestamp(),
      isActive: true,
      accountStatus: 'active'
    });
  }

  // Test admin login
  async testAdminLogin() {
    console.log('🔍 Testing admin login...');
    
    const testAdmin = this.adminCredentials[0]; // Test with first admin
    
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth, 
        testAdmin.email, 
        testAdmin.password
      );

      console.log('✅ Admin login test successful:', userCredential.user.email);
      
      // Sign out after test
      await signOut(auth);
      
    } catch (error) {
      console.error('❌ Admin login test failed:', error.message);
      // Don't throw error here, just log it
    }
  }

  // Show fix progress
  showFixProgress() {
    const progressDiv = document.createElement('div');
    progressDiv.id = 'auth-fix-progress';
    progressDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    progressDiv.innerHTML = `
      <div class="neo-brutalist bg-white p-6 text-center max-w-sm mx-4">
        <div class="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <h3 class="text-lg font-bold mb-2">Fixing Authentication...</h3>
        <p class="text-sm text-gray-600">Setting up admin accounts</p>
      </div>
    `;
    
    document.body.appendChild(progressDiv);
  }

  // Hide fix progress
  hideFixProgress() {
    const progressDiv = document.getElementById('auth-fix-progress');
    if (progressDiv) {
      progressDiv.remove();
    }
  }

  // Show success with login form
  showSuccessWithLogin() {
    const successDiv = document.createElement('div');
    successDiv.id = 'auth-success-login';
    successDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    successDiv.innerHTML = `
      <div class="neo-brutalist bg-white p-8 max-w-md mx-4">
        <div class="text-center mb-6">
          <div class="text-4xl mb-2">✅</div>
          <h2 class="text-2xl font-bold text-green-600">Authentication Fixed!</h2>
          <p class="text-sm text-gray-600 mt-2">Admin accounts are ready. You can now login.</p>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-bold mb-2">Admin Email:</label>
            <select id="fixed-admin-email" class="w-full p-3 border-2 border-gray-300">
              ${this.adminCredentials.map(admin => 
                `<option value="${admin.email}">${admin.email}</option>`
              ).join('')}
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-bold mb-2">Password:</label>
            <input type="password" id="fixed-admin-password" 
                   class="w-full p-3 border-2 border-gray-300" 
                   value="${this.adminCredentials[0].password}">
          </div>
        </div>

        <div class="mt-6 space-y-3">
          <button onclick="authServiceErrorFixer.performFixedLogin()" 
                  class="w-full neo-brutalist bg-green-500 text-white py-3 font-bold hover:bg-green-600">
            🔑 LOGIN NOW
          </button>
          
          <div class="flex space-x-2">
            <button onclick="authServiceErrorFixer.copyFixedCredentials()" 
                    class="flex-1 neo-brutalist bg-blue-500 text-white py-2 text-sm font-bold hover:bg-blue-600">
              📋 COPY
            </button>
            <button onclick="document.getElementById('auth-success-login').remove()" 
                    class="flex-1 neo-brutalist bg-gray-500 text-white py-2 text-sm font-bold hover:bg-gray-600">
              ❌ CLOSE
            </button>
          </div>
        </div>

        <div class="mt-4 p-3 bg-green-100 border-l-4 border-green-500 text-sm">
          <strong>🎉 Success!</strong> Authentication service is now working. 
          Use the credentials above to login as admin.
        </div>
      </div>
    `;

    document.body.appendChild(successDiv);

    // Update password when email changes
    const emailSelect = document.getElementById('fixed-admin-email');
    const passwordInput = document.getElementById('fixed-admin-password');
    
    emailSelect.addEventListener('change', () => {
      const selectedAdmin = this.adminCredentials.find(admin => admin.email === emailSelect.value);
      if (selectedAdmin) {
        passwordInput.value = selectedAdmin.password;
      }
    });
  }

  // Perform login with fixed credentials
  async performFixedLogin() {
    const emailSelect = document.getElementById('fixed-admin-email');
    const passwordInput = document.getElementById('fixed-admin-password');
    
    const email = emailSelect.value;
    const password = passwordInput.value;

    try {
      console.log('🔑 Attempting fixed admin login...');
      
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      
      console.log('✅ Fixed admin login successful:', userCredential.user.email);
      
      // Close success dialog
      document.getElementById('auth-success-login').remove();
      
      // Show final success message
      this.showFinalSuccess();
      
      // Refresh page to load admin interface
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      
    } catch (error) {
      console.error('❌ Fixed admin login failed:', error);
      this.showError('Login still failed: ' + error.message);
    }
  }

  // Copy fixed credentials
  copyFixedCredentials() {
    const emailSelect = document.getElementById('fixed-admin-email');
    const passwordInput = document.getElementById('fixed-admin-password');
    
    const credentials = `Email: ${emailSelect.value}\nPassword: ${passwordInput.value}`;
    
    navigator.clipboard.writeText(credentials).then(() => {
      this.showTempMessage('Credentials copied to clipboard!', 'green');
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = credentials;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      this.showTempMessage('Credentials copied to clipboard!', 'green');
    });
  }

  // Show final success message
  showFinalSuccess() {
    const finalDiv = document.createElement('div');
    finalDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-green-500 text-white p-4 max-w-md';
    finalDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">🎉</span>
        <h3 class="font-bold">Login Successful!</h3>
      </div>
      <p class="text-sm">Refreshing page to load admin interface...</p>
    `;
    
    document.body.appendChild(finalDiv);
  }

  // Show temporary message
  showTempMessage(message, color = 'blue') {
    const tempDiv = document.createElement('div');
    tempDiv.className = `fixed top-4 left-4 z-50 neo-brutalist bg-${color}-500 text-white p-3 text-sm`;
    tempDiv.textContent = message;
    
    document.body.appendChild(tempDiv);
    
    setTimeout(() => {
      if (tempDiv.parentElement) {
        tempDiv.remove();
      }
    }, 2000);
  }

  // Show error message
  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'fixed top-4 right-4 z-50 neo-brutalist bg-red-500 text-white p-4 max-w-md';
    errorDiv.innerHTML = `
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-2xl">❌</span>
        <h3 class="font-bold">Fix Failed</h3>
      </div>
      <p class="text-sm mb-3">${message}</p>
      <div class="space-x-2">
        <button onclick="authServiceErrorFixer.fixAuthServiceError()" 
                class="bg-white text-red-500 px-3 py-1 text-sm font-bold rounded">
          RETRY
        </button>
        <button onclick="this.parentElement.remove()" 
                class="bg-red-700 text-white px-3 py-1 text-sm font-bold rounded">
          CLOSE
        </button>
      </div>
    `;
    
    document.body.appendChild(errorDiv);
  }

  // Quick fix method for console use
  async quickFix() {
    console.log('🐺 Running quick auth service fix...');
    
    try {
      await this.createAllAdminAccounts();
      console.log('✅ Quick fix completed - admin accounts ready');
      return true;
    } catch (error) {
      console.error('❌ Quick fix failed:', error);
      return false;
    }
  }
}

// Initialize auth service error fixer
const authServiceErrorFixer = new AuthServiceErrorFixer();

// Make it globally available
window.authServiceErrorFixer = authServiceErrorFixer;

// Auto-detect auth service errors and offer fix
document.addEventListener('DOMContentLoaded', () => {
  // Check for auth service error messages
  setTimeout(() => {
    const errorElements = document.querySelectorAll('[class*="error"], [class*="alert"]');
    let hasAuthError = false;
    
    errorElements.forEach(element => {
      if (element.textContent.includes('Authentication service error') ||
          element.textContent.includes('service error') ||
          element.textContent.includes('try again in a moment')) {
        hasAuthError = true;
      }
    });
    
    if (hasAuthError) {
      console.log('🐺 Detected authentication service error, offering fix...');
      
      const fixButton = document.createElement('button');
      fixButton.className = 'fixed bottom-4 right-4 z-50 neo-brutalist bg-red-500 text-white px-4 py-2 font-bold hover:bg-red-600 animate-pulse';
      fixButton.innerHTML = '🔧 FIX AUTH ERROR';
      fixButton.onclick = () => {
        authServiceErrorFixer.fixAuthServiceError();
        fixButton.remove();
      };
      
      document.body.appendChild(fixButton);
    }
  }, 2000);
});

export default authServiceErrorFixer;
