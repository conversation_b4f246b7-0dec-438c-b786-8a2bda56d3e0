// Demo Mode Manager for The Wolf Challenge
// Provides full functionality when Firebase is not available

class DemoModeManager {
  constructor() {
    this.isActive = false;
    this.demoData = {
      users: new Map(),
      challenges: new Map(),
      submissions: new Map()
    };
    this.initializeDemoMode();
  }

  initializeDemoMode() {
    // Check if Firebase is available
    this.isActive = !window.db || typeof window.collection === 'undefined';
    
    if (this.isActive) {
      console.log('🐺 Demo mode activated');
      this.setupDemoData();
      this.showDemoModeNotification();
    }
  }

  setupDemoData() {
    // Initialize demo challenges
    this.initializeDemoChallenges();
    
    // Initialize demo users
    this.initializeDemoUsers();
  }

  initializeDemoChallenges() {
    const challenges = [
      {
        id: 'demo-1',
        title: 'Welcome Hunter',
        description: 'Find the hidden flag in the page source',
        category: 'beginner',
        type: 'HTML/Client-side',
        difficulty: 'Easy',
        points: 10,
        order: 1,
        flag: 'wolf{welcome_to_the_hunt}',
        instructions: 'View the page source to find the hidden flag',
        content: '<!-- wolf{welcome_to_the_hunt} -->',
        hints: ['Try viewing the page source', 'Look for HTML comments']
      },
      {
        id: 'demo-2',
        title: 'Cookie Monster',
        description: 'Manipulate cookies to gain access',
        category: 'beginner',
        type: 'Cookie manipulation',
        difficulty: 'Easy',
        points: 10,
        order: 2,
        flag: 'wolf{cookie_manipulation_101}',
        instructions: 'Change the "role" cookie value to "admin"',
        hints: ['Use browser developer tools', 'Look at the cookies tab']
      },
      {
        id: 'demo-3',
        title: 'SQL Injection Basics',
        description: 'Bypass login using SQL injection',
        category: 'intermediate',
        type: 'Database attacks',
        difficulty: 'Medium',
        points: 10,
        order: 1,
        flag: 'wolf{sql_injection_master}',
        instructions: 'Use SQL injection to bypass the login form',
        hints: ["Try ' OR '1'='1", 'Look for vulnerable parameters']
      },
      {
        id: 'demo-4',
        title: 'Advanced XSS',
        description: 'Execute JavaScript in a filtered environment',
        category: 'advanced',
        type: 'Web exploitation',
        difficulty: 'Hard',
        points: 10,
        order: 1,
        flag: 'wolf{xss_filter_bypass}',
        instructions: 'Bypass the XSS filter and execute JavaScript',
        hints: ['Try different encoding methods', 'Look for filter bypasses']
      }
    ];

    challenges.forEach(challenge => {
      this.demoData.challenges.set(challenge.id, challenge);
    });
  }

  initializeDemoUsers() {
    const users = [
      {
        id: 'demo-user-1',
        email: '<EMAIL>',
        role: 'participant',
        score: 450,
        challengesSolved: 45,
        lastActivity: new Date(Date.now() - 5 * 60 * 1000),
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 15, total: 20 },
          advanced: { solved: 20, total: 40 }
        },
        solvedChallenges: ['demo-1', 'demo-2']
      },
      {
        id: 'demo-user-2',
        email: '<EMAIL>',
        role: 'participant',
        score: 380,
        challengesSolved: 38,
        lastActivity: new Date(Date.now() - 15 * 60 * 1000),
        progress: {
          beginner: { solved: 10, total: 10 },
          intermediate: { solved: 18, total: 20 },
          advanced: { solved: 10, total: 40 }
        },
        solvedChallenges: ['demo-1']
      }
    ];

    users.forEach(user => {
      this.demoData.users.set(user.id, user);
    });
  }

  showDemoModeNotification() {
    // Show a notification that we're in demo mode
    setTimeout(() => {
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 left-4 bg-blue-500 text-white p-4 rounded shadow-lg z-50 max-w-md';
      notification.innerHTML = `
        <div class="flex items-center space-x-2">
          <i class="fas fa-info-circle"></i>
          <span class="font-bold">Demo Mode Active</span>
        </div>
        <p class="text-sm mt-1">Firebase is not available. All features are working with demo data.</p>
        <button onclick="this.parentElement.remove()" 
                class="mt-2 bg-white text-blue-500 px-3 py-1 text-sm font-bold rounded">
          GOT IT
        </button>
      `;
      
      document.body.appendChild(notification);
      
      // Auto-remove after 10 seconds
      setTimeout(() => {
        if (notification.parentElement) {
          notification.parentElement.removeChild(notification);
        }
      }, 10000);
    }, 2000);
  }

  // Mock Firebase functions
  createMockFirestore() {
    return {
      collection: (collectionName) => ({
        doc: (docId) => ({
          get: () => {
            const data = this.demoData[collectionName]?.get(docId);
            return Promise.resolve({
              exists: !!data,
              data: () => data,
              id: docId
            });
          },
          set: (data) => {
            if (!this.demoData[collectionName]) {
              this.demoData[collectionName] = new Map();
            }
            this.demoData[collectionName].set(docId, data);
            return Promise.resolve();
          },
          update: (data) => {
            if (this.demoData[collectionName]?.has(docId)) {
              const existing = this.demoData[collectionName].get(docId);
              this.demoData[collectionName].set(docId, { ...existing, ...data });
            }
            return Promise.resolve();
          }
        }),
        getDocs: () => {
          const docs = Array.from(this.demoData[collectionName]?.values() || []);
          return Promise.resolve({
            docs: docs.map(doc => ({
              id: doc.id,
              data: () => doc
            }))
          });
        }
      })
    };
  }

  // Utility methods
  isActive() {
    return this.isActive;
  }

  getDemoUser(userId) {
    return this.demoData.users.get(userId);
  }

  getDemoChallenge(challengeId) {
    return this.demoData.challenges.get(challengeId);
  }

  getAllDemoUsers() {
    return Array.from(this.demoData.users.values());
  }

  getAllDemoChallenges() {
    return Array.from(this.demoData.challenges.values());
  }

  addDemoUser(user) {
    this.demoData.users.set(user.id, user);
  }

  updateDemoUser(userId, updates) {
    if (this.demoData.users.has(userId)) {
      const existing = this.demoData.users.get(userId);
      this.demoData.users.set(userId, { ...existing, ...updates });
    }
  }

  solveDemoChallenge(userId, challengeId, points) {
    const user = this.demoData.users.get(userId);
    if (user) {
      user.score = (user.score || 0) + points;
      user.challengesSolved = (user.challengesSolved || 0) + 1;
      user.solvedChallenges = user.solvedChallenges || [];
      if (!user.solvedChallenges.includes(challengeId)) {
        user.solvedChallenges.push(challengeId);
      }
      user.lastActivity = new Date();
      this.demoData.users.set(userId, user);
    }
  }
}

// Initialize demo mode manager
const demoModeManager = new DemoModeManager();

// Make it globally available
window.demoModeManager = demoModeManager;

export default demoModeManager;
