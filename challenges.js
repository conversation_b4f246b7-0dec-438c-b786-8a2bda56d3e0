// Challenge Management System for The Wolf Challenge
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import scoreService from './score-service.js';
import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  updateDoc,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class ChallengeManager {
  constructor() {
    this.challenges = [];
    this.userProgress = null;
    this.initializeChallenges();
  }

  async initializeChallenges() {
    try {
      console.log('🐺 Starting challenge initialization...');
      await this.loadChallenges();
      console.log('🐺 Challenges loaded, count:', this.challenges.length);
      await this.loadUserProgress();
      console.log('🐺 User progress loaded');
      this.renderChallenges();
      console.log('🐺 Challenges rendered');
      this.setupChallengeModal();
      console.log('🐺 Challenge modal setup complete');
    } catch (error) {
      console.error('🐺 Error initializing challenges:', error);
      // Show error to user
      this.showInitializationError(error);
    }
  }

  async loadChallenges() {
    try {
      console.log('🐺 Starting to load challenges...');

      // SECURITY: Only Firebase database operations allowed
      if (!db || typeof collection === 'undefined') {
        console.error('🐺 Firebase database not available, using default challenges');
        this.challenges = this.getDefaultChallenges();
        console.log('🐺 Loaded default challenges:', this.challenges.length);
        return;
      }

      const challengesRef = collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES);
      const challengesSnap = await getDocs(query(challengesRef, orderBy('order')));

      this.challenges = challengesSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log('🐺 Loaded challenges from secure database:', this.challenges.length);

      // If no challenges found, initialize with default challenges
      if (this.challenges.length === 0) {
        console.log('🐺 No challenges in database, using default challenges');
        this.challenges = this.getDefaultChallenges();
        console.log('🐺 Default challenges loaded:', this.challenges.length);
      }
    } catch (error) {
      console.error('🐺 Error loading challenges from Firebase:', error);
      console.log('🐺 Falling back to default challenges');
      this.challenges = this.getDefaultChallenges();
      console.log('🐺 Fallback challenges loaded:', this.challenges.length);
    }
  }

  async initializeDefaultChallenges() {
    try {
      console.log('🐺 Initializing default challenges in database...');
      const defaultChallenges = this.getDefaultChallenges();

      for (const challenge of defaultChallenges) {
        const challengeRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES));
        await setDoc(challengeRef, {
          ...challenge,
          createdAt: serverTimestamp(),
          createdBy: 'system'
        });
      }

      console.log('🐺 Default challenges initialized successfully');
      // Reload challenges
      await this.loadChallenges();
    } catch (error) {
      console.error('🐺 Error initializing default challenges:', error);
      throw error;
    }
  }

  getDefaultChallenges() {
    return [
      // BEGINNER CHALLENGES (10 challenges × 10 points = 100 points)
      {
        id: 'beginner-1',
        title: 'Welcome Hunter',
        description: 'Find the hidden flag in the page source',
        category: 'beginner',
        type: 'HTML/Client-side',
        difficulty: 'Easy',
        points: 10,
        order: 1,
        flag: 'wolf{welcome_to_the_hunt}',
        instructions: 'View the page source to find the hidden flag',
        content: '<!-- wolf{welcome_to_the_hunt} -->',
        hints: ['Try viewing the page source', 'Look for HTML comments', 'Right-click and select "View Page Source"']
      },
      {
        id: 'beginner-2',
        title: 'Cookie Monster',
        description: 'Manipulate cookies to gain access',
        category: 'beginner',
        type: 'Cookie manipulation',
        difficulty: 'Easy',
        points: 10,
        order: 2,
        flag: 'wolf{cookie_manipulation_101}',
        instructions: 'Change the "role" cookie value to "admin"',
        content: '<script>document.cookie = "role=user; path=/";</script>',
        hints: ['Use browser developer tools', 'Look at the Application/Storage tab', 'Modify the cookie value']
      },
      {
        id: 'beginner-3',
        title: 'Hidden Elements',
        description: 'Find the flag hidden in CSS',
        category: 'beginner',
        type: 'HTML/Client-side',
        difficulty: 'Easy',
        points: 10,
        order: 3,
        flag: 'wolf{hidden_in_css}',
        instructions: 'Inspect the page elements to find hidden content',
        content: '<div style="display:none;">wolf{hidden_in_css}</div>',
        hints: ['Use browser inspector', 'Look for hidden elements', 'Check CSS display properties']
      },
      {
        id: 'beginner-4',
        title: 'JavaScript Secrets',
        description: 'Find the flag hidden in JavaScript code',
        category: 'beginner',
        type: 'HTML/Client-side',
        difficulty: 'Easy',
        points: 10,
        order: 4,
        flag: 'wolf{javascript_detective}',
        instructions: 'Check the browser console for hidden messages',
        content: '<script>console.log("Secret: wolf{javascript_detective}");</script>',
        hints: ['Open browser console (F12)', 'Look for console.log messages', 'Check the Console tab']
      },
      {
        id: 'beginner-5',
        title: 'Base64 Basics',
        description: 'Decode the Base64 encoded flag',
        category: 'beginner',
        type: 'Cryptography',
        difficulty: 'Easy',
        points: 10,
        order: 5,
        flag: 'wolf{base64_decoded}',
        instructions: 'Decode this Base64 string: d29sZntiYXNlNjRfZGVjb2RlZH0=',
        hints: ['Use online Base64 decoder', 'Try atob() in browser console', 'Base64 uses A-Z, a-z, 0-9, +, /']
      },
      {
        id: 'beginner-6',
        title: 'URL Parameters',
        description: 'Find the flag in URL parameters',
        category: 'beginner',
        type: 'Web basics',
        difficulty: 'Easy',
        points: 10,
        order: 6,
        flag: 'wolf{url_parameter_found}',
        instructions: 'Add ?flag=wolf{url_parameter_found} to the URL',
        hints: ['Check the URL bar', 'Try adding parameters with ?', 'Look for GET parameters']
      },
      {
        id: 'beginner-7',
        title: 'Form Inspection',
        description: 'Find the hidden form field',
        category: 'beginner',
        type: 'HTML/Client-side',
        difficulty: 'Easy',
        points: 10,
        order: 7,
        flag: 'wolf{hidden_form_field}',
        instructions: 'Inspect the form to find the hidden input field',
        content: '<form><input type="hidden" name="secret" value="wolf{hidden_form_field}"></form>',
        hints: ['Inspect the form element', 'Look for hidden input fields', 'Check input type="hidden"']
      },
      {
        id: 'beginner-8',
        title: 'Image Metadata',
        description: 'Extract flag from image metadata',
        category: 'beginner',
        type: 'Steganography',
        difficulty: 'Easy',
        points: 10,
        order: 8,
        flag: 'wolf{image_metadata_flag}',
        instructions: 'The flag is hidden in the image alt text or title',
        content: '<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="wolf{image_metadata_flag}">',
        hints: ['Right-click on images', 'Check image properties', 'Look at alt text and title attributes']
      },
      {
        id: 'beginner-9',
        title: 'Caesar Cipher',
        description: 'Decode the Caesar cipher (ROT13)',
        category: 'beginner',
        type: 'Cryptography',
        difficulty: 'Easy',
        points: 10,
        order: 9,
        flag: 'wolf{caesar_cipher_solved}',
        instructions: 'Decode: jbys{pnrfne_pvcure_fbyirq}',
        hints: ['Try ROT13 decoder', 'Caesar cipher shifts letters', 'Each letter is shifted by 13 positions']
      },
      {
        id: 'beginner-10',
        title: 'Network Inspector',
        description: 'Find the flag in network requests',
        category: 'beginner',
        type: 'Web basics',
        difficulty: 'Easy',
        points: 10,
        order: 10,
        flag: 'wolf{network_request_found}',
        instructions: 'Check the Network tab in developer tools',
        hints: ['Open Network tab in DevTools', 'Look for XHR/Fetch requests', 'Check request/response headers']
      },

      // INTERMEDIATE CHALLENGES (20 challenges × 10 points = 200 points)
      {
        id: 'intermediate-1',
        title: 'SQL Injection Basics',
        description: 'Bypass login using SQL injection',
        category: 'intermediate',
        type: 'Database attacks',
        difficulty: 'Medium',
        points: 10,
        order: 1,
        flag: 'wolf{sql_injection_master}',
        instructions: 'Use SQL injection to bypass the login form',
        content: 'Username: admin\' OR \'1\'=\'1\' -- \nPassword: anything',
        hints: ["Try ' OR '1'='1' --", 'Look for vulnerable parameters', 'Comment out the rest with --']
      },
      {
        id: 'intermediate-2',
        title: 'XSS Challenge',
        description: 'Execute JavaScript in the input field',
        category: 'intermediate',
        type: 'Web exploitation',
        difficulty: 'Medium',
        points: 10,
        order: 2,
        flag: 'wolf{xss_executed}',
        instructions: 'Execute a JavaScript alert to get the flag',
        content: '<script>alert("wolf{xss_executed}")</script>',
        hints: ['Try <script>alert(1)</script>', 'Look for input validation bypasses', 'Check for reflected XSS']
      },
      {
        id: 'intermediate-3',
        title: 'Directory Traversal',
        description: 'Access files outside the web directory',
        category: 'intermediate',
        type: 'Path traversal',
        difficulty: 'Medium',
        points: 10,
        order: 3,
        flag: 'wolf{directory_traversal_success}',
        instructions: 'Use ../ to access files outside the current directory',
        hints: ['Try ../../../etc/passwd', 'Use ../ to go up directories', 'Look for file inclusion vulnerabilities']
      },
      {
        id: 'intermediate-4',
        title: 'Command Injection',
        description: 'Execute system commands through input',
        category: 'intermediate',
        type: 'Command injection',
        difficulty: 'Medium',
        points: 10,
        order: 4,
        flag: 'wolf{command_injection_basic}',
        instructions: 'Execute system commands to find the flag',
        content: 'Input: 127.0.0.1; cat flag.txt',
        hints: ['Try ; ls', 'Use && or || for command chaining', 'Look for command concatenation']
      },
      {
        id: 'intermediate-5',
        title: 'Session Hijacking',
        description: 'Steal and use another user\'s session',
        category: 'intermediate',
        type: 'Session attacks',
        difficulty: 'Medium',
        points: 10,
        order: 5,
        flag: 'wolf{session_hijacked}',
        instructions: 'Modify the session cookie to impersonate another user',
        hints: ['Check session cookies', 'Try changing session ID', 'Look for predictable session tokens']
      },
      {
        id: 'intermediate-6',
        title: 'CSRF Attack',
        description: 'Perform Cross-Site Request Forgery',
        category: 'intermediate',
        type: 'CSRF',
        difficulty: 'Medium',
        points: 10,
        order: 6,
        flag: 'wolf{csrf_attack_successful}',
        instructions: 'Create a malicious form that performs actions on behalf of the user',
        hints: ['Create hidden form', 'Auto-submit with JavaScript', 'Target state-changing operations']
      },
      {
        id: 'intermediate-7',
        title: 'File Upload Bypass',
        description: 'Upload a malicious file by bypassing filters',
        category: 'intermediate',
        type: 'File upload',
        difficulty: 'Medium',
        points: 10,
        order: 7,
        flag: 'wolf{file_upload_bypassed}',
        instructions: 'Upload a PHP file by bypassing the file type restrictions',
        hints: ['Try different file extensions', 'Modify Content-Type header', 'Use double extensions like .php.jpg']
      },
      {
        id: 'intermediate-8',
        title: 'XXE Injection',
        description: 'Exploit XML External Entity vulnerability',
        category: 'intermediate',
        type: 'XML attacks',
        difficulty: 'Medium',
        points: 10,
        order: 8,
        flag: 'wolf{xxe_injection_success}',
        instructions: 'Use XXE to read local files',
        content: '<!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>',
        hints: ['Define external entity', 'Reference with &xxe;', 'Target local files']
      },
      {
        id: 'intermediate-9',
        title: 'LDAP Injection',
        description: 'Bypass LDAP authentication',
        category: 'intermediate',
        type: 'Injection attacks',
        difficulty: 'Medium',
        points: 10,
        order: 9,
        flag: 'wolf{ldap_injection_bypass}',
        instructions: 'Use LDAP injection to bypass authentication',
        hints: ['Try *)(&', 'Use wildcard characters', 'Break LDAP query syntax']
      },
      {
        id: 'intermediate-10',
        title: 'Insecure Deserialization',
        description: 'Exploit object deserialization vulnerability',
        category: 'intermediate',
        type: 'Deserialization',
        difficulty: 'Medium',
        points: 10,
        order: 10,
        flag: 'wolf{deserialization_exploited}',
        instructions: 'Modify serialized object to execute code',
        hints: ['Find serialized data', 'Modify object properties', 'Look for __wakeup or __destruct methods']
      },
      {
        id: 'intermediate-11',
        title: 'JWT Token Manipulation',
        description: 'Manipulate JSON Web Token to gain access',
        category: 'intermediate',
        type: 'Authentication bypass',
        difficulty: 'Medium',
        points: 10,
        order: 11,
        flag: 'wolf{jwt_manipulated}',
        instructions: 'Modify the JWT token to change user role',
        hints: ['Decode JWT token', 'Change payload data', 'Try algorithm confusion attacks']
      },
      {
        id: 'intermediate-12',
        title: 'NoSQL Injection',
        description: 'Exploit NoSQL database injection',
        category: 'intermediate',
        type: 'Database attacks',
        difficulty: 'Medium',
        points: 10,
        order: 12,
        flag: 'wolf{nosql_injection_success}',
        instructions: 'Bypass NoSQL authentication',
        content: '{"username": {"$ne": null}, "password": {"$ne": null}}',
        hints: ['Use $ne operator', 'Try $regex for pattern matching', 'Exploit MongoDB operators']
      },
      {
        id: 'intermediate-13',
        title: 'Server-Side Template Injection',
        description: 'Exploit template engine vulnerability',
        category: 'intermediate',
        type: 'Template injection',
        difficulty: 'Medium',
        points: 10,
        order: 13,
        flag: 'wolf{ssti_exploited}',
        instructions: 'Execute code through template injection',
        hints: ['Try {{7*7}}', 'Test different template engines', 'Look for template syntax']
      },
      {
        id: 'intermediate-14',
        title: 'Race Condition',
        description: 'Exploit timing vulnerabilities',
        category: 'intermediate',
        type: 'Logic flaws',
        difficulty: 'Medium',
        points: 10,
        order: 14,
        flag: 'wolf{race_condition_won}',
        instructions: 'Exploit race condition in concurrent requests',
        hints: ['Send multiple requests simultaneously', 'Look for TOCTOU vulnerabilities', 'Use threading or async requests']
      },
      {
        id: 'intermediate-15',
        title: 'HTTP Parameter Pollution',
        description: 'Exploit parameter parsing differences',
        category: 'intermediate',
        type: 'HTTP attacks',
        difficulty: 'Medium',
        points: 10,
        order: 15,
        flag: 'wolf{hpp_exploited}',
        instructions: 'Use duplicate parameters to bypass filters',
        hints: ['Send same parameter multiple times', 'Different servers handle duplicates differently', 'Try param=safe&param=malicious']
      },
      {
        id: 'intermediate-16',
        title: 'Business Logic Bypass',
        description: 'Exploit flawed business logic',
        category: 'intermediate',
        type: 'Logic flaws',
        difficulty: 'Medium',
        points: 10,
        order: 16,
        flag: 'wolf{business_logic_bypassed}',
        instructions: 'Find and exploit logical flaws in the application',
        hints: ['Look for workflow bypasses', 'Test edge cases', 'Check for missing authorization']
      },
      {
        id: 'intermediate-17',
        title: 'API Security Bypass',
        description: 'Exploit REST API vulnerabilities',
        category: 'intermediate',
        type: 'API attacks',
        difficulty: 'Medium',
        points: 10,
        order: 17,
        flag: 'wolf{api_security_bypassed}',
        instructions: 'Find and exploit API endpoint vulnerabilities',
        hints: ['Test different HTTP methods', 'Look for IDOR vulnerabilities', 'Check API versioning issues']
      },
      {
        id: 'intermediate-18',
        title: 'GraphQL Injection',
        description: 'Exploit GraphQL query vulnerabilities',
        category: 'intermediate',
        type: 'API attacks',
        difficulty: 'Medium',
        points: 10,
        order: 18,
        flag: 'wolf{graphql_injected}',
        instructions: 'Exploit GraphQL introspection and injection',
        hints: ['Use introspection queries', 'Try nested queries', 'Look for query depth limits']
      },
      {
        id: 'intermediate-19',
        title: 'WebSocket Manipulation',
        description: 'Exploit WebSocket communication',
        category: 'intermediate',
        type: 'Protocol attacks',
        difficulty: 'Medium',
        points: 10,
        order: 19,
        flag: 'wolf{websocket_manipulated}',
        instructions: 'Intercept and modify WebSocket messages',
        hints: ['Use WebSocket client tools', 'Intercept with proxy', 'Modify message content']
      },
      {
        id: 'intermediate-20',
        title: 'OAuth Flow Bypass',
        description: 'Exploit OAuth implementation flaws',
        category: 'intermediate',
        type: 'Authentication bypass',
        difficulty: 'Medium',
        points: 10,
        order: 20,
        flag: 'wolf{oauth_flow_bypassed}',
        instructions: 'Find flaws in OAuth implementation',
        hints: ['Check redirect_uri validation', 'Look for state parameter issues', 'Test token leakage']
      },

      // ADVANCED CHALLENGES (40 challenges × 10 points = 400 points)
      {
        id: 'advanced-1',
        title: 'Advanced XSS Filter Bypass',
        description: 'Execute JavaScript in a heavily filtered environment',
        category: 'advanced',
        type: 'Web exploitation',
        difficulty: 'Hard',
        points: 10,
        order: 1,
        flag: 'wolf{advanced_xss_filter_bypass}',
        instructions: 'Bypass multiple XSS filters and execute JavaScript',
        hints: ['Try different encoding methods', 'Use event handlers', 'Look for filter bypasses with unusual vectors']
      },
      {
        id: 'advanced-2',
        title: 'Blind SQL Injection',
        description: 'Extract data using blind SQL injection techniques',
        category: 'advanced',
        type: 'Database attacks',
        difficulty: 'Hard',
        points: 10,
        order: 2,
        flag: 'wolf{blind_sql_injection_master}',
        instructions: 'Extract sensitive data without seeing direct output',
        hints: ['Use time-based techniques', 'Try boolean-based blind injection', 'Use SUBSTRING and ASCII functions']
      },
      {
        id: 'advanced-3',
        title: 'Advanced Command Injection',
        description: 'Execute commands in a restricted environment',
        category: 'advanced',
        type: 'Command injection',
        difficulty: 'Hard',
        points: 10,
        order: 3,
        flag: 'wolf{advanced_command_injection}',
        instructions: 'Bypass command filters and execute system commands',
        hints: ['Use command substitution', 'Try different separators', 'Encode commands in different ways']
      },
      {
        id: 'advanced-4',
        title: 'Binary Exploitation',
        description: 'Exploit buffer overflow vulnerability',
        category: 'advanced',
        type: 'Binary exploitation',
        difficulty: 'Hard',
        points: 10,
        order: 4,
        flag: 'wolf{buffer_overflow_exploited}',
        instructions: 'Exploit buffer overflow to execute shellcode',
        hints: ['Find buffer overflow point', 'Control return address', 'Inject and execute shellcode']
      },
      {
        id: 'advanced-5',
        title: 'Cryptographic Attack',
        description: 'Break weak cryptographic implementation',
        category: 'advanced',
        type: 'Cryptography',
        difficulty: 'Hard',
        points: 10,
        order: 5,
        flag: 'wolf{crypto_attack_successful}',
        instructions: 'Exploit weak random number generation or key reuse',
        hints: ['Look for predictable patterns', 'Check for key reuse', 'Analyze entropy sources']
      },
      {
        id: 'advanced-6',
        title: 'Advanced SSRF',
        description: 'Exploit Server-Side Request Forgery with bypasses',
        category: 'advanced',
        type: 'SSRF',
        difficulty: 'Hard',
        points: 10,
        order: 6,
        flag: 'wolf{advanced_ssrf_exploited}',
        instructions: 'Bypass SSRF filters to access internal services',
        hints: ['Use different URL schemes', 'Try IP encoding variations', 'Bypass blacklist filters']
      },
      {
        id: 'advanced-7',
        title: 'Prototype Pollution',
        description: 'Exploit JavaScript prototype pollution',
        category: 'advanced',
        type: 'Client-side attacks',
        difficulty: 'Hard',
        points: 10,
        order: 7,
        flag: 'wolf{prototype_pollution_exploited}',
        instructions: 'Pollute Object.prototype to achieve code execution',
        hints: ['Target __proto__ property', 'Look for merge functions', 'Exploit constructor.prototype']
      },
      {
        id: 'advanced-8',
        title: 'Advanced XXE',
        description: 'Exploit XXE with out-of-band techniques',
        category: 'advanced',
        type: 'XML attacks',
        difficulty: 'Hard',
        points: 10,
        order: 8,
        flag: 'wolf{advanced_xxe_exploited}',
        instructions: 'Use out-of-band XXE to exfiltrate data',
        hints: ['Use external DTD', 'Set up external server', 'Use parameter entities']
      },
      {
        id: 'advanced-9',
        title: 'Deserialization RCE',
        description: 'Achieve remote code execution via deserialization',
        category: 'advanced',
        type: 'Deserialization',
        difficulty: 'Hard',
        points: 10,
        order: 9,
        flag: 'wolf{deserialization_rce_achieved}',
        instructions: 'Craft malicious serialized object for code execution',
        hints: ['Use gadget chains', 'Target magic methods', 'Craft payload for specific library']
      },
      {
        id: 'advanced-10',
        title: 'Advanced SSTI',
        description: 'Achieve RCE through Server-Side Template Injection',
        category: 'advanced',
        type: 'Template injection',
        difficulty: 'Hard',
        points: 10,
        order: 10,
        flag: 'wolf{advanced_ssti_rce}',
        instructions: 'Execute system commands through template injection',
        hints: ['Identify template engine', 'Use built-in functions', 'Access global objects']
      },
      {
        id: 'advanced-11',
        title: 'Memory Corruption',
        description: 'Exploit heap-based buffer overflow',
        category: 'advanced',
        type: 'Binary exploitation',
        difficulty: 'Hard',
        points: 10,
        order: 11,
        flag: 'wolf{heap_overflow_exploited}',
        instructions: 'Exploit heap corruption to gain control',
        hints: ['Understand heap layout', 'Corrupt heap metadata', 'Control allocation patterns']
      },
      {
        id: 'advanced-12',
        title: 'Advanced Race Condition',
        description: 'Exploit complex timing vulnerabilities',
        category: 'advanced',
        type: 'Logic flaws',
        difficulty: 'Hard',
        points: 10,
        order: 12,
        flag: 'wolf{advanced_race_condition}',
        instructions: 'Exploit race condition in multi-threaded environment',
        hints: ['Use precise timing', 'Exploit TOCTOU windows', 'Coordinate multiple threads']
      },
      {
        id: 'advanced-13',
        title: 'Kernel Exploitation',
        description: 'Exploit kernel-level vulnerability',
        category: 'advanced',
        type: 'System exploitation',
        difficulty: 'Hard',
        points: 10,
        order: 13,
        flag: 'wolf{kernel_exploited}',
        instructions: 'Escalate privileges through kernel vulnerability',
        hints: ['Find kernel bug', 'Bypass SMEP/SMAP', 'Return to userland safely']
      },
      {
        id: 'advanced-14',
        title: 'Advanced Crypto Breaking',
        description: 'Break custom cryptographic algorithm',
        category: 'advanced',
        type: 'Cryptography',
        difficulty: 'Hard',
        points: 10,
        order: 14,
        flag: 'wolf{custom_crypto_broken}',
        instructions: 'Analyze and break the custom encryption',
        hints: ['Look for mathematical weaknesses', 'Analyze key generation', 'Find patterns in ciphertext']
      },
      {
        id: 'advanced-15',
        title: 'Side-Channel Attack',
        description: 'Exploit timing side-channel vulnerability',
        category: 'advanced',
        type: 'Cryptography',
        difficulty: 'Hard',
        points: 10,
        order: 15,
        flag: 'wolf{side_channel_exploited}',
        instructions: 'Extract secrets using timing analysis',
        hints: ['Measure response times', 'Statistical analysis', 'Look for timing differences']
      },
      {
        id: 'advanced-16',
        title: 'Advanced Web Cache Poisoning',
        description: 'Exploit cache poisoning with complex headers',
        category: 'advanced',
        type: 'Web exploitation',
        difficulty: 'Hard',
        points: 10,
        order: 16,
        flag: 'wolf{cache_poisoning_advanced}',
        instructions: 'Poison web cache to serve malicious content',
        hints: ['Find cache keys', 'Use unkeyed headers', 'Exploit cache behavior']
      },
      {
        id: 'advanced-17',
        title: 'HTTP Request Smuggling',
        description: 'Exploit HTTP request smuggling vulnerability',
        category: 'advanced',
        type: 'Protocol attacks',
        difficulty: 'Hard',
        points: 10,
        order: 17,
        flag: 'wolf{request_smuggling_exploited}',
        instructions: 'Smuggle requests to bypass security controls',
        hints: ['Use CL.TE or TE.CL techniques', 'Exploit parser differences', 'Chain with other vulnerabilities']
      },
      {
        id: 'advanced-18',
        title: 'Advanced DOM Clobbering',
        description: 'Exploit DOM clobbering for XSS',
        category: 'advanced',
        type: 'Client-side attacks',
        difficulty: 'Hard',
        points: 10,
        order: 18,
        flag: 'wolf{dom_clobbering_exploited}',
        instructions: 'Use DOM clobbering to achieve XSS',
        hints: ['Clobber global variables', 'Use form elements', 'Target specific properties']
      },
      {
        id: 'advanced-19',
        title: 'Advanced LDAP Injection',
        description: 'Exploit blind LDAP injection',
        category: 'advanced',
        type: 'Injection attacks',
        difficulty: 'Hard',
        points: 10,
        order: 19,
        flag: 'wolf{advanced_ldap_injection}',
        instructions: 'Extract data using blind LDAP injection',
        hints: ['Use boolean-based techniques', 'Exploit LDAP functions', 'Time-based extraction']
      },
      {
        id: 'advanced-20',
        title: 'Advanced NoSQL Injection',
        description: 'Exploit NoSQL injection for data extraction',
        category: 'advanced',
        type: 'Database attacks',
        difficulty: 'Hard',
        points: 10,
        order: 20,
        flag: 'wolf{advanced_nosql_injection}',
        instructions: 'Extract sensitive data from NoSQL database',
        hints: ['Use $where operator', 'JavaScript injection in MongoDB', 'Exploit aggregation pipeline']
      },
      {
        id: 'advanced-21',
        title: 'Advanced JWT Attacks',
        description: 'Exploit JWT implementation vulnerabilities',
        category: 'advanced',
        type: 'Authentication bypass',
        difficulty: 'Hard',
        points: 10,
        order: 21,
        flag: 'wolf{advanced_jwt_exploited}',
        instructions: 'Exploit JWT vulnerabilities for privilege escalation',
        hints: ['Algorithm confusion attack', 'Key confusion', 'JWT header injection']
      },
      {
        id: 'advanced-22',
        title: 'Advanced File Upload',
        description: 'Achieve RCE through file upload',
        category: 'advanced',
        type: 'File upload',
        difficulty: 'Hard',
        points: 10,
        order: 22,
        flag: 'wolf{file_upload_rce_achieved}',
        instructions: 'Upload malicious file to achieve code execution',
        hints: ['Bypass multiple filters', 'Use polyglot files', 'Chain with path traversal']
      },
      {
        id: 'advanced-23',
        title: 'Advanced CSRF',
        description: 'Exploit CSRF with SameSite bypass',
        category: 'advanced',
        type: 'CSRF',
        difficulty: 'Hard',
        points: 10,
        order: 23,
        flag: 'wolf{advanced_csrf_exploited}',
        instructions: 'Bypass SameSite cookie protection',
        hints: ['Use top-level navigation', 'Exploit subdomain', 'Use popup windows']
      },
      {
        id: 'advanced-24',
        title: 'Advanced SSRF Chain',
        description: 'Chain SSRF with other vulnerabilities',
        category: 'advanced',
        type: 'SSRF',
        difficulty: 'Hard',
        points: 10,
        order: 24,
        flag: 'wolf{ssrf_chain_exploited}',
        instructions: 'Chain SSRF with local file inclusion',
        hints: ['Target internal services', 'Use file:// protocol', 'Chain with XXE or deserialization']
      },
      {
        id: 'advanced-25',
        title: 'Advanced Business Logic',
        description: 'Exploit complex business logic flaws',
        category: 'advanced',
        type: 'Logic flaws',
        difficulty: 'Hard',
        points: 10,
        order: 25,
        flag: 'wolf{complex_logic_bypassed}',
        instructions: 'Find and exploit multi-step logic vulnerabilities',
        hints: ['Analyze complete workflow', 'Test state transitions', 'Look for missing validations']
      },
      {
        id: 'advanced-26',
        title: 'Advanced API Exploitation',
        description: 'Exploit GraphQL and REST API vulnerabilities',
        category: 'advanced',
        type: 'API attacks',
        difficulty: 'Hard',
        points: 10,
        order: 26,
        flag: 'wolf{advanced_api_exploited}',
        instructions: 'Chain multiple API vulnerabilities',
        hints: ['Combine IDOR with privilege escalation', 'Exploit rate limiting bypasses', 'Use API versioning issues']
      },
      {
        id: 'advanced-27',
        title: 'Advanced WebSocket Exploitation',
        description: 'Exploit WebSocket protocol vulnerabilities',
        category: 'advanced',
        type: 'Protocol attacks',
        difficulty: 'Hard',
        points: 10,
        order: 27,
        flag: 'wolf{websocket_advanced_exploited}',
        instructions: 'Exploit WebSocket for cross-origin attacks',
        hints: ['Bypass origin validation', 'Exploit message handling', 'Use WebSocket smuggling']
      },
      {
        id: 'advanced-28',
        title: 'Advanced OAuth Exploitation',
        description: 'Exploit OAuth 2.0 implementation flaws',
        category: 'advanced',
        type: 'Authentication bypass',
        difficulty: 'Hard',
        points: 10,
        order: 28,
        flag: 'wolf{oauth_advanced_exploited}',
        instructions: 'Chain OAuth vulnerabilities for account takeover',
        hints: ['Exploit PKCE bypass', 'Use authorization code interception', 'Target token endpoint']
      },
      {
        id: 'advanced-29',
        title: 'Advanced Deserialization Chain',
        description: 'Build complex deserialization exploit chain',
        category: 'advanced',
        type: 'Deserialization',
        difficulty: 'Hard',
        points: 10,
        order: 29,
        flag: 'wolf{deserialization_chain_exploited}',
        instructions: 'Build gadget chain for remote code execution',
        hints: ['Analyze available classes', 'Chain multiple gadgets', 'Bypass security restrictions']
      },
      {
        id: 'advanced-30',
        title: 'Advanced Template Injection Chain',
        description: 'Chain SSTI with other vulnerabilities',
        category: 'advanced',
        type: 'Template injection',
        difficulty: 'Hard',
        points: 10,
        order: 30,
        flag: 'wolf{ssti_chain_exploited}',
        instructions: 'Chain template injection with file operations',
        hints: ['Combine with file upload', 'Use template inheritance', 'Exploit template caching']
      },
      {
        id: 'advanced-31',
        title: 'Container Escape',
        description: 'Escape from containerized environment',
        category: 'advanced',
        type: 'Container security',
        difficulty: 'Hard',
        points: 10,
        order: 31,
        flag: 'wolf{container_escaped}',
        instructions: 'Break out of Docker container to host system',
        hints: ['Exploit privileged containers', 'Mount host filesystem', 'Use kernel vulnerabilities']
      },
      {
        id: 'advanced-32',
        title: 'Advanced Privilege Escalation',
        description: 'Escalate privileges in Linux system',
        category: 'advanced',
        type: 'System exploitation',
        difficulty: 'Hard',
        points: 10,
        order: 32,
        flag: 'wolf{privilege_escalated}',
        instructions: 'Gain root access through privilege escalation',
        hints: ['Check SUID binaries', 'Exploit sudo misconfigurations', 'Use kernel exploits']
      },
      {
        id: 'advanced-33',
        title: 'Advanced Network Exploitation',
        description: 'Exploit network protocol vulnerabilities',
        category: 'advanced',
        type: 'Network attacks',
        difficulty: 'Hard',
        points: 10,
        order: 33,
        flag: 'wolf{network_exploited}',
        instructions: 'Exploit custom network protocol',
        hints: ['Analyze protocol structure', 'Find parsing vulnerabilities', 'Exploit state machines']
      },
      {
        id: 'advanced-34',
        title: 'Advanced Firmware Exploitation',
        description: 'Exploit embedded firmware vulnerabilities',
        category: 'advanced',
        type: 'Firmware analysis',
        difficulty: 'Hard',
        points: 10,
        order: 34,
        flag: 'wolf{firmware_exploited}',
        instructions: 'Extract and exploit firmware vulnerabilities',
        hints: ['Extract firmware image', 'Analyze binary structure', 'Find hardcoded credentials']
      },
      {
        id: 'advanced-35',
        title: 'Advanced Reverse Engineering',
        description: 'Reverse engineer complex binary',
        category: 'advanced',
        type: 'Reverse engineering',
        difficulty: 'Hard',
        points: 10,
        order: 35,
        flag: 'wolf{reverse_engineering_master}',
        instructions: 'Reverse engineer obfuscated binary',
        hints: ['Use disassemblers', 'Analyze control flow', 'Defeat anti-debugging']
      },
      {
        id: 'advanced-36',
        title: 'Advanced Malware Analysis',
        description: 'Analyze sophisticated malware sample',
        category: 'advanced',
        type: 'Malware analysis',
        difficulty: 'Hard',
        points: 10,
        order: 36,
        flag: 'wolf{malware_analyzed}',
        instructions: 'Extract IOCs from malware sample',
        hints: ['Use sandbox analysis', 'Static and dynamic analysis', 'Decode obfuscated strings']
      },
      {
        id: 'advanced-37',
        title: 'Advanced Forensics',
        description: 'Perform advanced digital forensics',
        category: 'advanced',
        type: 'Digital forensics',
        difficulty: 'Hard',
        points: 10,
        order: 37,
        flag: 'wolf{forensics_expert}',
        instructions: 'Recover deleted evidence from disk image',
        hints: ['Use forensic tools', 'Analyze file systems', 'Recover deleted files']
      },
      {
        id: 'advanced-38',
        title: 'Advanced Steganography',
        description: 'Extract hidden data using advanced techniques',
        category: 'advanced',
        type: 'Steganography',
        difficulty: 'Hard',
        points: 10,
        order: 38,
        flag: 'wolf{steganography_master}',
        instructions: 'Extract hidden message from multimedia file',
        hints: ['Use LSB analysis', 'Check metadata', 'Analyze frequency domain']
      },
      {
        id: 'advanced-39',
        title: 'Advanced OSINT',
        description: 'Perform advanced open source intelligence',
        category: 'advanced',
        type: 'OSINT',
        difficulty: 'Hard',
        points: 10,
        order: 39,
        flag: 'wolf{osint_investigator}',
        instructions: 'Gather intelligence using public sources',
        hints: ['Use social media analysis', 'Geolocation techniques', 'Metadata analysis']
      },
      {
        id: 'advanced-40',
        title: 'The Ultimate Challenge',
        description: 'Master-level multi-stage challenge',
        category: 'advanced',
        type: 'Multi-stage',
        difficulty: 'Expert',
        points: 10,
        order: 40,
        flag: 'wolf{ultimate_wolf_champion}',
        instructions: 'Combine all your skills to solve this ultimate challenge',
        hints: ['Use everything you\'ve learned', 'Think outside the box', 'The wolf pack awaits your victory']
      }
    ];
  }

  async loadUserProgress() {
    const user = authManager.getCurrentUser();
    if (!user) {
      console.log('🐺 No authenticated user, skipping progress load');
      return;
    }

    try {
      console.log('🐺 Loading user progress for:', user.email);

      // Check if Firebase is available
      if (!db || typeof doc === 'undefined') {
        console.warn('🐺 Firebase not available, loading from localStorage');
        this.loadUserProgressOffline(user);
        return;
      }

      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        this.userProgress = userSnap.data();
        console.log('🐺 User progress loaded from Firebase:', this.userProgress);
        this.updateProgressDisplay();
      } else {
        console.log('🐺 User document not found, creating new progress');
        // Initialize new user progress
        await this.initializeNewUserProgress(user);
      }
    } catch (error) {
      console.error('🐺 Error loading user progress:', error);
      // Fallback to offline mode
      this.loadUserProgressOffline(user);
    }
  }

  loadUserProgressOffline(user) {
    try {
      const storageKey = `ctf_progress_${user.uid}`;
      const storedProgress = localStorage.getItem(storageKey);

      if (storedProgress) {
        this.userProgress = JSON.parse(storedProgress);
        console.log('🐺 User progress loaded from localStorage:', this.userProgress);
      } else {
        this.userProgress = this.createDefaultUserProgress();
        localStorage.setItem(storageKey, JSON.stringify(this.userProgress));
        console.log('🐺 Created new offline user progress');
      }

      this.updateProgressDisplay();
    } catch (error) {
      console.error('🐺 Error loading offline progress:', error);
      this.userProgress = this.createDefaultUserProgress();
      this.updateProgressDisplay();
    }
  }

  async initializeNewUserProgress(user) {
    const defaultProgress = this.createDefaultUserProgress();

    try {
      if (db && typeof setDoc !== 'undefined') {
        const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
        const userData = {
          ...defaultProgress,
          email: user.email,
          uid: user.uid,
          role: 'participant',
          createdAt: serverTimestamp(),
          lastActivity: serverTimestamp()
        };

        await setDoc(userRef, userData);
        console.log('🐺 New user document created in Firebase');
        this.userProgress = userData;
      } else {
        // Offline fallback
        const storageKey = `ctf_progress_${user.uid}`;
        localStorage.setItem(storageKey, JSON.stringify(defaultProgress));
        this.userProgress = defaultProgress;
        console.log('🐺 New user progress created offline');
      }
    } catch (error) {
      console.error('🐺 Error creating user document:', error);
      // Use default progress anyway
      this.userProgress = defaultProgress;
    }

    this.updateProgressDisplay();
  }

  createDefaultUserProgress() {
    return {
      score: 0,
      challengesSolved: 0,
      solvedChallenges: [],
      progress: {
        beginner: {
          solved: 0,
          total: CTF_CONFIG.SCORING.BEGINNER.challenges
        },
        intermediate: {
          solved: 0,
          total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges
        },
        advanced: {
          solved: 0,
          total: CTF_CONFIG.SCORING.ADVANCED.challenges
        }
      }
    };
  }

  updateProgressDisplay() {
    if (!this.userProgress) return;

    // Update progress stats with enhanced display
    const totalScoreElement = document.getElementById('total-score');
    const challengesSolvedElement = document.getElementById('challenges-solved');

    if (totalScoreElement) {
      totalScoreElement.textContent = this.userProgress.score || 0;
      // Add visual emphasis for score milestones
      const score = this.userProgress.score || 0;
      if (score >= 500) {
        totalScoreElement.className = 'text-3xl font-bold text-purple-600';
      } else if (score >= 300) {
        totalScoreElement.className = 'text-3xl font-bold text-blue-600';
      } else if (score >= 100) {
        totalScoreElement.className = 'text-3xl font-bold text-green-600';
      } else {
        totalScoreElement.className = 'text-3xl font-bold text-gray-800';
      }
    }

    // Calculate total challenges once
    const totalChallenges = CTF_CONFIG.SCORING.BEGINNER.challenges +
                           CTF_CONFIG.SCORING.INTERMEDIATE.challenges +
                           CTF_CONFIG.SCORING.ADVANCED.challenges;

    if (challengesSolvedElement) {
      challengesSolvedElement.textContent = `${this.userProgress.challengesSolved || 0}`;
    }

    // Update completion rate
    const completionRateElement = document.getElementById('completion-rate');
    if (completionRateElement) {
      const completionRate = Math.round(((this.userProgress.challengesSolved || 0) / totalChallenges) * 100);
      completionRateElement.textContent = `${completionRate}%`;

      // Color coding for completion rate
      if (completionRate >= 80) {
        completionRateElement.className = 'text-4xl font-bold text-green-600';
      } else if (completionRate >= 50) {
        completionRateElement.className = 'text-4xl font-bold text-yellow-600';
      } else if (completionRate >= 25) {
        completionRateElement.className = 'text-4xl font-bold text-orange-600';
      } else {
        completionRateElement.className = 'text-4xl font-bold text-red-600';
      }
    }

    // Update progress bars for each category
    this.updateCategoryProgress();

    // Update scoring breakdown if element exists
    this.updateScoringBreakdown();
  }

  updateScoringBreakdown() {
    const breakdownElement = document.getElementById('scoring-breakdown');
    if (!breakdownElement || !this.userProgress) return;

    const beginnerScore = (this.userProgress.progress?.beginner?.solved || 0) * CTF_CONFIG.SCORING.BEGINNER.pointsPerChallenge;
    const intermediateScore = (this.userProgress.progress?.intermediate?.solved || 0) * CTF_CONFIG.SCORING.INTERMEDIATE.pointsPerChallenge;
    const advancedScore = (this.userProgress.progress?.advanced?.solved || 0) * CTF_CONFIG.SCORING.ADVANCED.pointsPerChallenge;

    breakdownElement.innerHTML = `
      <div class="text-sm space-y-1">
        <div class="flex justify-between">
          <span>🟢 Beginner:</span>
          <span class="font-bold">${beginnerScore}/${CTF_CONFIG.SCORING.BEGINNER.totalPoints}</span>
        </div>
        <div class="flex justify-between">
          <span>🟡 Intermediate:</span>
          <span class="font-bold">${intermediateScore}/${CTF_CONFIG.SCORING.INTERMEDIATE.totalPoints}</span>
        </div>
        <div class="flex justify-between">
          <span>🔴 Advanced:</span>
          <span class="font-bold">${advancedScore}/${CTF_CONFIG.SCORING.ADVANCED.totalPoints}</span>
        </div>
        <hr class="my-2">
        <div class="flex justify-between font-bold text-lg">
          <span>Total:</span>
          <span>${this.userProgress.score || 0}/${CTF_CONFIG.TOTAL_POSSIBLE_SCORE}</span>
        </div>
      </div>
    `;
  }

  updateCategoryProgress() {
    const categories = ['beginner', 'intermediate', 'advanced'];
    
    categories.forEach(category => {
      const progress = this.userProgress.progress[category];
      const progressBar = document.getElementById(`${category}-progress`);
      const progressText = document.getElementById(`${category}-progress-text`);
      
      if (progressBar && progressText) {
        const percentage = (progress.solved / progress.total) * 100;
        progressBar.style.width = `${percentage}%`;
        progressText.textContent = `${progress.solved}/${progress.total}`;
      }
    });
  }

  renderChallenges() {
    console.log('🐺 renderChallenges called, challenges count:', this.challenges.length);
    const categoriesContainer = document.getElementById('challenge-categories');
    if (!categoriesContainer) {
      console.error('🐺 challenge-categories container not found');
      return;
    }

    const categories = {
      beginner: { name: 'Beginner', icon: '🟢', challenges: [] },
      intermediate: { name: 'Intermediate', icon: '🟡', challenges: [] },
      advanced: { name: 'Advanced', icon: '🔴', challenges: [] }
    };

    // Group challenges by category
    this.challenges.forEach(challenge => {
      console.log('🐺 Processing challenge:', challenge.title, 'Category:', challenge.category);
      if (categories[challenge.category]) {
        categories[challenge.category].challenges.push(challenge);
      } else {
        console.warn('🐺 Unknown category:', challenge.category, 'for challenge:', challenge.title);
      }
    });

    // Log category counts
    Object.keys(categories).forEach(categoryKey => {
      console.log(`🐺 ${categoryKey} challenges:`, categories[categoryKey].challenges.length);
    });

    // Render each category
    categoriesContainer.innerHTML = '';
    Object.keys(categories).forEach(categoryKey => {
      const category = categories[categoryKey];
      const categoryDiv = this.createCategorySection(categoryKey, category);
      categoriesContainer.appendChild(categoryDiv);
    });

    console.log('🐺 Challenges rendered successfully');
  }

  createCategorySection(categoryKey, category) {
    const div = document.createElement('div');
    div.className = 'neo-brutalist bg-white p-6';

    const solvedCount = this.userProgress?.progress[categoryKey]?.solved || 0;
    const totalCount = CTF_CONFIG.SCORING[categoryKey.toUpperCase()].challenges;
    const points = CTF_CONFIG.SCORING[categoryKey.toUpperCase()].pointsPerChallenge;
    
    div.innerHTML = `
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-2xl font-bold">${category.icon} ${category.name} Level</h3>
        <div class="text-right">
          <div class="text-lg font-bold">${solvedCount}/${totalCount} Solved</div>
          <div class="text-sm text-gray-600">${points} points each</div>
          <div class="text-xs text-blue-600 font-bold">Total: ${solvedCount * points}/${totalCount * points} points</div>
        </div>
      </div>
      
      <div class="mb-4">
        <div class="neo-brutalist w-full bg-gray-300 h-4 overflow-hidden">
          <div id="${categoryKey}-progress" class="bg-green-500 h-full transition-all duration-300" 
               style="width: ${(solvedCount/totalCount)*100}%"></div>
        </div>
        <div class="text-center mt-2">
          <span id="${categoryKey}-progress-text" class="text-sm font-bold">${solvedCount}/${totalCount}</span>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="${categoryKey}-challenges">
        ${this.renderChallengeCards(category.challenges)}
      </div>
    `;
    
    return div;
  }

  renderChallengeCards(challenges) {
    return challenges.map(challenge => {
      const isSolved = this.userProgress?.solvedChallenges?.includes(challenge.id) || false;
      const isAccessible = this.isChallengeAccessible(challenge);
      const isLocked = !isAccessible; // Invert the logic - locked when NOT accessible

      return `
        <div class="neo-brutalist bg-gray-50 p-4 challenge-card ${isSolved ? 'solved' : ''} ${isLocked ? 'locked' : ''}"
             data-challenge-id="${challenge.id}">
          <div class="flex justify-between items-start mb-2">
            <h4 class="text-lg font-bold">${challenge.title}</h4>
            <div class="flex items-center space-x-2">
              ${isSolved ? '<i class="fas fa-check-circle text-green-500"></i>' : ''}
              ${isLocked ? '<i class="fas fa-lock text-gray-400"></i>' : ''}
              <span class="text-sm font-bold px-2 py-1 rounded ${isSolved ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}">${challenge.points} pts</span>
            </div>
          </div>

          <p class="text-sm text-gray-600 mb-3">${challenge.description}</p>

          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-2">
              <span class="text-xs px-2 py-1 bg-gray-200 rounded">${challenge.type}</span>
              <span class="text-xs px-2 py-1 bg-blue-200 rounded">${challenge.difficulty}</span>
            </div>

            <button class="challenge-btn neo-brutalist px-4 py-2 text-sm font-bold
                     ${isSolved ? 'bg-green-500 text-white cursor-not-allowed' :
                       isLocked ? 'bg-gray-300 text-gray-500 cursor-not-allowed' :
                       'bg-yellow-400 text-black hover:bg-yellow-500'}"
                    ${isSolved || isLocked ? 'disabled' : ''}>
              ${isSolved ? 'SOLVED' : isLocked ? 'LOCKED' : 'START'}
            </button>
          </div>
        </div>
      `;
    }).join('');
  }

  isChallengeAccessible(challenge) {
    // For now, all challenges are accessible
    // Later can implement prerequisite logic based on challenge.prerequisites
    // Example: return !challenge.prerequisites || challenge.prerequisites.every(id => this.userProgress?.solvedChallenges?.includes(id));

    // Validate challenge exists
    if (!challenge || !challenge.id) {
      console.warn('🐺 Invalid challenge provided to accessibility check');
      return false;
    }

    return true;
  }

  setupChallengeModal() {
    const modal = document.getElementById('challenge-modal');
    const closeBtn = document.getElementById('close-challenge');
    
    // Close modal handlers
    closeBtn?.addEventListener('click', () => this.closeChallengeModal());
    modal?.addEventListener('click', (e) => {
      if (e.target === modal) this.closeChallengeModal();
    });
    
    // Challenge card click handlers
    document.addEventListener('click', (e) => {
      const challengeCard = e.target.closest('.challenge-card');
      const challengeBtn = e.target.closest('.challenge-btn');

      console.log('🐺 Click detected:', {
        target: e.target,
        challengeCard,
        challengeBtn,
        disabled: challengeBtn?.disabled,
        challengeId: challengeCard?.dataset?.challengeId
      });

      if (challengeBtn && !challengeBtn.disabled) {
        const challengeId = challengeCard.dataset.challengeId;
        console.log('🐺 Opening challenge:', challengeId);
        this.openChallenge(challengeId);
      } else if (challengeBtn && challengeBtn.disabled) {
        console.log('🐺 Button is disabled, not opening challenge');
      } else if (!challengeBtn) {
        console.log('🐺 No challenge button found in click target');
      }
    });
  }

  async openChallenge(challengeId) {
    console.log('🐺 openChallenge called with ID:', challengeId);
    const challenge = this.challenges.find(c => c.id === challengeId);
    console.log('🐺 Found challenge:', challenge);

    if (!challenge) {
      console.error('🐺 Challenge not found:', challengeId);
      return;
    }

    // Check if already solved
    if (this.userProgress?.solvedChallenges?.includes(challengeId)) {
      alert('You have already solved this challenge!');
      return;
    }

    const modal = document.getElementById('challenge-modal');
    const title = document.getElementById('challenge-title');
    const content = document.getElementById('challenge-content');

    console.log('🐺 Modal elements:', { modal, title, content });

    if (!modal || !title || !content) {
      console.error('🐺 Modal elements not found');
      return;
    }

    title.textContent = challenge.title;
    content.innerHTML = await this.renderChallengeContent(challenge);

    modal.classList.remove('hidden');
    console.log('🐺 Modal opened for challenge:', challenge.title);

    // Setup flag submission
    this.setupFlagSubmission(challenge);
  }

  async renderChallengeContent(challenge) {
    return `
      <div class="space-y-4">
        <div class="p-4 bg-gray-100 border-4 border-gray-300">
          <h4 class="font-bold mb-2">Challenge Description</h4>
          <p>${challenge.description}</p>
        </div>
        
        <div class="p-4 bg-blue-50 border-4 border-blue-300">
          <h4 class="font-bold mb-2">Instructions</h4>
          <div>${challenge.instructions || 'Find the flag and submit it below.'}</div>
        </div>
        
        ${challenge.content ? `
          <div class="p-4 bg-yellow-50 border-4 border-yellow-300">
            <h4 class="font-bold mb-2">Challenge Content</h4>
            <div>${challenge.content}</div>
          </div>
        ` : ''}
        
        ${challenge.hints ? `
          <div class="p-4 bg-green-50 border-4 border-green-300">
            <h4 class="font-bold mb-2">💡 Hints</h4>
            <ul class="list-disc list-inside space-y-1">
              ${challenge.hints.map(hint => `<li>${hint}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
        
        <div class="p-4 bg-red-50 border-4 border-red-300">
          <h4 class="font-bold mb-2">🚩 Submit Flag</h4>
          <form id="flag-form" data-no-csrf="true">
            <div class="flex space-x-2">
              <input type="text" id="flag-input" placeholder="wolf{flag_here}"
                     class="neo-brutalist flex-1 p-3 text-lg bg-white border-4 border-black focus:outline-none">
              <button type="submit" id="submit-flag" class="neo-brutalist bg-red-500 text-white px-6 py-3 text-lg font-bold">
                SUBMIT
              </button>
            </div>
          </form>
          <div id="flag-feedback" class="mt-2 hidden"></div>
        </div>
      </div>
    `;
  }

  setupFlagSubmission(challenge) {
    const flagForm = document.getElementById('flag-form');
    const flagInput = document.getElementById('flag-input');
    const feedback = document.getElementById('flag-feedback');

    flagForm?.addEventListener('submit', async (e) => {
      e.preventDefault();
      const flag = flagInput.value.trim();
      await this.submitFlag(challenge, flag, feedback);
    });

    flagInput?.addEventListener('keypress', async (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        const flag = flagInput.value.trim();
        await this.submitFlag(challenge, flag, feedback);
      }
    });
  }

  async submitFlag(challenge, flag, feedbackElement) {
    console.log('🐺 Flag submission started:', { challengeId: challenge.id, flag: flag, challengeFlag: challenge.flag });

    // Validate inputs
    if (!challenge) {
      console.error('🐺 No challenge provided');
      this.showFeedback(feedbackElement, '❌ Invalid challenge. Please try again.', 'error');
      return;
    }

    if (!flag) {
      this.showFeedback(feedbackElement, '❌ Please enter a flag!', 'error');
      return;
    }

    // Show loading state
    this.showFeedback(feedbackElement, '🔄 Validating flag...', 'info');

    try {
      // Check authentication first
      const user = authManager.getCurrentUser();
      if (!user) {
        console.error('🐺 No authenticated user found');
        this.showFeedback(feedbackElement, '❌ Please log in to submit flags', 'error');
        return;
      }

      console.log('🐺 User authenticated:', user.email);

      // Ensure user progress is initialized
      if (!this.userProgress) {
        console.log('🐺 User progress not initialized, creating default');
        this.userProgress = this.createDefaultUserProgress();
      }

      // Check if challenge is already solved
      if (this.userProgress.solvedChallenges && this.userProgress.solvedChallenges.includes(challenge.id)) {
        console.log('🐺 Challenge already solved');
        this.showFeedback(feedbackElement, '✅ You have already solved this challenge!', 'warning');
        return;
      }

      // Validate flag format
      if (!flag.startsWith('wolf{') || !flag.endsWith('}')) {
        console.log('🐺 Invalid flag format');
        this.showFeedback(feedbackElement, '❌ Flag must be in format: wolf{flag_here}', 'error');
        return;
      }

      // Validate challenge has a flag
      if (!challenge.flag) {
        console.error('🐺 Challenge has no flag defined');
        this.showFeedback(feedbackElement, '❌ Challenge configuration error. Please contact administrator.', 'error');
        return;
      }

      // Check flag correctness
      console.log('🐺 Comparing flags:', { submitted: flag, expected: challenge.flag });
      if (flag === challenge.flag) {
        console.log('🐺 Flag is correct, processing...');

        // Store old score for animation
        const oldScore = this.userProgress?.score || 0;

        await this.handleCorrectFlag(challenge);

        // Calculate new total score for display
        const newTotalScore = this.userProgress?.score || 0;

        // Show animated score update
        this.updateScoreWithAnimation(oldScore, newTotalScore, challenge.points);

        this.showFeedback(feedbackElement, `🎉 Correct! Challenge solved! +${challenge.points} points (Total: ${newTotalScore})`, 'success');

        // Close modal after short delay
        setTimeout(() => {
          this.closeChallengeModal();
          this.refreshChallenges();
        }, 3000);
      } else {
        console.log('🐺 Flag is incorrect');
        this.showFeedback(feedbackElement, '❌ Incorrect flag. Try again!', 'error');
      }
    } catch (error) {
      console.error('🐺 Error submitting flag:', error);
      console.error('🐺 Error stack:', error.stack);

      // Provide more specific error messages
      let errorMessage = '❌ Error submitting flag. Please try again.';

      if (error.message.includes('permission-denied')) {
        errorMessage = '❌ Permission denied. Please check your account permissions.';
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = '❌ Network error. Please check your connection and try again.';
      } else if (error.message.includes('auth') || error.message.includes('User not authenticated')) {
        errorMessage = '❌ Authentication error. Please log in again.';
      } else if (error.message.includes('already solved')) {
        errorMessage = '✅ You have already solved this challenge!';
      } else if (error.message.includes('User profile not found')) {
        errorMessage = '❌ User profile error. Please contact administrator.';
      } else if (error.message.includes('Firebase')) {
        errorMessage = '❌ Database error. Attempting offline mode...';
        // Try offline fallback for correct flags
        if (flag === challenge.flag) {
          try {
            // Store old score for animation
            const oldScore = this.userProgress?.score || 0;

            await this.handleCorrectFlagOffline(challenge, user);
            const newTotalScore = (this.userProgress?.score || 0);

            // Show animated score update
            this.updateScoreWithAnimation(oldScore, newTotalScore, challenge.points);

            this.showFeedback(feedbackElement, `🎉 Correct! Challenge solved offline! +${challenge.points} points (Total: ${newTotalScore})`, 'success');
            setTimeout(() => {
              this.closeChallengeModal();
              this.refreshChallenges();
            }, 3000);
            return;
          } catch (offlineError) {
            console.error('🐺 Offline fallback also failed:', offlineError);
            errorMessage = '❌ Unable to save progress. Please try again later.';
          }
        }
      }

      this.showFeedback(feedbackElement, errorMessage, 'error');
    }
  }

  async handleCorrectFlag(challenge) {
    const user = authManager.getCurrentUser();
    if (!user) {
      console.error('🐺 No authenticated user for flag submission');
      throw new Error('User not authenticated');
    }

    console.log('🐺 Processing correct flag for challenge:', challenge.title);

    // Validate challenge data
    if (!challenge.id || !challenge.points || !challenge.category) {
      console.error('🐺 Invalid challenge data:', challenge);
      throw new Error('Invalid challenge data');
    }

    try {
      // Check if Firebase is available
      if (!db || typeof doc === 'undefined') {
        console.warn('🐺 Firebase not available, using local storage fallback');
        return this.handleCorrectFlagOffline(challenge, user);
      }

      // Use the enhanced score service for awarding points
      console.log('🐺 Using enhanced score service for challenge completion');
      const result = await scoreService.awardChallengePoints(
        user.uid,
        challenge.id,
        challenge.points,
        {
          title: challenge.title,
          category: challenge.category,
          difficulty: challenge.difficulty,
          flag: challenge.flag
        }
      );

      // Update local user progress cache
      if (result.success) {
        this.userProgress = {
          ...this.userProgress,
          score: result.newScore,
          challengesSolved: result.challengesSolved
        };

        // Refresh user progress from database to get updated progress
        await this.loadUserProgress();

        console.log('🐺 Challenge completion processed successfully via score service');
        return result;
      } else {
        throw new Error('Score service failed to award points');
      }
    } catch (error) {
      console.error('🐺 Error in enhanced flag handling:', error);

      // Fallback to offline mode for certain errors
      if (error.message.includes('permission-denied') ||
          error.message.includes('network') ||
          error.message.includes('fetch') ||
          error.message.includes('unavailable') ||
          error.message.includes('already solved')) {
        console.log('🐺 Falling back to offline mode due to:', error.message);
        return this.handleCorrectFlagOffline(challenge, user);
      }

      throw error;
    }

  }

  handleCorrectFlagOffline(challenge, user) {
    console.log('🐺 Handling flag submission offline');

    // Validate inputs
    if (!challenge || !user) {
      throw new Error('Invalid challenge or user data for offline handling');
    }

    try {
      // Get current progress from localStorage
      const storageKey = `ctf_progress_${user.uid}`;
      let userProgress;

      try {
        const storedData = localStorage.getItem(storageKey);
        userProgress = storedData ? JSON.parse(storedData) : null;
      } catch (parseError) {
        console.warn('🐺 Error parsing stored progress, creating new:', parseError);
        userProgress = null;
      }

      // Initialize if empty or invalid
      if (!userProgress || typeof userProgress !== 'object') {
        userProgress = this.createDefaultUserProgress();
        console.log('🐺 Created new offline progress structure');
      }

      // Ensure all required properties exist
      if (!userProgress.solvedChallenges) userProgress.solvedChallenges = [];
      if (!userProgress.progress) userProgress.progress = this.createDefaultUserProgress().progress;
      if (typeof userProgress.score !== 'number') userProgress.score = 0;
      if (typeof userProgress.challengesSolved !== 'number') userProgress.challengesSolved = 0;

      // Check if already solved
      if (userProgress.solvedChallenges.includes(challenge.id)) {
        console.log('🐺 Challenge already solved offline');
        throw new Error('Challenge already solved');
      }

      // Ensure category progress exists
      if (!userProgress.progress[challenge.category]) {
        userProgress.progress[challenge.category] = {
          solved: 0,
          total: CTF_CONFIG.SCORING[challenge.category.toUpperCase()].challenges
        };
      }

      // Update progress
      userProgress.score += challenge.points;
      userProgress.challengesSolved += 1;
      userProgress.solvedChallenges.push(challenge.id);
      userProgress.progress[challenge.category].solved += 1;
      userProgress.lastActivity = Date.now();

      // Save to localStorage with error handling
      try {
        localStorage.setItem(storageKey, JSON.stringify(userProgress));
        console.log('🐺 Progress saved to localStorage successfully');
      } catch (storageError) {
        console.error('🐺 Failed to save to localStorage:', storageError);
        throw new Error('Unable to save progress offline. Storage may be full.');
      }

      // Update local cache
      this.userProgress = userProgress;

      console.log('🐺 Challenge solved offline:', challenge.title, 'Points:', challenge.points);

      // Show offline notice
      this.showOfflineNotice();

      return userProgress;

    } catch (error) {
      console.error('🐺 Error in offline flag handling:', error);
      throw error;
    }
  }

  showOfflineNotice() {
    // Prevent multiple notices
    if (document.querySelector('.offline-notice')) {
      return;
    }

    setTimeout(() => {
      const notice = document.createElement('div');
      notice.className = 'offline-notice fixed top-4 right-4 bg-orange-500 text-white p-3 rounded shadow-lg z-50 max-w-sm';
      notice.innerHTML = `
        <div class="flex items-center">
          <span class="mr-2">📱</span>
          <div>
            <div class="font-bold">Offline Mode</div>
            <div class="text-sm">Progress saved locally. Will sync when online.</div>
          </div>
        </div>
      `;
      document.body.appendChild(notice);

      setTimeout(() => {
        if (notice.parentNode) {
          notice.parentNode.removeChild(notice);
        }
      }, 5000);
    }, 1000);
  }

  // Method to show animated score update
  showScoreAnimation(pointsEarned, newTotalScore) {
    // Create floating score animation
    const scoreAnimation = document.createElement('div');
    scoreAnimation.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 pointer-events-none';
    scoreAnimation.innerHTML = `
      <div class="bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg text-center animate-bounce">
        <div class="text-2xl font-bold">+${pointsEarned} Points!</div>
        <div class="text-sm">Total: ${newTotalScore}</div>
      </div>
    `;

    document.body.appendChild(scoreAnimation);

    // Remove animation after 3 seconds
    setTimeout(() => {
      if (scoreAnimation.parentNode) {
        scoreAnimation.style.opacity = '0';
        scoreAnimation.style.transform = 'translate(-50%, -50%) scale(0.5)';
        scoreAnimation.style.transition = 'all 0.5s ease-out';

        setTimeout(() => {
          if (scoreAnimation.parentNode) {
            scoreAnimation.parentNode.removeChild(scoreAnimation);
          }
        }, 500);
      }
    }, 2500);
  }

  // Method to update score display with animation
  updateScoreWithAnimation(oldScore, newScore, pointsEarned) {
    // Show floating animation
    this.showScoreAnimation(pointsEarned, newScore);

    // Animate the score counter
    const scoreElement = document.getElementById('total-score');
    if (scoreElement) {
      let currentScore = oldScore;
      const duration = 1000; // 1 second animation
      const steps = Math.floor(duration / 50); // 50ms intervals
      const scoreIncrement = pointsEarned / steps;

      let step = 0;
      const animateScore = () => {
        if (step < steps) {
          currentScore += scoreIncrement;
          scoreElement.textContent = Math.floor(currentScore);
          step++;
          setTimeout(animateScore, 50);
        } else {
          scoreElement.textContent = newScore;
        }
      };

      animateScore();
    }
  }

  showFeedback(element, message, type) {
    if (!element) {
      console.warn('🐺 Feedback element not found');
      return;
    }

    console.log('🐺 Showing feedback:', { message, type });

    let bgColor, borderColor, textColor;

    switch (type) {
      case 'success':
        bgColor = 'bg-green-100';
        borderColor = 'border-green-500';
        textColor = 'text-green-700';
        break;
      case 'warning':
        bgColor = 'bg-yellow-100';
        borderColor = 'border-yellow-500';
        textColor = 'text-yellow-700';
        break;
      case 'info':
        bgColor = 'bg-blue-100';
        borderColor = 'border-blue-500';
        textColor = 'text-blue-700';
        break;
      case 'error':
      default:
        bgColor = 'bg-red-100';
        borderColor = 'border-red-500';
        textColor = 'text-red-700';
        break;
    }

    element.className = `mt-2 p-2 border-4 font-bold ${bgColor} ${borderColor} ${textColor}`;
    element.textContent = message;
    element.classList.remove('hidden');

    // Auto-hide info messages after 3 seconds
    if (type === 'info') {
      setTimeout(() => {
        if (element && element.textContent === message) {
          element.classList.add('hidden');
        }
      }, 3000);
    }
  }

  closeChallengeModal() {
    document.getElementById('challenge-modal').classList.add('hidden');
  }

  async refreshChallenges() {
    await this.loadUserProgress();
    this.renderChallenges();
  }

  showInitializationError(error) {
    const categoriesContainer = document.getElementById('challenge-categories');
    if (categoriesContainer) {
      categoriesContainer.innerHTML = `
        <div class="neo-brutalist bg-red-100 border-4 border-red-500 p-6 text-center">
          <div class="text-4xl mb-4">⚠️</div>
          <h3 class="text-2xl font-bold text-red-700 mb-4">Challenge Loading Error</h3>
          <p class="text-red-600 mb-4">${error.message}</p>
          <button onclick="location.reload()"
                  class="neo-brutalist bg-red-500 text-white px-6 py-3 font-bold">
            RETRY
          </button>
        </div>
      `;
    }
  }

  // Debug method to test challenge rendering
  testChallengeRendering() {
    console.log('🐺 Testing challenge rendering...');
    console.log('🐺 Total challenges:', this.challenges.length);
    console.log('🐺 Sample challenge:', this.challenges[0]);

    const categoriesContainer = document.getElementById('challenge-categories');
    console.log('🐺 Categories container:', categoriesContainer);

    if (categoriesContainer) {
      console.log('🐺 Container innerHTML length:', categoriesContainer.innerHTML.length);
      console.log('🐺 Container children count:', categoriesContainer.children.length);
    }

    // Test if we can find challenge buttons
    const challengeButtons = document.querySelectorAll('.challenge-btn');
    console.log('🐺 Challenge buttons found:', challengeButtons.length);
    challengeButtons.forEach((btn, index) => {
      console.log(`🐺 Button ${index}:`, btn, 'Disabled:', btn.disabled);
    });
  }

  // Debug method to test flag submission
  testFlagSubmission(challengeId, testFlag) {
    console.log('🐺 Testing flag submission...');
    const challenge = this.challenges.find(c => c.id === challengeId);
    if (!challenge) {
      console.error('🐺 Challenge not found:', challengeId);
      return;
    }

    console.log('🐺 Testing challenge:', challenge.title);
    console.log('🐺 Expected flag:', challenge.flag);
    console.log('🐺 Test flag:', testFlag);

    // Create a mock feedback element
    const mockFeedback = document.createElement('div');
    document.body.appendChild(mockFeedback);

    this.submitFlag(challenge, testFlag, mockFeedback).then(() => {
      console.log('🐺 Flag submission test completed');
      setTimeout(() => {
        if (mockFeedback.parentNode) {
          mockFeedback.parentNode.removeChild(mockFeedback);
        }
      }, 5000);
    });
  }

  // Debug method to check authentication status
  checkAuthStatus() {
    const user = authManager.getCurrentUser();
    console.log('🐺 Authentication status:', {
      user: user,
      email: user?.email,
      uid: user?.uid,
      userProgress: this.userProgress
    });
    return user;
  }

  // Comprehensive debug method for flag submission issues
  debugFlagSubmission() {
    console.log('🐺 === FLAG SUBMISSION DEBUG REPORT ===');

    // Check authentication
    const user = authManager.getCurrentUser();
    console.log('🐺 User Authentication:', {
      authenticated: !!user,
      email: user?.email,
      uid: user?.uid
    });

    // Check Firebase connectivity
    console.log('🐺 Firebase Status:', {
      dbAvailable: !!db,
      docFunction: typeof doc,
      getDocFunction: typeof getDoc,
      updateDocFunction: typeof updateDoc,
      setDocFunction: typeof setDoc
    });

    // Check user progress
    console.log('🐺 User Progress:', {
      progressLoaded: !!this.userProgress,
      score: this.userProgress?.score,
      challengesSolved: this.userProgress?.challengesSolved,
      solvedChallenges: this.userProgress?.solvedChallenges?.length || 0,
      progressStructure: this.userProgress?.progress
    });

    // Check CTF configuration
    console.log('🐺 CTF Configuration:', {
      collections: CTF_CONFIG.COLLECTIONS,
      scoring: CTF_CONFIG.SCORING,
      userRoles: CTF_CONFIG.USER_ROLES
    });

    // Check localStorage
    if (user) {
      const storageKey = `ctf_progress_${user.uid}`;
      const storedData = localStorage.getItem(storageKey);
      console.log('🐺 LocalStorage:', {
        hasStoredData: !!storedData,
        storedDataLength: storedData?.length || 0
      });
    }

    // Check challenges
    console.log('🐺 Challenges:', {
      totalChallenges: this.challenges.length,
      sampleChallenge: this.challenges[0] ? {
        id: this.challenges[0].id,
        title: this.challenges[0].title,
        flag: this.challenges[0].flag ? 'Present' : 'Missing',
        points: this.challenges[0].points,
        category: this.challenges[0].category
      } : 'No challenges loaded'
    });

    console.log('🐺 === END DEBUG REPORT ===');

    return {
      user: !!user,
      firebase: !!db,
      userProgress: !!this.userProgress,
      challenges: this.challenges.length > 0
    };
  }

  // Initialize default challenges if none exist
  async initializeDefaultChallenges() {
    // This would be called by admin to set up initial challenges
    console.log('🐺 No challenges found - admin needs to create challenges');
  }

  // Method to retry failed flag submissions
  async retryFlagSubmission(challenge, flag, feedbackElement, retryCount = 0) {
    const maxRetries = 2;

    if (retryCount >= maxRetries) {
      console.error('🐺 Max retries reached for flag submission');
      this.showFeedback(feedbackElement, '❌ Maximum retry attempts reached. Please try again later.', 'error');
      return;
    }

    console.log(`🐺 Retrying flag submission (attempt ${retryCount + 1}/${maxRetries + 1})`);

    try {
      await this.submitFlag(challenge, flag, feedbackElement);
    } catch (error) {
      console.error(`🐺 Retry ${retryCount + 1} failed:`, error);

      // Wait before retrying
      setTimeout(() => {
        this.retryFlagSubmission(challenge, flag, feedbackElement, retryCount + 1);
      }, 1000 * (retryCount + 1)); // Exponential backoff
    }
  }

  // Method to validate system health before flag submission
  validateSystemHealth() {
    const issues = [];

    // Check authentication
    const user = authManager.getCurrentUser();
    if (!user) {
      issues.push('User not authenticated');
    }

    // Check user progress
    if (!this.userProgress) {
      issues.push('User progress not loaded');
    }

    // Check Firebase connectivity (optional)
    if (!db) {
      console.warn('🐺 Firebase not available - will use offline mode');
    }

    // Check challenges loaded
    if (this.challenges.length === 0) {
      issues.push('No challenges loaded');
    }

    return {
      healthy: issues.length === 0,
      issues: issues
    };
  }
}

// Initialize challenge manager
const challengeManager = new ChallengeManager();

// Expose for debugging
window.challengeManager = challengeManager;

// Add global debug functions for troubleshooting
window.debugCTF = {
  checkAuth: () => challengeManager.checkAuthStatus(),
  debugFlag: () => challengeManager.debugFlagSubmission(),
  validateHealth: () => challengeManager.validateSystemHealth(),
  testFlag: (challengeId, flag) => challengeManager.testFlagSubmission(challengeId, flag),
  resetProgress: () => {
    const user = authManager.getCurrentUser();
    if (user) {
      localStorage.removeItem(`ctf_progress_${user.uid}`);
      console.log('🐺 Progress reset - please refresh the page');
    }
  },
  getProgress: () => challengeManager.userProgress,
  getChallenges: () => challengeManager.challenges,
  showScoring: () => {
    console.log('🐺 === SCORING SYSTEM OVERVIEW ===');
    console.log('🐺 Configuration:', CTF_CONFIG.SCORING);
    console.log('🐺 Total Possible Score:', CTF_CONFIG.TOTAL_POSSIBLE_SCORE);

    if (challengeManager.userProgress) {
      const progress = challengeManager.userProgress;
      console.log('🐺 Current Progress:', {
        totalScore: progress.score,
        challengesSolved: progress.challengesSolved,
        breakdown: {
          beginner: `${progress.progress?.beginner?.solved || 0}/${CTF_CONFIG.SCORING.BEGINNER.challenges} (${(progress.progress?.beginner?.solved || 0) * CTF_CONFIG.SCORING.BEGINNER.pointsPerChallenge} pts)`,
          intermediate: `${progress.progress?.intermediate?.solved || 0}/${CTF_CONFIG.SCORING.INTERMEDIATE.challenges} (${(progress.progress?.intermediate?.solved || 0) * CTF_CONFIG.SCORING.INTERMEDIATE.pointsPerChallenge} pts)`,
          advanced: `${progress.progress?.advanced?.solved || 0}/${CTF_CONFIG.SCORING.ADVANCED.challenges} (${(progress.progress?.advanced?.solved || 0) * CTF_CONFIG.SCORING.ADVANCED.pointsPerChallenge} pts)`
        }
      });
    } else {
      console.log('🐺 No user progress loaded');
    }

    console.log('🐺 === END SCORING OVERVIEW ===');
  }
};

console.log('🐺 Challenge Manager initialized with debug tools');
console.log('🐺 Available debug commands: window.debugCTF');

export default challengeManager;
