// Admin Setup Script for The Wolf Challenge CTF Platform
import { auth, db, CTF_CONFIG } from './firebase-config.js';
import { 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js';
import { 
  doc, 
  setDoc, 
  getDoc,
  serverTimestamp 
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AdminSetup {
  constructor() {
    this.adminCredentials = {
      email: '<EMAIL>',
      password: 'tamilselvanadmin',
      displayName: 'Tamil Selvan Admin'
    };
  }

  // Main setup method
  async setupAdmin() {
    try {
      console.log('🐺 Setting up admin account...');
      
      // Show setup progress
      this.showSetupProgress();
      
      // Step 1: Create admin account
      await this.createAdminAccount();
      
      // Step 2: Test admin login
      await this.testAdminLogin();
      
      // Step 3: Show success message
      this.showSuccessMessage();
      
      console.log('✅ Admin setup completed successfully');
      
    } catch (error) {
      console.error('❌ Admin setup failed:', error);
      this.showError(error.message);
    } finally {
      this.hideSetupProgress();
    }
  }

  // Create admin account
  async createAdminAccount() {
    try {
      console.log('📝 Creating admin account...');
      
      const { email, password, displayName } = this.adminCredentials;
      
      // Try to create the account
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Create admin document in Firestore
      await this.createAdminDocument(user, displayName);
      
      console.log('✅ Admin account created successfully');
      
      // Sign out after creation
      await signOut(auth);
      
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        console.log('ℹ️ Admin account already exists');
        // Try to update existing user to admin
        await this.updateExistingUserToAdmin();
      } else {
        console.error('❌ Failed to create admin account:', error);
        throw error;
      }
    }
  }

  // Create admin document
  async createAdminDocument(user, displayName) {
    const adminRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
    
    await setDoc(adminRef, {
      email: user.email,
      role: 'admin',
      displayName: displayName,
      score: 0,
      challengesSolved: 0,
      progress: {
        beginner: { solved: 0, total: 10 },
        intermediate: { solved: 0, total: 20 },
        advanced: { solved: 0, total: 40 }
      },
      solvedChallenges: [],
      permissions: [
        'user_management',
        'challenge_management',
        'score_management',
        'system_administration',
        'push_notifications',
        'analytics_access'
      ],
      createdAt: serverTimestamp(),
      lastActivity: serverTimestamp(),
      isActive: true,
      accountStatus: 'active'
    });
  }

  // Update existing user to admin
  async updateExistingUserToAdmin() {
    try {
      // Sign in to get the user
      const userCredential = await signInWithEmailAndPassword(
        auth, 
        this.adminCredentials.email, 
        this.adminCredentials.password
      );
      
      const user = userCredential.user;
      
      // Update user document to admin
      await this.createAdminDocument(user, this.adminCredentials.displayName);
      
      console.log('✅ Existing user updated to admin');
      
      // Sign out
      await signOut(auth);
      
    } catch (error) {
      console.error('❌ Failed to update existing user:', error);
      throw error;
    }
  }

  // Test admin login
  async testAdminLogin() {
    try {
      console.log('🔍 Testing admin login...');
      
      const userCredential = await signInWithEmailAndPassword(
        auth, 
        this.adminCredentials.email, 
        this.adminCredentials.password
      );
      
      const user = userCredential.user;
      
      // Check if user has admin role
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, user.uid);
      const userDoc = await getDoc(userRef);
      
      if (userDoc.exists() && userDoc.data().role === 'admin') {
        console.log('✅ Admin login test successful');
      } else {
        throw new Error('User does not have admin role');
      }
      
      // Sign out after test
      await signOut(auth);
      
    } catch (error) {
      console.error('❌ Admin login test failed:', error);
      throw error;
    }
  }

  // Show setup progress
  showSetupProgress() {
    const progressDiv = document.createElement('div');
    progressDiv.id = 'admin-setup-progress';
    progressDiv.className = 'fixed top-4 right-4 bg-blue-500 text-white p-4 rounded shadow-lg z-50';
    progressDiv.innerHTML = `
      <div class="flex items-center space-x-2">
        <div class="loading-spinner"></div>
        <span>Setting up admin account...</span>
      </div>
    `;
    document.body.appendChild(progressDiv);
  }

  // Hide setup progress
  hideSetupProgress() {
    const progressDiv = document.getElementById('admin-setup-progress');
    if (progressDiv) {
      progressDiv.remove();
    }
  }

  // Show success message
  showSuccessMessage() {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white p-6 rounded shadow-lg z-50';
    successDiv.innerHTML = `
      <div class="space-y-3">
        <h3 class="font-bold text-lg">✅ Admin Setup Complete!</h3>
        <div class="space-y-2 text-sm">
          <p><strong>Email:</strong> ${this.adminCredentials.email}</p>
          <p><strong>Password:</strong> ${this.adminCredentials.password}</p>
          <p class="text-green-200">You can now login with these credentials to access the admin panel.</p>
        </div>
        <button onclick="this.parentElement.parentElement.remove()" 
                class="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm">
          Close
        </button>
      </div>
    `;
    document.body.appendChild(successDiv);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (successDiv.parentElement) {
        successDiv.remove();
      }
    }, 10000);
  }

  // Show error message
  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded shadow-lg z-50';
    errorDiv.innerHTML = `
      <div class="space-y-2">
        <h3 class="font-bold">❌ Setup Failed</h3>
        <p class="text-sm">${message}</p>
        <button onclick="this.parentElement.parentElement.remove()" 
                class="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm">
          Close
        </button>
      </div>
    `;
    document.body.appendChild(errorDiv);
  }
}

// Create global instance
window.adminSetup = new AdminSetup();

// Auto-setup on page load (optional)
document.addEventListener('DOMContentLoaded', () => {
  console.log('🐺 Admin setup script loaded');
  console.log('Run adminSetup.setupAdmin() to create admin account');
});

export default AdminSetup;
