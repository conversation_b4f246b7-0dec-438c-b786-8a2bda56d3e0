// Real-time Notification Service for The Wolf Challenge CTF Platform
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot,
  doc,
  updateDoc,
  getDocs
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class NotificationService {
  constructor() {
    this.notifications = [];
    this.unsubscribe = null;
    this.isListening = false;
    this.notificationContainer = null;
    this.soundEnabled = true;
    this.maxNotifications = 50;
    this.initializeService();
  }

  initializeService() {
    console.log('🐺 Initializing Notification Service...');
    this.createNotificationContainer();
    this.setupEventListeners();
  }

  createNotificationContainer() {
    // Create notification container
    this.notificationContainer = document.createElement('div');
    this.notificationContainer.id = 'notification-container';
    this.notificationContainer.className = 'fixed top-4 right-4 z-50 space-y-2 max-w-sm';
    document.body.appendChild(this.notificationContainer);

    // Create notification bell icon
    this.createNotificationBell();
  }

  createNotificationBell() {
    const bellContainer = document.createElement('div');
    bellContainer.className = 'fixed top-4 left-4 z-50';
    bellContainer.innerHTML = `
      <div class="relative">
        <button id="notification-bell" 
                class="neo-brutalist bg-yellow-400 text-black p-3 rounded-full hover:bg-yellow-500 transition-colors">
          <i class="fas fa-bell text-xl"></i>
          <span id="notification-count" 
                class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold hidden">
            0
          </span>
        </button>
        
        <!-- Notification Panel -->
        <div id="notification-panel" 
             class="hidden absolute top-16 left-0 neo-brutalist bg-white border-4 border-black w-80 max-h-96 overflow-y-auto">
          <div class="p-4 border-b-2 border-black bg-gray-100">
            <div class="flex justify-between items-center">
              <h3 class="font-bold">Notifications</h3>
              <div class="flex space-x-2">
                <button onclick="notificationService.markAllAsRead()" 
                        class="text-xs bg-blue-500 text-white px-2 py-1 rounded font-bold">
                  Mark All Read
                </button>
                <button onclick="notificationService.toggleSound()" 
                        class="text-xs bg-gray-500 text-white px-2 py-1 rounded font-bold">
                  <i class="fas fa-volume-${this.soundEnabled ? 'up' : 'mute'}"></i>
                </button>
              </div>
            </div>
          </div>
          <div id="notification-list" class="p-2">
            <div class="text-center py-4 text-gray-500">No notifications</div>
          </div>
        </div>
      </div>
    `;
    document.body.appendChild(bellContainer);
  }

  setupEventListeners() {
    // Bell click handler
    document.addEventListener('click', (e) => {
      if (e.target.closest('#notification-bell')) {
        this.toggleNotificationPanel();
      } else if (!e.target.closest('#notification-panel')) {
        this.hideNotificationPanel();
      }
    });

    // Listen for user login/logout
    authManager.onAuthStateChanged((user) => {
      if (user) {
        this.startListening(user.uid);
      } else {
        this.stopListening();
      }
    });
  }

  startListening(userId) {
    if (this.isListening) return;

    console.log('🐺 Starting notification listener for user:', userId);
    this.isListening = true;

    try {
      const notificationsRef = collection(db, CTF_CONFIG.COLLECTIONS.NOTIFICATIONS);
      const notificationsQuery = query(
        notificationsRef,
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
        limit(this.maxNotifications)
      );

      this.unsubscribe = onSnapshot(notificationsQuery, (snapshot) => {
        console.log('🐺 Notification update received');
        this.handleNotificationUpdate(snapshot);
      }, (error) => {
        console.error('🐺 Notification listener error:', error);
      });

    } catch (error) {
      console.error('🐺 Error starting notification listener:', error);
    }
  }

  stopListening() {
    if (!this.isListening) return;

    console.log('🐺 Stopping notification listener');
    this.isListening = false;

    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }

    this.notifications = [];
    this.updateNotificationDisplay();
  }

  handleNotificationUpdate(snapshot) {
    const newNotifications = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Check for new notifications
    const existingIds = new Set(this.notifications.map(n => n.id));
    const reallyNewNotifications = newNotifications.filter(n => !existingIds.has(n.id));

    // Show toast for new notifications
    reallyNewNotifications.forEach(notification => {
      if (!notification.read) {
        this.showToastNotification(notification);
      }
    });

    this.notifications = newNotifications;
    this.updateNotificationDisplay();
  }

  showToastNotification(notification) {
    const toast = document.createElement('div');
    toast.className = 'neo-brutalist bg-white border-4 border-black p-4 mb-2 animate-slide-in-right';
    
    const typeColors = {
      [CTF_CONFIG.NOTIFICATION_TYPES.SCORE_UPDATE]: 'border-l-4 border-l-green-500',
      [CTF_CONFIG.NOTIFICATION_TYPES.RANK_CHANGE]: 'border-l-4 border-l-blue-500',
      [CTF_CONFIG.NOTIFICATION_TYPES.ACHIEVEMENT]: 'border-l-4 border-l-yellow-500',
      [CTF_CONFIG.NOTIFICATION_TYPES.ADMIN_MESSAGE]: 'border-l-4 border-l-red-500'
    };

    toast.classList.add(typeColors[notification.type] || 'border-l-4 border-l-gray-500');

    toast.innerHTML = `
      <div class="flex justify-between items-start">
        <div class="flex-1">
          <div class="font-bold text-sm">${notification.title}</div>
          <div class="text-xs text-gray-600 mt-1">${notification.message}</div>
          <div class="text-xs text-gray-400 mt-2">
            ${this.formatTimeAgo(notification.timestamp)}
          </div>
        </div>
        <button onclick="this.parentElement.parentElement.remove()" 
                class="text-gray-400 hover:text-gray-600 ml-2">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;

    this.notificationContainer.appendChild(toast);

    // Play sound if enabled
    if (this.soundEnabled) {
      this.playNotificationSound(notification.type);
    }

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (toast.parentElement) {
        toast.classList.add('animate-slide-out-right');
        setTimeout(() => {
          if (toast.parentElement) {
            toast.remove();
          }
        }, 300);
      }
    }, 5000);
  }

  updateNotificationDisplay() {
    const unreadCount = this.notifications.filter(n => !n.read).length;
    const countElement = document.getElementById('notification-count');
    const listElement = document.getElementById('notification-list');

    // Update count badge
    if (countElement) {
      if (unreadCount > 0) {
        countElement.textContent = unreadCount > 99 ? '99+' : unreadCount;
        countElement.classList.remove('hidden');
      } else {
        countElement.classList.add('hidden');
      }
    }

    // Update notification list
    if (listElement) {
      if (this.notifications.length === 0) {
        listElement.innerHTML = '<div class="text-center py-4 text-gray-500">No notifications</div>';
      } else {
        listElement.innerHTML = this.notifications.map(notification => 
          this.renderNotificationItem(notification)
        ).join('');
      }
    }
  }

  renderNotificationItem(notification) {
    const typeIcons = {
      [CTF_CONFIG.NOTIFICATION_TYPES.SCORE_UPDATE]: 'fas fa-trophy text-green-600',
      [CTF_CONFIG.NOTIFICATION_TYPES.RANK_CHANGE]: 'fas fa-chart-line text-blue-600',
      [CTF_CONFIG.NOTIFICATION_TYPES.ACHIEVEMENT]: 'fas fa-medal text-yellow-600',
      [CTF_CONFIG.NOTIFICATION_TYPES.ADMIN_MESSAGE]: 'fas fa-exclamation-circle text-red-600'
    };

    const icon = typeIcons[notification.type] || 'fas fa-bell text-gray-600';
    const readClass = notification.read ? 'opacity-60' : 'bg-blue-50';

    return `
      <div class="p-2 border-b border-gray-200 hover:bg-gray-50 cursor-pointer ${readClass}"
           onclick="notificationService.markAsRead('${notification.id}')">
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0 mt-1">
            <i class="${icon}"></i>
          </div>
          <div class="flex-1 min-w-0">
            <div class="font-bold text-sm">${notification.title}</div>
            <div class="text-xs text-gray-600 mt-1">${notification.message}</div>
            <div class="text-xs text-gray-400 mt-1">
              ${this.formatTimeAgo(notification.timestamp)}
            </div>
          </div>
          ${!notification.read ? '<div class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2"></div>' : ''}
        </div>
      </div>
    `;
  }

  toggleNotificationPanel() {
    const panel = document.getElementById('notification-panel');
    if (panel) {
      panel.classList.toggle('hidden');
    }
  }

  hideNotificationPanel() {
    const panel = document.getElementById('notification-panel');
    if (panel) {
      panel.classList.add('hidden');
    }
  }

  async markAsRead(notificationId) {
    try {
      const notificationRef = doc(db, CTF_CONFIG.COLLECTIONS.NOTIFICATIONS, notificationId);
      await updateDoc(notificationRef, { read: true });
      
      // Update local state
      const notification = this.notifications.find(n => n.id === notificationId);
      if (notification) {
        notification.read = true;
        this.updateNotificationDisplay();
      }
    } catch (error) {
      console.error('🐺 Error marking notification as read:', error);
    }
  }

  async markAllAsRead() {
    try {
      const unreadNotifications = this.notifications.filter(n => !n.read);
      
      for (const notification of unreadNotifications) {
        const notificationRef = doc(db, CTF_CONFIG.COLLECTIONS.NOTIFICATIONS, notification.id);
        await updateDoc(notificationRef, { read: true });
        notification.read = true;
      }
      
      this.updateNotificationDisplay();
    } catch (error) {
      console.error('🐺 Error marking all notifications as read:', error);
    }
  }

  toggleSound() {
    this.soundEnabled = !this.soundEnabled;
    const soundButton = document.querySelector('#notification-panel .fa-volume-up, #notification-panel .fa-volume-mute');
    if (soundButton) {
      soundButton.className = `fas fa-volume-${this.soundEnabled ? 'up' : 'mute'}`;
    }
    
    // Save preference
    localStorage.setItem('ctf_notification_sound', this.soundEnabled.toString());
  }

  playNotificationSound(type) {
    // Create audio context for different notification sounds
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    
    const frequencies = {
      [CTF_CONFIG.NOTIFICATION_TYPES.SCORE_UPDATE]: [523, 659, 784], // C-E-G chord
      [CTF_CONFIG.NOTIFICATION_TYPES.RANK_CHANGE]: [440, 554, 659], // A-C#-E chord
      [CTF_CONFIG.NOTIFICATION_TYPES.ACHIEVEMENT]: [659, 784, 988], // E-G-B chord
      [CTF_CONFIG.NOTIFICATION_TYPES.ADMIN_MESSAGE]: [349, 415, 523] // F-G#-C chord
    };

    const freqs = frequencies[type] || [440, 554, 659];
    
    freqs.forEach((freq, index) => {
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3);
      
      oscillator.start(audioContext.currentTime + index * 0.1);
      oscillator.stop(audioContext.currentTime + 0.3 + index * 0.1);
    });
  }

  formatTimeAgo(timestamp) {
    if (!timestamp) return 'Unknown time';
    
    const now = new Date();
    const time = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return time.toLocaleDateString();
  }

  // Public API for sending notifications (used by other services)
  async sendNotification(userId, notification) {
    // This is handled by the score service queue system
    console.log('🐺 Notification queued for user:', userId, notification);
  }

  // Initialize sound preference
  initializeSoundPreference() {
    const savedPreference = localStorage.getItem('ctf_notification_sound');
    if (savedPreference !== null) {
      this.soundEnabled = savedPreference === 'true';
    }
  }
}

// Initialize notification service
const notificationService = new NotificationService();

// Make it globally available
window.notificationService = notificationService;

export default notificationService;
