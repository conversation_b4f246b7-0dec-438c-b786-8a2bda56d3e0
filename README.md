# 🐺 The Wolf Challenge - CTF Platform

A comprehensive Capture The Flag (CTF) web application built with modern web technologies and Firebase backend.

**Developed by <PERSON><PERSON>**

## 🎯 Features

### Core Platform
- **Firebase Authentication**: Secure login-only access
- **Real-time Database**: Firestore for live data synchronization
- **Role-based Access**: Participants and Administrators
- **Security System**: CSRF protection, rate limiting, session management

### Challenge System
- **Three Difficulty Levels**:
  - Beginner: 10 challenges × 10 points = 100 points
  - Intermediate: 20 challenges × 10 points = 200 points
  - Advanced: 40 challenges × 10 points = 400 points
- **Total Possible Score**: 700 points
- **One-time Access Rule**: No reattempts on solved challenges
- **Multiple Challenge Types**: HTML/Client-side, Cookie manipulation, Database attacks, Multi-step challenges

### Real-time Features
- **Live Leaderboard**: Real-time ranking updates
- **Progress Tracking**: Individual and category-wise progress
- **Activity Monitoring**: Last activity timestamps
- **Security Monitoring**: Real-time violation detection

### Admin Panel
- **Challenge Management**: Create, edit, delete challenges
- **User Management**: Monitor participant progress
- **Analytics Dashboard**: Platform statistics and insights
- **Security Logs**: Monitor security events and violations

## 🚀 Quick Start

### Prerequisites
- Modern web browser with JavaScript enabled
- Firebase project with Firestore enabled
- Web server (for local development)

### Installation

1. **Clone/Download** the project files
2. **Configure Firebase** in `firebase-config.js` with your project credentials
3. **Set up Firestore** with appropriate security rules
4. **Deploy** to a web server or run locally

### Firebase Configuration

Update `firebase-config.js` with your Firebase project settings:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.firebasestorage.app",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};
```

### Firestore Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Challenges are readable by authenticated users
    match /challenges/{challengeId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Submissions are user-specific
    match /submissions/{submissionId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
  }
}
```

## 🏗️ Architecture

### File Structure
```
├── index.html              # Main application page
├── style.css              # CTF platform styles
├── script.js              # Main application controller
├── firebase-config.js     # Firebase configuration
├── auth.js                # Authentication system
├── challenges.js          # Challenge management
├── dashboard.js           # User dashboard
├── leaderboard.js         # Real-time leaderboard
├── admin.js               # Admin panel
├── security.js            # Security and access control
├── challenges/            # Challenge files directory
└── README.md              # This file
```

### Key Components

1. **Authentication Manager** (`auth.js`)
   - Firebase Authentication integration
   - User session management
   - Role-based access control

2. **Challenge Manager** (`challenges.js`)
   - Challenge loading and rendering
   - Flag submission and validation
   - Progress tracking

3. **Security Manager** (`security.js`)
   - CSRF protection
   - Rate limiting
   - Session security
   - Attack detection

4. **Dashboard Manager** (`dashboard.js`)
   - User statistics
   - Progress visualization
   - Tab navigation

5. **Leaderboard Manager** (`leaderboard.js`)
   - Real-time ranking updates
   - Live activity monitoring
   - Performance statistics

6. **Admin Manager** (`admin.js`)
   - Challenge creation/management
   - User administration
   - Platform analytics

## 🔒 Security Features

### Authentication & Authorization
- Firebase Authentication for secure login
- Role-based access control (Participant/Admin)
- Session timeout and management
- CSRF token protection

### Rate Limiting
- 5 attempts per minute per challenge
- 5-minute cooldown on rate limit exceeded
- Automatic cleanup of expired limits

### Attack Prevention
- XSS protection and input sanitization
- SQL injection pattern detection
- Automated submission detection
- Suspicious activity monitoring

### Data Protection
- One-time challenge access enforcement
- Secure flag validation
- Activity logging and monitoring
- Real-time security event tracking

## 🎮 Usage

### For Participants
1. **Login** with your credentials
2. **Browse Challenges** by difficulty level
3. **Solve Challenges** and submit flags
4. **Track Progress** on your dashboard
5. **Compete** on the real-time leaderboard

### For Administrators
1. **Access Admin Panel** (admin role required)
2. **Create Challenges** with the challenge editor
3. **Monitor Users** and their progress
4. **View Analytics** and platform statistics
5. **Manage Security** events and violations

## 🛠️ Development

### Debug Tools
In development mode, debug tools are available at `window.ctfDebug`:

```javascript
// Check application status
ctfDebug.getStatus()

// Access managers
ctfDebug.auth
ctfDebug.challenges
ctfDebug.security

// Emergency functions
ctfDebug.emergencyReset()
ctfDebug.emergencyLogout()

// Security monitoring
ctfDebug.getSecurityEvents()
ctfDebug.getRateLimitStatus('challenge-id')
```

### Adding Challenges
1. Use the admin panel to create challenges
2. Or manually add to Firestore with this structure:

```javascript
{
  title: "Challenge Title",
  description: "Challenge description",
  category: "beginner|intermediate|advanced",
  type: "HTML/Client-side|Cookie manipulation|Database attacks",
  difficulty: "Easy|Medium|Hard",
  points: 10,
  order: 1,
  flag: "wolf{flag_value}",
  instructions: "Detailed instructions",
  content: "Challenge content/HTML",
  hints: ["Hint 1", "Hint 2"]
}
```

## 📊 Scoring System

- **Beginner Level**: 10 challenges × 10 points each = 100 points maximum
- **Intermediate Level**: 20 challenges × 10 points each = 200 points maximum
- **Advanced Level**: 40 challenges × 10 points each = 400 points maximum
- **Total Maximum Score**: 700 points

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is developed for educational purposes. Please respect the terms of use and don't use for malicious activities.

## 🐺 About

**The Wolf Challenge** is a comprehensive CTF platform designed to provide a realistic and engaging cybersecurity learning experience. Built with modern web technologies and security best practices.

**Developer**: S.Tamilselvan

---

*Happy hunting! 🎯*
