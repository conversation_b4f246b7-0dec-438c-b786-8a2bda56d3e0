// Enhanced Score Service for The Wolf Challenge CTF Platform
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import scoreValidationService from './score-validation-service.js';
import {
  collection,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  increment,
  writeBatch,
  getDocs
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class ScoreService {
  constructor() {
    this.scoreListeners = new Map();
    this.leaderboardListeners = new Set();
    this.notificationListeners = new Set();
    this.scoreCache = new Map();
    this.eventQueue = [];
    this.isProcessingQueue = false;
    this.initializeService();
  }

  initializeService() {
    console.log('🐺 Initializing Enhanced Score Service...');
    
    // Set up periodic cache cleanup
    setInterval(() => {
      this.cleanupCache();
    }, 5 * 60 * 1000); // Every 5 minutes

    // Process event queue periodically
    setInterval(() => {
      this.processEventQueue();
    }, 1000); // Every second
  }

  // Real-time score tracking for a user
  subscribeToUserScore(userId, callback) {
    if (this.scoreListeners.has(userId)) {
      this.scoreListeners.get(userId).push(callback);
      return;
    }

    const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);
    const unsubscribe = onSnapshot(userRef, (doc) => {
      if (doc.exists()) {
        const userData = doc.data();
        const scoreData = {
          score: userData.score || 0,
          challengesSolved: userData.challengesSolved || 0,
          progress: userData.progress || {},
          lastActivity: userData.lastActivity,
          rank: userData.rank || null
        };

        // Update cache
        this.scoreCache.set(userId, scoreData);

        // Notify all listeners
        const listeners = this.scoreListeners.get(userId) || [];
        listeners.forEach(cb => cb(scoreData));
      }
    }, (error) => {
      console.error('🐺 Error in score subscription:', error);
    });

    this.scoreListeners.set(userId, [callback]);
    return unsubscribe;
  }

  // Unsubscribe from user score updates
  unsubscribeFromUserScore(userId) {
    if (this.scoreListeners.has(userId)) {
      this.scoreListeners.delete(userId);
      this.scoreCache.delete(userId);
    }
  }

  // Award points for challenge completion
  async awardChallengePoints(userId, challengeId, points, challengeData = {}) {
    try {
      console.log('🐺 Awarding challenge points:', { userId, challengeId, points });

      // Validate inputs
      if (!userId || !challengeId || !points || points <= 0) {
        throw new Error('Invalid parameters for awarding points');
      }

      // Enhanced security validation
      const validationResult = await scoreValidationService.validateScoreUpdate(
        userId,
        challengeId,
        points,
        {
          ...challengeData,
          solveTime: challengeData.solveTime,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        }
      );

      if (!validationResult.valid) {
        console.warn('🐺 Score validation failed:', validationResult.reasons);

        // For high-risk violations, throw error immediately
        if (validationResult.securityLevel === 'CRITICAL' || validationResult.securityLevel === 'HIGH') {
          throw new Error(`Security validation failed: ${validationResult.reasons.join(', ')}`);
        }

        // For medium/low risk, log but allow with warning
        console.warn('🐺 Proceeding with caution due to validation warnings');
      }

      // Check if challenge already solved
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();
      const solvedChallenges = userData.solvedChallenges || [];
      
      if (solvedChallenges.includes(challengeId)) {
        throw new Error('Challenge already solved');
      }

      // Create batch operation for atomic updates
      const batch = writeBatch(db);

      // Update user score and progress
      const newScore = (userData.score || 0) + points;
      const newChallengesSolved = (userData.challengesSolved || 0) + 1;
      const updatedSolvedChallenges = [...solvedChallenges, challengeId];

      // Update category progress
      const progress = userData.progress || this.createDefaultProgress();
      const category = challengeData.category || 'beginner';
      if (progress[category]) {
        progress[category].solved = Math.min(
          progress[category].solved + 1,
          progress[category].total
        );
      }

      // Update user document
      batch.update(userRef, {
        score: newScore,
        challengesSolved: newChallengesSolved,
        solvedChallenges: updatedSolvedChallenges,
        progress: progress,
        lastActivity: serverTimestamp()
      });

      // Record score event
      const scoreEventRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.SCORE_EVENTS));
      batch.set(scoreEventRef, {
        userId: userId,
        type: CTF_CONFIG.SCORE_EVENTS.CHALLENGE_SOLVED,
        challengeId: challengeId,
        pointsAwarded: points,
        previousScore: userData.score || 0,
        newScore: newScore,
        timestamp: serverTimestamp(),
        metadata: {
          challengeTitle: challengeData.title || 'Unknown',
          category: category,
          difficulty: challengeData.difficulty || 'Unknown'
        }
      });

      // Record submission
      const submissionRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS));
      batch.set(submissionRef, {
        userId: userId,
        challengeId: challengeId,
        flag: challengeData.flag || '',
        timestamp: serverTimestamp(),
        points: points,
        isCorrect: true
      });

      // Commit batch
      await batch.commit();

      // Queue notification
      this.queueNotification(userId, {
        type: CTF_CONFIG.NOTIFICATION_TYPES.SCORE_UPDATE,
        title: 'Challenge Solved!',
        message: `You earned ${points} points for solving "${challengeData.title || 'a challenge'}"`,
        data: {
          challengeId,
          pointsAwarded: points,
          newScore: newScore
        }
      });

      // Update leaderboard rankings
      await this.updateLeaderboardRankings();

      console.log('🐺 Challenge points awarded successfully');
      return {
        success: true,
        newScore: newScore,
        pointsAwarded: points,
        challengesSolved: newChallengesSolved
      };

    } catch (error) {
      console.error('🐺 Error awarding challenge points:', error);
      throw error;
    }
  }

  // Manual score adjustment (admin only)
  async adjustScore(userId, pointsChange, reason, adminId) {
    try {
      // Verify admin permissions
      if (!authManager.isAdmin()) {
        throw new Error('Insufficient permissions for score adjustment');
      }

      console.log('🐺 Manual score adjustment:', { userId, pointsChange, reason });

      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();
      const previousScore = userData.score || 0;
      const newScore = Math.max(0, previousScore + pointsChange); // Prevent negative scores

      // Create batch operation
      const batch = writeBatch(db);

      // Update user score
      batch.update(userRef, {
        score: newScore,
        lastActivity: serverTimestamp()
      });

      // Record score event
      const scoreEventRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.SCORE_EVENTS));
      batch.set(scoreEventRef, {
        userId: userId,
        type: CTF_CONFIG.SCORE_EVENTS.MANUAL_ADJUSTMENT,
        pointsAwarded: pointsChange,
        previousScore: previousScore,
        newScore: newScore,
        timestamp: serverTimestamp(),
        adminId: adminId,
        reason: reason,
        metadata: {
          adjustmentType: pointsChange > 0 ? 'bonus' : 'penalty'
        }
      });

      // Record audit log
      const auditLogRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.AUDIT_LOGS));
      batch.set(auditLogRef, {
        action: 'SCORE_ADJUSTMENT',
        adminId: adminId,
        targetUserId: userId,
        details: {
          previousScore,
          newScore,
          pointsChange,
          reason
        },
        timestamp: serverTimestamp()
      });

      await batch.commit();

      // Queue notification
      this.queueNotification(userId, {
        type: CTF_CONFIG.NOTIFICATION_TYPES.SCORE_UPDATE,
        title: pointsChange > 0 ? 'Bonus Points!' : 'Score Adjustment',
        message: `Your score has been ${pointsChange > 0 ? 'increased' : 'decreased'} by ${Math.abs(pointsChange)} points. Reason: ${reason}`,
        data: {
          pointsChange,
          newScore,
          reason
        }
      });

      // Update leaderboard rankings
      await this.updateLeaderboardRankings();

      return {
        success: true,
        previousScore,
        newScore,
        pointsChange
      };

    } catch (error) {
      console.error('🐺 Error adjusting score:', error);
      throw error;
    }
  }

  // Get user's current score and stats
  async getUserScore(userId) {
    try {
      // Check cache first
      if (this.scoreCache.has(userId)) {
        return this.scoreCache.get(userId);
      }

      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        return null;
      }

      const userData = userDoc.data();
      const scoreData = {
        score: userData.score || 0,
        challengesSolved: userData.challengesSolved || 0,
        progress: userData.progress || {},
        lastActivity: userData.lastActivity,
        rank: userData.rank || null,
        solvedChallenges: userData.solvedChallenges || []
      };

      // Update cache
      this.scoreCache.set(userId, scoreData);
      return scoreData;

    } catch (error) {
      console.error('🐺 Error getting user score:', error);
      return null;
    }
  }

  // Get score history for a user
  async getScoreHistory(userId, limit = 50) {
    try {
      const eventsRef = collection(db, CTF_CONFIG.COLLECTIONS.SCORE_EVENTS);
      const eventsQuery = query(
        eventsRef,
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
        limit(limit)
      );

      const eventsSnap = await getDocs(eventsQuery);
      return eventsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

    } catch (error) {
      console.error('🐺 Error getting score history:', error);
      return [];
    }
  }

  // Helper methods
  createDefaultProgress() {
    return {
      beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
      intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
      advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
    };
  }

  queueNotification(userId, notification) {
    this.eventQueue.push({
      type: 'notification',
      userId,
      notification,
      timestamp: Date.now()
    });
  }

  async processEventQueue() {
    if (this.isProcessingQueue || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    const events = this.eventQueue.splice(0, 10); // Process up to 10 events at once

    try {
      for (const event of events) {
        if (event.type === 'notification') {
          await this.sendNotification(event.userId, event.notification);
        }
      }
    } catch (error) {
      console.error('🐺 Error processing event queue:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  async sendNotification(userId, notification) {
    try {
      const notificationRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.NOTIFICATIONS));
      await setDoc(notificationRef, {
        userId: userId,
        ...notification,
        timestamp: serverTimestamp(),
        read: false
      });
    } catch (error) {
      console.error('🐺 Error sending notification:', error);
    }
  }

  cleanupCache() {
    const maxAge = 10 * 60 * 1000; // 10 minutes
    const now = Date.now();
    
    for (const [userId, data] of this.scoreCache.entries()) {
      if (data.lastAccess && (now - data.lastAccess) > maxAge) {
        this.scoreCache.delete(userId);
      }
    }
  }

  // Update leaderboard rankings (called after score changes)
  async updateLeaderboardRankings() {
    try {
      console.log('🐺 Updating leaderboard rankings...');

      // Import leaderboard manager dynamically to avoid circular dependency
      const { default: leaderboardManager } = await import('./leaderboard.js');

      // Trigger ranking update
      if (leaderboardManager && leaderboardManager.updateRankings) {
        await leaderboardManager.updateRankings();
      }

      console.log('🐺 Leaderboard rankings updated successfully');
    } catch (error) {
      console.error('🐺 Error updating leaderboard rankings:', error);
    }
  }
}

// Initialize and export score service
const scoreService = new ScoreService();
export default scoreService;
