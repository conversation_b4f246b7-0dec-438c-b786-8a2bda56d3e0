# 🐺 The Wolf Challenge CTF - Firestore Security Rules Documentation

## Overview

This document outlines the comprehensive security rules for The Wolf Challenge CTF platform's Firestore database. These rules ensure data integrity, user privacy, and proper access control while enabling the platform's advanced features.

## 🔐 Security Principles

### Authentication Requirements
- **All operations require authentication** except for specific public endpoints
- **Role-based access control** with `admin` and `participant` roles
- **User ownership validation** for personal data access
- **Timestamp validation** for audit trails

### Data Integrity
- **Input validation** for all critical fields (scores, emails, etc.)
- **Immutable audit logs** to prevent tampering
- **Score limits** to prevent unrealistic values
- **Rate limiting** support for abuse prevention

## 📊 Collection Rules

### 1. Users Collection (`/users/{userId}`)

**Read Access:**
- ✅ All authenticated users (for leaderboard functionality)

**Write Access:**
- ✅ Users can create their own profile during registration
- ✅ Users can update limited fields (lastActivity, progress)
- ✅ Admins can update any user (scores, bans, role changes)
- ✅ Only admins can delete users

**Validation:**
- Email format validation
- Score range: 0-10,000 points
- Challenges solved range: 0-100
- Role restrictions during creation

```javascript
// Example valid user creation
{
  "email": "<EMAIL>",
  "role": "participant",
  "score": 0,
  "challengesSolved": 0,
  "progress": {...},
  "lastActivity": serverTimestamp()
}
```

### 2. Challenges Collection (`/challenges/{challengeId}`)

**Read Access:**
- ✅ All authenticated users

**Write Access:**
- ✅ Only admins can create, update, or delete challenges

**Use Case:**
- Challenge definitions and metadata
- Points values and categories
- Challenge availability status

### 3. Submissions Collection (`/submissions/{submissionId}`)

**Read Access:**
- ✅ Users can read their own submissions
- ✅ Admins can read all submissions

**Write Access:**
- ✅ Users can create submissions for themselves
- ✅ Only admins can update or delete submissions

**Validation:**
- Timestamp must be server timestamp
- Points must be non-negative number
- User ownership validation

### 4. Score Events Collection (`/score_events/{eventId}`)

**Read Access:**
- ✅ Users can read their own score events
- ✅ Admins can read all score events

**Write Access:**
- ✅ Only admins/system can create score events
- ✅ Only admins can update or delete score events

**Purpose:**
- Audit trail for all score changes
- Challenge completion tracking
- Manual score adjustments logging

### 5. Notifications Collection (`/notifications/{notificationId}`)

**Read Access:**
- ✅ Users can read their own notifications
- ✅ Admins can read all notifications

**Write Access:**
- ✅ Users can mark their own notifications as read
- ✅ Admins can create notifications
- ✅ Only admins can delete notifications

**Features:**
- Real-time notification delivery
- Read status tracking
- Multiple notification types

### 6. Push Subscriptions Collection (`/push_subscriptions/{subscriptionId}`)

**Read Access:**
- ✅ Users can read their own subscriptions
- ✅ Admins can read all subscriptions

**Write Access:**
- ✅ Users can create their own subscriptions
- ✅ Users can deactivate their subscriptions
- ✅ Admins have full control

**Security:**
- Subscription endpoint validation
- User ownership enforcement
- Active status management

### 7. Audit Logs Collection (`/audit_logs/{logId}`)

**Read Access:**
- ✅ Only admins can read audit logs

**Write Access:**
- ✅ Only admins/system can create audit logs
- ✅ Only admins can delete audit logs
- ❌ **No updates allowed** (immutable for integrity)

**Purpose:**
- Security event tracking
- Admin action logging
- Compliance and forensics

### 8. Security Collections

#### Rate Limits (`/rate_limits/{userId}`)
- User-specific rate limiting data
- Abuse prevention tracking
- Submission frequency monitoring

#### Security Events (`/security_events/{eventId}`)
- Suspicious activity detection
- Failed validation attempts
- Security threat assessment

#### Analytics (`/analytics/{analyticsId}`)
- Platform usage statistics
- Performance metrics
- Admin-only access

## 🚀 Web Push Notification Configuration

### VAPID Configuration
```javascript
// Your VAPID public key
const vapidPublicKey = 'qEjDhUv4P-YpgaZoSNyUMy4bpdzZNlae050QYYTq07o';
```

### Service Worker Setup
The platform includes a comprehensive service worker (`sw.js`) that handles:
- Push notification reception
- Offline functionality
- Cache management
- Background sync

### Notification Types
1. **Score Updates** - Challenge completion notifications
2. **Rank Changes** - Leaderboard position updates
3. **Achievements** - Special accomplishment alerts
4. **Admin Messages** - Important announcements

## 🛡️ Security Features

### Anti-Cheat Measures
- **Rate limiting** on submissions
- **Timing analysis** for automation detection
- **Score progression validation**
- **Suspicious pattern detection**

### Data Protection
- **User data isolation** - users can only access their own data
- **Admin privilege separation** - clear admin vs user permissions
- **Audit trail completeness** - all actions are logged
- **Input validation** - all data is validated before storage

### Access Control Matrix

| Collection | User Read | User Write | Admin Read | Admin Write |
|------------|-----------|------------|------------|-------------|
| users | ✅ All | ✅ Own (limited) | ✅ All | ✅ All |
| challenges | ✅ All | ❌ None | ✅ All | ✅ All |
| submissions | ✅ Own | ✅ Create own | ✅ All | ✅ All |
| score_events | ✅ Own | ❌ None | ✅ All | ✅ All |
| notifications | ✅ Own | ✅ Mark read | ✅ All | ✅ All |
| push_subscriptions | ✅ Own | ✅ Own | ✅ All | ✅ All |
| audit_logs | ❌ None | ❌ None | ✅ All | ✅ Create/Delete |
| security_events | ❌ None | ❌ None | ✅ All | ✅ Create/Delete |

## 📋 Deployment Instructions

### 1. Deploy Firestore Rules
```bash
# Using Firebase CLI
firebase deploy --only firestore:rules

# Or copy the rules to Firebase Console
# Go to Firestore > Rules tab and paste the content of firestore.rules
```

### 2. Configure Push Notifications
```bash
# Set up VAPID keys in Firebase Console
# Go to Project Settings > Cloud Messaging
# Add your VAPID key: qEjDhUv4P-YpgaZoSNyUMy4bpdzZNlae050QYYTq07o
```

### 3. Initialize Collections
The rules support these collections:
- `users` - User profiles and scores
- `challenges` - Challenge definitions
- `submissions` - User submissions
- `score_events` - Score change audit trail
- `notifications` - Real-time notifications
- `push_subscriptions` - Web push subscriptions
- `audit_logs` - Security and admin audit logs
- `security_events` - Security monitoring
- `rate_limits` - Rate limiting data
- `analytics` - Platform analytics
- `system_config` - System configuration
- `competition_settings` - Competition parameters

### 4. Admin User Setup
Create the first admin user:
```javascript
// In Firebase Console or via admin SDK
{
  "email": "<EMAIL>",
  "role": "admin",
  "score": 0,
  "challengesSolved": 0,
  "createdAt": serverTimestamp()
}
```

## 🔍 Testing the Rules

### Test Cases
1. **User Registration** - Verify users can create accounts
2. **Score Updates** - Test score validation and limits
3. **Admin Functions** - Verify admin-only operations
4. **Data Isolation** - Ensure users can't access others' data
5. **Push Notifications** - Test subscription and messaging
6. **Security Events** - Verify audit logging works

### Common Issues
- **Permission Denied**: Check user authentication and role
- **Invalid Data**: Verify input validation rules
- **Missing Fields**: Ensure required fields are present
- **Timestamp Issues**: Use `serverTimestamp()` for time fields

## 📞 Support

For issues with the rules or security configuration:
1. Check the Firebase Console logs
2. Verify user authentication status
3. Confirm user roles are set correctly
4. Test with Firebase Rules Playground
5. Review audit logs for security events

The rules are designed to be secure by default while enabling all the advanced features of The Wolf Challenge CTF platform including live leaderboards, real-time notifications, and comprehensive admin functionality.
