# 🐺 The Wolf Challenge CTF - Complete Deployment Guide

## 🚀 Quick Start Deployment

Your CTF platform is now ready for deployment! Here's everything you need to get it running with live leaderboards, push notifications, and full admin functionality.

### 📋 Prerequisites

1. **Firebase Project**: `wolf-ctf` (already configured)
2. **Service Account**: `wolf-ctf-8e7993c7b98c.json` (already available)
3. **VAPID Key**: `qEjDhUv4P-YpgaZoSNyUMy4bpdzZNlae050QYYTq07o` (already configured)
4. **Firebase CLI**: Install with `npm install -g firebase-tools`

## 🔥 Firebase Configuration

### Step 1: Deploy Firestore Security Rules

```bash
# Login to Firebase
firebase login

# Initialize project (if not already done)
firebase init firestore

# Deploy the security rules
firebase deploy --only firestore:rules
```

**Or manually in Firebase Console:**
1. Go to [Firebase Console](https://console.firebase.google.com/project/wolf-ctf)
2. Navigate to Firestore Database > Rules
3. Copy and paste the content from `firestore.rules`
4. Click "Publish"

### Step 2: Configure Push Notifications

1. Go to Firebase Console > Project Settings
2. Navigate to "Cloud Messaging" tab
3. In "Web configuration" section:
   - Add your domain to authorized domains
   - Set VAPID key: `qEjDhUv4P-YpgaZoSNyUMy4bpdzZNlae050QYYTq07o`

### Step 3: Create Required Firestore Indexes

Go to Firestore > Indexes and create these composite indexes:

```javascript
// Collection: users
// Fields: role (Ascending), score (Descending), lastActivity (Descending)

// Collection: submissions  
// Fields: userId (Ascending), timestamp (Descending)

// Collection: score_events
// Fields: userId (Ascending), timestamp (Descending)

// Collection: notifications
// Fields: userId (Ascending), timestamp (Descending), read (Ascending)

// Collection: audit_logs
// Fields: timestamp (Descending), eventType (Ascending)
```

### Step 4: Initialize System Data

Run the deployment script to set up initial data:

```bash
node firebase-deploy.js
```

This will create:
- ✅ System configuration
- ✅ Competition settings  
- ✅ Sample challenges
- ✅ First admin user
- ✅ Required collections

## 🌐 Web Server Deployment

### Option 1: Firebase Hosting (Recommended)

```bash
# Initialize hosting
firebase init hosting

# Deploy to Firebase Hosting
firebase deploy --only hosting
```

### Option 2: Static Web Server

Deploy all files to any web server that supports HTTPS:
- `index.html`
- `style.css`
- `script.js`
- All JavaScript modules (`*.js`)
- `sw.js` (Service Worker)
- `manifest.json` (if created)

**Important**: HTTPS is required for:
- Web Push Notifications
- Service Worker functionality
- Firebase Authentication

## 🔐 Security Setup

### Admin User Creation

The deployment script creates an admin user:
- **Email**: `<EMAIL>`
- **Password**: `TempPassword123!` (change immediately)

### First Login Steps

1. Open your deployed CTF platform
2. Login with admin credentials
3. **Immediately change the password**
4. Test all admin functions:
   - User management
   - Push notifications
   - Score adjustments
   - System settings

### Security Checklist

- ✅ Firestore rules deployed
- ✅ HTTPS enabled
- ✅ Admin password changed
- ✅ VAPID keys configured
- ✅ Service Worker registered
- ✅ Push notifications working

## 📱 Push Notification Testing

### Test Push Notifications

1. Login as admin
2. Go to Admin Panel > Settings
3. Enter notification details:
   - **Title**: "Welcome to The Wolf Challenge!"
   - **Message**: "Test notification working perfectly!"
   - **Type**: Admin Message
4. Click "TEST NOTIFICATION" first
5. Then click "SEND TO ALL USERS"

### Troubleshooting Push Notifications

**If notifications don't work:**

1. **Check HTTPS**: Push notifications require HTTPS
2. **Verify VAPID Key**: Ensure it matches in Firebase Console
3. **Browser Permissions**: Check if notifications are allowed
4. **Service Worker**: Verify `sw.js` is accessible
5. **Console Errors**: Check browser developer tools

## 🏆 Live Leaderboard Features

### Real-time Updates

The leaderboard automatically updates when:
- ✅ Users solve challenges
- ✅ Admins adjust scores
- ✅ Rankings change
- ✅ New users join

### Advanced Features

- **Filtering**: By category, timeframe, activity
- **Sorting**: Score, challenges solved, last activity
- **Pagination**: Smooth navigation through large datasets
- **Live Indicators**: Shows real-time update status
- **Rank Notifications**: Users get notified of significant rank changes

## 🛠️ Admin Panel Features

### User Management
- View all registered users
- Adjust individual user scores
- Reset user progress
- Ban/unban users
- Export user data

### Push Notification Management
- Broadcast to all users
- Send targeted notifications
- View subscription statistics
- Export notification data

### System Administration
- Complete leaderboard reset
- Data backup and export
- Cache management
- Security monitoring
- Analytics dashboard

## 📊 Analytics & Monitoring

### Built-in Analytics
- User engagement metrics
- Score distribution analysis
- Challenge solve rates
- Activity timelines
- Category performance

### Security Monitoring
- Failed login attempts
- Suspicious score patterns
- Rate limit violations
- Audit trail of all actions

## 🔧 Maintenance

### Regular Tasks

1. **Monitor Security Events**
   - Check audit logs weekly
   - Review suspicious activities
   - Update security rules if needed

2. **Backup Data**
   - Export user data monthly
   - Backup challenge configurations
   - Save system settings

3. **Update Challenges**
   - Add new challenges regularly
   - Update point values
   - Refresh challenge content

### Performance Optimization

1. **Database Cleanup**
   - Archive old submissions
   - Clean up expired notifications
   - Remove inactive push subscriptions

2. **Index Optimization**
   - Monitor query performance
   - Add indexes for slow queries
   - Remove unused indexes

## 🆘 Troubleshooting

### Common Issues

**"Service Unavailable" Error:**
- Check internet connection
- Verify Firebase project is active
- Ensure Firestore rules are deployed
- Check browser console for specific errors

**Push Notifications Not Working:**
- Verify HTTPS is enabled
- Check VAPID key configuration
- Ensure service worker is registered
- Test browser notification permissions

**Leaderboard Not Updating:**
- Check Firestore connection
- Verify security rules allow reads
- Test with browser developer tools
- Check for JavaScript errors

**Admin Functions Not Working:**
- Verify admin user role in database
- Check Firestore security rules
- Ensure proper authentication
- Test with different browser

### Getting Help

1. **Check Browser Console**: Look for JavaScript errors
2. **Firebase Console**: Check for service issues
3. **Network Tab**: Verify API calls are working
4. **Firestore Rules**: Test with Rules Playground

## 🎉 Success Checklist

Your deployment is successful when:

- ✅ Users can register and login
- ✅ Challenges are accessible and functional
- ✅ Leaderboard updates in real-time
- ✅ Push notifications work
- ✅ Admin panel is fully functional
- ✅ Security rules are enforced
- ✅ Analytics are collecting data
- ✅ All features work on mobile devices

## 📞 Support

For technical support or questions:
1. Check the `rules.md` file for detailed documentation
2. Review Firebase Console logs
3. Test with Firebase Rules Playground
4. Check browser developer tools

---

**🐺 The Wolf Challenge CTF Platform is now ready for production!**

Your platform includes:
- ✅ Live leaderboard with real-time updates
- ✅ Web push notifications for all users
- ✅ Comprehensive admin panel
- ✅ Advanced security and validation
- ✅ Analytics and monitoring
- ✅ Mobile-responsive design
- ✅ Offline functionality via Service Worker

**Happy hunting! 🎯**
