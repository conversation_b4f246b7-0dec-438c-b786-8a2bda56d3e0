<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Denied - The Wolf Challenge</title>
    <style>
        * {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
        }
        
        body {
            font-family: 'Roboto Mono', monospace;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ff4444;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            border: 4px solid #ff4444;
            box-shadow: 0 0 20px rgba(255, 68, 68, 0.3);
            background: rgba(0, 0, 0, 0.8);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.3); }
            50% { box-shadow: 0 0 30px rgba(255, 68, 68, 0.6); }
            100% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.3); }
        }
        
        .error-code {
            font-size: 120px;
            font-weight: bold;
            margin: 0;
            text-shadow: 0 0 10px #ff4444;
        }
        
        .error-title {
            font-size: 32px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .error-message {
            font-size: 18px;
            margin: 20px 0;
            line-height: 1.6;
        }
        
        .warning {
            background: rgba(255, 68, 68, 0.1);
            border: 2px solid #ff4444;
            padding: 20px;
            margin: 20px 0;
            font-size: 14px;
        }
        
        .back-button {
            display: inline-block;
            background: #ff4444;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            font-weight: bold;
            border: 2px solid #ff4444;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background: transparent;
            color: #ff4444;
        }
        
        .skull {
            font-size: 60px;
            margin: 20px 0;
            animation: shake 1s infinite;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="skull">💀</div>
        <h1 class="error-code">404</h1>
        <h2 class="error-title">ACCESS DENIED</h2>
        <p class="error-message">
            Unauthorized access attempt detected.<br>
            This incident has been logged and reported.
        </p>
        
        <div class="warning">
            <strong>⚠️ SECURITY ALERT</strong><br>
            Attempting to bypass security measures is prohibited.<br>
            All activities are monitored and logged.
        </div>
        
        <p class="error-message">
            If you believe this is an error, please contact the administrator.
        </p>
        
        <a href="index.html" class="back-button">RETURN TO LOGIN</a>
    </div>

    <script>
        // Disable all security bypass attempts
        document.addEventListener('contextmenu', e => e.preventDefault());
        document.addEventListener('keydown', function(e) {
            if (e.keyCode === 123 || 
                (e.ctrlKey && e.shiftKey && e.keyCode === 73) ||
                (e.ctrlKey && e.keyCode === 85) ||
                (e.ctrlKey && e.keyCode === 83)) {
                e.preventDefault();
                return false;
            }
        });
        
        // Log the unauthorized access attempt
        console.log('%c🚫 UNAUTHORIZED ACCESS ATTEMPT DETECTED', 'color: red; font-size: 20px; font-weight: bold;');
        console.log('%cThis incident has been logged.', 'color: orange; font-size: 14px;');
        
        // Clear console periodically
        setInterval(() => {
            console.clear();
            console.log('%c🚫 ACCESS DENIED', 'color: red; font-size: 20px; font-weight: bold;');
        }, 1000);
    </script>
</body>
</html>
