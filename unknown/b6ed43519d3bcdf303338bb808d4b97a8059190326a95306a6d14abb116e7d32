// Web Push Notification Service for The Wolf Challenge CTF Platform
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import { 
  collection, 
  doc, 
  setDoc, 
  updateDoc,
  getDocs, 
  query, 
  where,
  serverTimestamp,
  writeBatch
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class PushNotificationService {
  constructor() {
    this.swRegistration = null;
    this.isSupported = 'serviceWorker' in navigator && 'PushManager' in window;
    this.vapidPublicKey = 'qEjDhUv4P-YpgaZoSNyUMy4bpdzZNlae050QYYTq07o'; // Your actual VAPID public key
    this.subscriptions = new Map();
    this.initializeService();
  }

  async initializeService() {
    if (!this.isSupported) {
      console.warn('🐺 Push notifications not supported in this browser');
      return;
    }

    console.log('🐺 Initializing Push Notification Service...');
    
    try {
      // Register service worker
      await this.registerServiceWorker();
      
      // Request permission if not already granted
      await this.requestPermission();
      
      // Subscribe user if logged in
      const user = authManager.getCurrentUser();
      if (user) {
        await this.subscribeUser(user.uid);
      }

      // Listen for auth state changes
      authManager.onAuthStateChanged(async (user) => {
        if (user) {
          await this.subscribeUser(user.uid);
        } else {
          await this.unsubscribeUser();
        }
      });

    } catch (error) {
      console.error('🐺 Error initializing push notifications:', error);
    }
  }

  async registerServiceWorker() {
    try {
      this.swRegistration = await navigator.serviceWorker.register('/sw.js');
      console.log('🐺 Service Worker registered successfully');
      
      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;
      
    } catch (error) {
      console.error('🐺 Service Worker registration failed:', error);
      throw error;
    }
  }

  async requestPermission() {
    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      console.log('🐺 Notification permission granted');
      return true;
    } else if (permission === 'denied') {
      console.warn('🐺 Notification permission denied');
      return false;
    } else {
      console.warn('🐺 Notification permission dismissed');
      return false;
    }
  }

  async subscribeUser(userId) {
    try {
      if (!this.swRegistration) {
        throw new Error('Service Worker not registered');
      }

      // Check if already subscribed
      let subscription = await this.swRegistration.pushManager.getSubscription();
      
      if (!subscription) {
        // Create new subscription
        subscription = await this.swRegistration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
        });
      }

      // Save subscription to Firestore
      await this.saveSubscription(userId, subscription);
      
      console.log('🐺 User subscribed to push notifications');
      return subscription;

    } catch (error) {
      console.error('🐺 Error subscribing to push notifications:', error);
      throw error;
    }
  }

  async saveSubscription(userId, subscription) {
    try {
      const subscriptionData = {
        userId: userId,
        endpoint: subscription.endpoint,
        keys: {
          p256dh: subscription.getKey('p256dh') ? 
            btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('p256dh')))) : null,
          auth: subscription.getKey('auth') ? 
            btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('auth')))) : null
        },
        createdAt: serverTimestamp(),
        isActive: true
      };

      const subscriptionRef = doc(collection(db, 'push_subscriptions'));
      await setDoc(subscriptionRef, subscriptionData);
      
      // Cache locally
      this.subscriptions.set(userId, subscriptionData);
      
      console.log('🐺 Push subscription saved to database');

    } catch (error) {
      console.error('🐺 Error saving subscription:', error);
      throw error;
    }
  }

  async unsubscribeUser() {
    try {
      if (!this.swRegistration) return;

      const subscription = await this.swRegistration.pushManager.getSubscription();
      if (subscription) {
        await subscription.unsubscribe();
        console.log('🐺 User unsubscribed from push notifications');
      }

    } catch (error) {
      console.error('🐺 Error unsubscribing:', error);
    }
  }

  // Admin function to send push notification to all users
  async sendNotificationToAll(title, message, data = {}) {
    if (!authManager.isAdmin()) {
      throw new Error('Only admins can send notifications to all users');
    }

    try {
      console.log('🐺 Sending push notification to all users...');

      // Get all active subscriptions
      const subscriptionsRef = collection(db, 'push_subscriptions');
      const subscriptionsQuery = query(subscriptionsRef, where('isActive', '==', true));
      const subscriptionsSnap = await getDocs(subscriptionsQuery);

      const notifications = [];
      const batch = writeBatch(db);

      subscriptionsSnap.docs.forEach(doc => {
        const subscription = doc.data();
        
        // Create notification record
        const notificationRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.NOTIFICATIONS));
        batch.set(notificationRef, {
          userId: subscription.userId,
          type: CTF_CONFIG.NOTIFICATION_TYPES.ADMIN_MESSAGE,
          title: title,
          message: message,
          data: data,
          timestamp: serverTimestamp(),
          read: false,
          sentVia: 'push'
        });

        notifications.push({
          subscription: subscription,
          payload: {
            title: title,
            body: message,
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
            data: {
              ...data,
              url: window.location.origin,
              timestamp: Date.now()
            },
            actions: [
              {
                action: 'view',
                title: 'View CTF Platform'
              },
              {
                action: 'dismiss',
                title: 'Dismiss'
              }
            ],
            requireInteraction: true,
            tag: 'admin-broadcast'
          }
        });
      });

      // Save notification records to database
      await batch.commit();

      // Send push notifications (this would typically be done server-side)
      // For demo purposes, we'll simulate the push notification
      await this.simulatePushNotifications(notifications);

      console.log(`🐺 Push notifications sent to ${notifications.length} users`);
      return { success: true, count: notifications.length };

    } catch (error) {
      console.error('🐺 Error sending push notifications:', error);
      throw error;
    }
  }

  // Admin function to send notification to specific user
  async sendNotificationToUser(userId, title, message, data = {}) {
    if (!authManager.isAdmin()) {
      throw new Error('Only admins can send targeted notifications');
    }

    try {
      // Get user's subscription
      const subscriptionsRef = collection(db, 'push_subscriptions');
      const userQuery = query(subscriptionsRef, 
        where('userId', '==', userId), 
        where('isActive', '==', true)
      );
      const userSnap = await getDocs(userQuery);

      if (userSnap.empty) {
        throw new Error('User not subscribed to push notifications');
      }

      const subscription = userSnap.docs[0].data();

      // Create notification record
      const notificationRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.NOTIFICATIONS));
      await setDoc(notificationRef, {
        userId: userId,
        type: CTF_CONFIG.NOTIFICATION_TYPES.ADMIN_MESSAGE,
        title: title,
        message: message,
        data: data,
        timestamp: serverTimestamp(),
        read: false,
        sentVia: 'push'
      });

      // Send push notification
      await this.simulatePushNotifications([{
        subscription: subscription,
        payload: {
          title: title,
          body: message,
          icon: '/icon-192x192.png',
          data: { ...data, url: window.location.origin }
        }
      }]);

      console.log('🐺 Push notification sent to user:', userId);
      return { success: true };

    } catch (error) {
      console.error('🐺 Error sending user notification:', error);
      throw error;
    }
  }

  // Simulate push notifications (in production, this would be server-side)
  async simulatePushNotifications(notifications) {
    for (const notification of notifications) {
      try {
        // In a real implementation, you would send this to your server
        // which would then use the Web Push Protocol to send the notification
        
        // For demo purposes, show browser notification if permission granted
        if (Notification.permission === 'granted') {
          new Notification(notification.payload.title, {
            body: notification.payload.body,
            icon: notification.payload.icon,
            tag: notification.payload.tag || 'ctf-notification',
            data: notification.payload.data
          });
        }

        // Simulate delay between notifications
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error('🐺 Error sending individual notification:', error);
      }
    }
  }

  // Utility function to convert VAPID key
  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Get subscription status
  async getSubscriptionStatus() {
    try {
      if (!this.swRegistration) return { subscribed: false, supported: false };

      const subscription = await this.swRegistration.pushManager.getSubscription();
      return {
        subscribed: !!subscription,
        supported: this.isSupported,
        permission: Notification.permission
      };

    } catch (error) {
      console.error('🐺 Error getting subscription status:', error);
      return { subscribed: false, supported: false, error: error.message };
    }
  }

  // Admin function to get all subscriptions
  async getAllSubscriptions() {
    if (!authManager.isAdmin()) {
      throw new Error('Only admins can view all subscriptions');
    }

    try {
      const subscriptionsRef = collection(db, 'push_subscriptions');
      const subscriptionsSnap = await getDocs(subscriptionsRef);
      
      return subscriptionsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

    } catch (error) {
      console.error('🐺 Error getting subscriptions:', error);
      throw error;
    }
  }

  // Test notification function
  async sendTestNotification() {
    try {
      if (Notification.permission === 'granted') {
        new Notification('🐺 The Wolf Challenge', {
          body: 'Push notifications are working correctly!',
          icon: '/icon-192x192.png',
          tag: 'test-notification'
        });
        return { success: true };
      } else {
        throw new Error('Notification permission not granted');
      }
    } catch (error) {
      console.error('🐺 Error sending test notification:', error);
      throw error;
    }
  }
}

// Initialize push notification service
const pushNotificationService = new PushNotificationService();

// Make it globally available
window.pushNotificationService = pushNotificationService;

export default pushNotificationService;
